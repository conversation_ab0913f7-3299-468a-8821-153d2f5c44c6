const people = [
    { name: '<PERSON>', age: 25, sex: 'female' },
    { name: '<PERSON>', age: 30, sex: 'female' },
    { name: '<PERSON>', age: 22, sex: 'female' },
    { name: '<PERSON>', age: 35, sex: 'male' },
    { name: '<PERSON>', age: 28, sex: 'female' },
    { name: 'Alice<PERSON>', age: 25, sex: 'male' },
  ]
  
  // 使用 Array.prototype.reduce 按年龄分组
  const groupByAge = people.reduce((acc, person) => {
    const key = person.age % 2 === 0 ? 'even' : 'odd';
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(person.age); // 只存储年龄值
    return acc;
  }, {});
  
  console.log('按奇偶年龄分组结果:', groupByAge);
  // 按年龄分组
  // const result = []
  // for (const person of people) {
  //   const key = person.age
  //   if (!result[key]) {
  //     result[key] = []
  //   }
  //   result[key].push(person)
  // }
  
  // console.log('result:', result)
  
  // 按性别分组
  // const result = {}
  // for (const person of people) {
  //   const key = person.sex
  //   if (!result[key]) {
  //     result[key] = []
  //   }
  //   result[key].push(person)
  // }
  
  // console.log('result:', result)
  
  // 按属性名来分组 generrateKey为一个函数获取key
  function groupBy (arr, generrateKey) {
    const result = []
    for (const person of arr) {
      const key = generrateKey(person)
      if (!result[key]) {
        result[key] = []
      }
      result[key].push(person)
    }
    return result
  }
  // const result = groupBy(people, item => item.sex)
  // console.log('result:', result)
  
  // const result1 = groupBy(people, item => item.age)
  // console.log('result1:', result1)
  
  // const result2 = groupBy(people, item => `${item.sex}-${item.age}`)
  // console.log('result2:', result2)
  
//   const arr = [3, 7, 1, 4, 8, 2]
//   const result4 = groupBy(arr, item => item % 2 === 0 ? 'even' : 'odd')
//   console.log('result4:', result4)
  