import request from '@/utils/request';
const webkk = process.env.BASE_API_FED;
export function fetchList(params) {
  return request({
    url: '/pickColor/order/list',
    method: 'get',
    params: params
  });
}

export function sellerList(params) {
  return request({
    url: `/pickColor/seller/list`,
    method: 'get',
    params
  });
}

export function uploadFile(data) {
  return request({
    url: `/pickColor/uploadFile`,
    method: 'post',
    data
  });
}

export function totalPayAmount(params) {
  return request({
    url: `/pickColor/totalPayAmount`,
    method: 'get',
    params
  });
}
