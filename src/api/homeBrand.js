import request from '@/utils/request';
export function fetchList(params) {
  return request({
    url: '/home/<USER>/list',
    method: 'get',
    params: params
  });
}

export function updateRecommendStatus(data) {
  return request({
    url: '/home/<USER>/update/recommendStatus',
    method: 'post',
    data: data
  });
}

export function deleteHomeBrand(data) {
  return request({
    url: '/home/<USER>/delete',
    method: 'post',
    data: data
  });
}

export function createHomeBrand(data) {
  return request({
    url: '/home/<USER>/create',
    method: 'post',
    data: data
  });
}

export function updateHomeBrandSort(params) {
  return request({
    url: '/home/<USER>/update/sort/' + params.id,
    method: 'post',
    params: params
  });
}
