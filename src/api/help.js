import request from '@/utils/request';
import util from '@/utils/index';

export function getHelpDetail(id) {
  return request({
    url: '/product/updateInfo/' + id,
    method: 'get'
  });
}

export function fetchList(params) {
  return request({
    url: '/product/list',
    method: 'get',
    params: params
  });
}

export function getHelpDetailKF(id) {
  return request({
    url: '/productKF/updateInfo/' + id,
    method: 'get'
  });
}

export function fetchListKF(params) {
  return request({
    url: '/productKF/list',
    method: 'get',
    params: params
  });
}

export function getHelpDetailZB(id) {
  return request({
    url: '/productZB/updateInfo/' + id,
    method: 'get'
  });
}

export function fetchListZB(params) {
  return request({
    url: '/productZB/list',
    method: 'get',
    params: params
  });
}

export function fetchListDoc(params) {
  return request({
    url: '/productDoc/list',
    method: 'get',
    params: params
  });
}

export function createProductDoc(data) {
  return request({
    url: '/productDoc/create',
    method: 'post',
    data
  });
}

export function updateProductDoc(id, data) {
  return request({
    url: '/productDoc/update/' + id,
    method: 'post',
    data
  });
}

export function deleteDoc(params) {
  params.id = params.ids;
  delete params.ids;
  return request({
    url: '/productDoc/update/deleteStatus',
    method: 'post',
    params
  });
}

export function getHelpDetailDoc(id) {
  return request({
    url: '/productDoc/updateInfo/' + id,
    method: 'get'
  });
}
