import request from '@/utils/request';
const webkk = process.env.BASE_API_FED;
const webkkms=process.env.BASE_API_SE
const webkkSearch =process.env.BASE_API_SEARCH
export function getBuyerList(params) {
  return request({
    url: webkk + `/negotia/BuyerList`,
    method: 'get',
    params
  });
}
export function negotiaCancel(params) {
  return request({
    url: webkk + '/negotia/cancel',
    method: 'post',
    params
  });
}
export function negotiaDelete(id) {
  return request({
    url: webkk + `/negotia/delete/${id}`,
    method: 'post'
  });
}

export function getSellerList(params) {
  return request({
    url: webkk + '/negotia/SellerList',
    method: 'get',
    params
  });
}
export function sellerDo(params) {
  return request({
    url: webkk + '/negotia/sellerDo',
    method: 'post',
    params
  });
}
export function myOrderList(params) {
  return request({
    url: webkk + '/order/list',
    method: 'get',
    params
  });
}
export function cancelUserOrder(data, params) {
  return request({
    url: webkk + '/order/cancelUserOrder',
    method: 'post',
    data,
    params
  });
}
export function deleteOrder(orderId) {
  return request({
    url: webkk + `/order/deleteOrder/${orderId}`,
    method: 'post'
  });
}
export function mySellerList(params) {
    return request({
      url: webkk +'/order/sellerList',
      method: 'get',
      params,
    });
  }

  export function getProductList(params) {
    return request({
      url:webkk + '/member/product/list',
      method: 'get',
      params,
    });
  }
  export function deleteProduct(id) {
    return request({
      url: webkk +`/member/product/delete/${id}`,
      method: 'get',
    });
  }
  export function publishDown(params) {
    return request({
      url:  webkk +`/member/product/update/publishDown`,
      method: 'get',
      params,
    });
  }
  export function getGameList(params) {
    return request({
      url: webkk +'/home/<USER>',
      method: 'get',
      params,
    });
  }
  export function offerPrice(params) {
    return request({
      url: webkk+'/negotia/buyerOfferPrice',
      method: 'post',
      params,
    });
  }
  export function sellerOfferPrice(params) {
    return request({
      url:webkk+ '/negotia/sellerOfferPrice',
      method: 'post',
      params,
    });
  }
  
  export function updatePrice(id, params) {
    return request({
      url: webkk+ `/member/product/updatePrice/${id}`,
      method: 'post',
      params,
    });
  }
  
  export function sendSm(data) {
    return request({
      url:  webkk+ '/member/product/smsCode',
      method: 'post',
      data,
    });
  }
  
  export function startLuhao(data) {
    return request({
      url:  webkk+ '/member/product/startLuhao',
      method: 'post',
      data,
    });
  }
  
  export function startLuhao2(data) {
    return request({
      url: webkk+  '/record/task/start',
      method: 'post',
      data,
    });
  }

export function stsTokenApi() {
    return request({
      url: webkk+ '/member/oss/policy',
      method: 'get',
    });
  }
  

  export function getWxQrcode() {
    return request({
      url: webkk+ '/member/get_wx_qrcode',
      method: 'get',
    });
  }

  export function recordTaskDetail(id) {
    return request({
      url: webkk+ `/record/task/${id}`,
      method: 'get'
    });
  }
  export function taskUpdate(data) {
    return request({
      url: webkk+ `/record/task/update`,
      method: 'post',
      data
    });
  }
  
  export function recordTaskSmsCode(data) {
    return request({
      url: webkk+ `/record/task/smsCode`,
      method: 'post',
      data
    });
  }

  export function getProductAttribute(skuId) {
    return request({
      url: webkk+ '/home/<USER>/listall/' + skuId,
      method: 'get',
    });
  }
  
  export function productCreate(data) {
    return request({
      url: webkk+ '/member/product/create',
      method: 'post',
      data,
    });
  }
  
  export function productUpdate(id, data) {
    return request({
      url: webkk+ '/member/product/update/' + id,
      method: 'post',
      data,
    });
  }
  
  export function productUpdate2(id, data) {
    return request({
      url: webkk+ '/member/product/updateQuestion/' + id,
      method: 'post',
      data,
    });
  }
  export function getUpdateInfo(id) {
    return request({
      url: webkk+ '/member/product/updateInfo/' + id,
      method: 'get',
    });
  }
  export function getProductCategoryList(id) {
    return request({
      url: webkk+ '/home/<USER>/list/' + id,
      method: 'get',
    });
  }
  
  export function getProductCategory(id) {
    return request({
      url: webkk+ '/home/<USER>/' + id,
      method: 'get',
    });
  }
  export function getShopDetail(params) {
    return request({
      url: '/shop/detail',
      method: 'get',
      params
    });
  }
  export function shopUpdate(id,data) {
    return request({
      url: '/shop/update/'+ id,
      method: 'post',
      data,
    });
  }
  export function getOrderDetail(id) {
    return request({
      url: webkk+ '/order/detail/' + id,
      method: 'get',
    });
  }
  export function getOrderTeam(params) {
    return request({
      url: webkk+'/order/orderTeam',
      method: 'get',
      params,
    });
  }
  
  export function pay(params) {
    return request({
      url: webkk+'/kkpay/pay',
      method: 'get',
      params,
    });
  }
  export function payCheck(params) {
    return request({
      url: webkk+'/order/payCheck',
      method: 'get',
      params,
    });
  }
  export function payCheck3(params) {
    return request({
      url: webkk+'/order/payCheck3',
      method: 'get',
      params,
    });
  }

export function pay3(params) {
  return request({
    url: webkk+'/kkpay/pay3',
    method: 'get',
    params,
  });
}
export function payConfig(orderId) {
  return request({
    url:webkk+`/member/payConfig`,
    method: 'get',
  });
}

export function generateKKOrder2(params) {
  return request({
    url: webkk+'/negotia/generateKKOrder2',
    method: 'get',
    params,
  });
}


export function fetchList(params) {
  return request({
    url: '/shopProductCategory/list',
    method: 'get',
    params
  });
}
export function deleteProductCate(id) {
  return request({
    url: '/shopProductCategory/delete/' + id,
    method: 'post'
  });
}
export function updateShowStatus(data) {
  return request({
    url: '/productCategory/update/showStatus',
    method: 'post',
    data: data
  });
}

export function productListAll(params) {
  return request({
    url: '/shopProductCategory/listAll',
    method: 'get',
    params
  });
}
export function shopProductCategoryAdd(data) {
  return request({
    url: '/shopProductCategory/add',
    method: 'post',
    data: data
  });
}

export function shopProductCategoryUpdate(id,data) {
  return request({
    url: `/shopProductCategory/update/${id}`,
    method: 'post',
    data: data
  });
}
export function publishUp(params) {
  return request({
    url:webkk+ `/member/product/update/publishUp`,
    method: 'get',
    params
  });
}

export function getIdlefishList(params) {
  return request({
    url:`/thirdShop/list`,
    method: 'get',
    params
  });
}
export function thirdShopDetail(params) {
  return request({
    url:`/thirdShop/detail/${params}`,
    method: 'get',
  });
}
export function deleteIdlefish(id) {
  return request({
    url: '/thirdShop/delete/' + id,
    method: 'get'
  });
}
export function updateIdlefish(data) {
  return request({
    url: '/thirdShop/update',
    method: 'post',
    data
  });
}
export function createIdlefish(data) {
  return request({
    url: '/thirdShop/create',
    method: 'post',
    data
  });
}
//发布商品到第三方
export function publishGoods(data) {
  return request({
    url: '/thirdShopProduct/publish',
    method: 'post',
    data
  });
}
//下架
export function downShelfGoods(data) {
  return request({
    url: '/thirdShopProduct/downShelf',
    method: 'post',
    data
  });
}
// 删除
export function deleteGoods(data) {
  return request({
    url: '/thirdShopProduct/delete',
    method: 'post',
    data
  });
}
// 更新
export function updateGoods(data) {
  return request({
    url: '/thirdShopProduct/update/',
    method: 'post',
    data
  });
}
// 列表
export function getIdlefishGoodsList(data) {
  return request({
    url:`/thirdShopProduct/list`,
    method: 'post',
    data
  });
}

//添加
export function createAndPublish(data) {
  return request({
    url:`/thirdShopProduct/create`,
    method: 'post',
    data
  });
}

export function getProductDetail(params) {
  return request({
    url:webkk+`/product/detail`,
    method: 'get',
    params
  });
}
export function memberConsultList(params) {
  return request({
    url:`/consultRecords/memberConsultList`,
    method: 'post',
    params
  });
}

export function loadByIM(params) {
  return request({
    url: '/memberManage/loadByIM',
    method: 'get',
    params
  });
}



export function kkProductCategoryList(params) {
  return request({
    url: '/kkProductCategory/list',
    method: 'get',
    params
  });
}
export function deleteKkProductCategory(id) {
  return request({
    url: '/kkProductCategory/delete/' + id,
    method: 'post'
  });
}

export function kkProductCategoryAdd(data) {
  return request({
    url: '/kkProductCategory/add',
    method: 'post',
    data: data
  });
}

export function kkProductCategoryUpdate(id,data) {
  return request({
    url: `/kkProductCategory/update/${id}`,
    method: 'post',
    data: data
  });
}

export function getCertDetail() {
  return request({
    url:  webkk+'/member/cert/detail',
    method: 'get',
  });
}

export function certAdd(data) {
  return request({
    url:  webkk+'/member/cert/add',
    method: 'post',
    data,
  });
}



export function productLeadList(data) {
  return request({
    url: '/productLead/list',
    method: 'post',
    data
  });
}
export function productLeadCreate(data) {
  return request({
    url: '/productLead/create',
    method: 'post',
    data
  });
}
export function productLeadDelete(data) {
  return request({
    url: '/productLead/delete',
    method: 'post',
    data
  });
}
export function productLeadUpdate(data) {
  return request({
    url: '/productLead/update',
    method: 'post',
    data
  });
}

export function thirdShopCommand(data) {
  return request({
    url: '/thirdShop/im/command',
    method: 'post',
    data
  });
}
export function updateShopShowStatus(id,data) {
  return request({
    url: `/shopProduct/update/shopShowStatus/${id}`,
    method: 'post',
    data
  });
}




export function getProductAttributeAttrInfo(id) {
  return request({
      url: webkk + '/home/<USER>/attrInfo/' + id,
      method: 'get',
  });
}

export function getSearchTagList(id,params) {
  return request({
      url: '/searchTag/list/' + id,
      method: 'get',
      params
  });
}

export function addSearchTag(data) {
  return request({
      url: '/searchTag/create',
      method: 'post',
      data
  });
}

export function updateSearchTag(id,data) {
  return request({
      url: '/searchTag/update/' + id,
      method: 'post',
      data
  });
}
export function deleteSearchTag(id) {
  return request({
      url: '/searchTag/delete/' + id,
      method: 'get',
  });
}
export function detailSearchTag(id) {
  return request({
      url: '/searchTag/detail/' + id,
      method: 'get',
  });
}

export function searchProductList2(params, data) {
  return request({
    url: webkkSearch+'/kkSearch/innerSearch',
    method: 'post',
    data,
    params,
  });
}

export function getKfList(data) {
  return request({
    url: webkk+'/home/<USER>/list',
    method: 'post',
    data,
  });
}

export function getMemberHisKFList(params) {
  return request({
    url: webkk+'/kkim/m2kf/getMemberHisKFList',
    method: 'get',
    params,
  });
}


export function m2kfTalk(params) {
  return request({
    url: webkk+'/kkim/m2kf/talk',
    method: 'get',
    params,
  });
}

export function readHistoryCreate(data, params) {
  return request({
    url: webkk+'/member/readHistory/create',
    method: 'post',
    data,
    params,
  });
}

export function getDetailByCode(params) {
  return request({
    url: webkk+'/product/detail',
    method: 'get',
    params,
  });
}
