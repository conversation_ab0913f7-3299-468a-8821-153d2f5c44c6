import request from '@/utils/request';
import util from '@/utils/index';
export function fetchList(params, url) {
  return request({
    url: url || '/product/list',
    method: 'get',
    params: params
  });
}

export function fetchSimpleList(params) {
  return request({
    url: '/product/simpleList',
    method: 'get',
    params: params
  });
}

export function updateDeleteStatus(params) {
  params = util.ids2id(params);
  return request({
    url: '/product/update/deleteStatus',
    method: 'post',
    params: params
  });
}

export function updateNewStatus(params) {
  params = util.ids2id(params);
  return request({
    url: '/product/update/newStatus',
    method: 'post',
    params: params
  });
}

export function updateRecommendStatus(params) {
  params = util.ids2id(params);
  return request({
    url: '/product/update/recommendStatus',
    method: 'post',
    params: params
  });
}

export function updatePublishStatus(params) {
  params = util.ids2id(params);
  return request({
    url: '/product/update/publishStatus',
    method: 'post',
    params: params
  });
}

export function createProduct(data) {
  return request({
    url: '/product/create',
    method: 'post',
    data: data
  });
}

export function updateProduct(id, data) {
  return request({
    url: '/product/update/' + id,
    method: 'post',
    data: data
  });
}

export function getProduct(id) {
  return request({
    url: '/product/updateInfo/' + id,
    method: 'get'
  });
}

export function doVerifyStatus(id, data) {
  return request({
    url: '/product/updateVerify/' + id,
    method: 'post',
    data
  });
}

export function productKFCreate(data) {
  return request({
    url: '/productKF/create',
    method: 'post',
    data
  });
}

export function productKFUpdate(id, data) {
  return request({
    url: '/productKF/update/' + id,
    method: 'post',
    data
  });
}

export function productKFDelete(params) {
  params = util.ids2id(params);
  return request({
    url: '/productKF/update/deleteStatus',
    method: 'post',
    params: params
  });
}

export function productZBCreate(data) {
  return request({
    url: '/productZB/create',
    method: 'post',
    data
  });
}

export function productZBUpdate(id, data) {
  return request({
    url: '/productZB/update/' + id,
    method: 'post',
    data
  });
}

export function productZBDelete(params) {
  params = util.ids2id(params);
  return request({
    url: '/productZB/update/deleteStatus',
    method: 'post',
    params
  });
}

export function getBlackUserList(params) {
  return request({
    url: '/blackUser/list',
    method: 'get',
    params
  });
}

export function blackUserCreate(data) {
  return request({
    url: '/blackUser/create',
    method: 'post',
    data
  });
}

export function blackUserUpdate(id, data) {
  return request({
    url: '/blackUser/update/' + id,
    method: 'post',
    data
  });
}

export function getBlackUser(id) {
  return request({
    url: '/blackUser/' + id,
    method: 'get'
  });
}

export function productSHCreate(data) {
  return request({
    url: '/merchantAccount/create',
    method: 'post',
    data
  });
}

export function getProductSHList(params) {
  return request({
    url: '/merchantAccount/list',
    method: 'get',
    params
  });
}

export function productHKCreate(data) {
  return request({
    url: '/kkTransactions/create',
    method: 'post',
    data
  });
}

export function getProductHKList(params) {
  return request({
    url: '/kkTransactions/list',
    method: 'get',
    params
  });
}

export function verifyLog(productId) {
  return request({
    url: '/product/verifyLog/' + productId,
    method: 'get'
  });
}

export function lastlog(productId) {
  return request({
    url: '/product/lastlog/' + productId,
    method: 'get'
  });
}

export function queryBankCode() {
  return request({
    url: '/kkTransactions/queryBankCode',
    method: 'get'
  });
}

export function queryCityCode() {
  return request({
    url: '/kkTransactions/queryCityCode',
    method: 'get'
  });
}

export function queryCnapscode(params) {
  return request({
    url: '/kkTransactions/queryCnapscode',
    method: 'get',
    params
  });
}

export function kkTransactionsCreateByOrder(data) {
  return request({
    url: '/kkTransactions/createByOrder',
    method: 'post',
    data
  });
}

export function createKKTransactions(data) {
  return request({
    url: '/kkTransactions/create',
    method: 'post',
    data
  });
}

// export function kkTransactionsVerify(id, params) {
//   return request({
//     url: `/kkTransactions/verify/${id}`,
//     method: 'get',
//     params
//   });
// }

export function kkTransactionsUpdateInfo(id, params) {
  return request({
    url: `/kkTransactions/updateInfo/${id}`,
    method: 'get',
    params
  });
}

export function kkTransactionsUpdateStatus(id, data) {
  return request({
    url: `/kkTransactions/update/status/${id}`,
    method: 'post',
    data
  });
}
