import request from '@/utils/request';
const webkk = process.env.BASE_API_FED;
export function getIminfo() {
  return request({
    url: '/admin/iminfo',
    method: 'get'
  });
}

export function productAttributeListall() {
  return request({
    url:webkk + '/home/<USER>/listall/17',
    method:'get',
  })
}
export function taskCreate(data, params) {
  return request({
    url: webkk + '/record/task/create',
    data,
    params,
    method:'post',
  })
}

export function recordTaskDetail(id) {
  return request({
    url: webkk + `/record/full_task/${id}`,
    method: 'get'
  });
}

export function addNote(data, params) {
  return request({
    url: webkk + '/order/flow/addNote',
    method: 'post',
    data,
    params,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

export function addServerNote(data, params) {
  return request({
    url: webkk + '/order/flow/addServerNote',
    method: 'post',
    data,
    params,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

export function getStatics(params) {
  return request({
    url: webkk + '/order/flow/flowStatics',
    method: 'get',
    params: params
  });
}

export function getTools(params) {
  return request({
    url: webkk + '/order/flowTools/list',
    method: 'get',
    params: params,
    noLoading: 1
  });
}

export function consultRecordsCreate(data) {
  return request({
    url: '/consultRecords/create',
    method: 'post',
    data
  });
}

export function productConsultList(params) {
  return request({
    url: '/consultRecords/productConsultList',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

export function orderQueryBySn(params) {
  return request({
    url: '/order/queryBySn',
    method: 'get',
    params: params
  });
}

export function returnApplyCreate(data) {
  return request({
    url: '/returnApply/create',
    method: 'post',
    data
  });
}

export function actionTimeClockSet(params) {
  return request({
    url: webkk + '/order/flow/actionTimeClockSet',
    method: 'post',
    params
  });
}

export function createSellerFormURL(params) {
  return request({
    url: '/order/createSellerFormURL',
    method: 'get',
    params: params
  });
}

// 获取动态码
export function generateOrderPassword(data) {
  return request({
    url: webkk + '/order/generateOrderPassword',
    method: 'post',
    data
  });
}

export function generateKKOrderByKF(data) {
  return request({
    url: webkk + '/order/generateKKOrderByKF',
    method: 'post',
    data
  });
}

export function generateKKOrderByKF2(data) {
  return request({
    url: webkk + '/order/generateKKOrderByKF2',
    method: 'post',
    data
  });
}

export function flowStepDetail(params) {
  return request({
    url: webkk + '/order/flow/flowStepDetail',
    method: 'get',
    params,
    noLoading: 1
  });
}

export function soundCall(params) {
  return request({
    url: webkk + '/order/flowTools/soundCall',
    method: 'get',
    params
  });
}

export function kferCallBar(params) {
  return request({
    url: webkk + '/order/flowTools/kferCallBar',
    method: 'get',
    params
  });
}
export function wxMsgCall(params) {
  return request({
    url: webkk + '/order/flowTools/wxMsgCall',
    method: 'get',
    params
  });
}


export function listMemberReadHis(params) {
  return request({
    url: webkk + '/order/flowTools/listMemberReadHis',
    method: 'get',
    params
  });
}

export function getProductCategory(id) {
  return request({
    url: webkk + '/home/<USER>/' + id,
    method: 'get'
  });
}

export function loadByIM(params) {
  return request({
    url: '/memberManage/loadByIM',
    method: 'get',
    params
  });
}

export function memberManageAddress(id) {
  return request({
    url: `/memberManage/address/byMemberId/${id}`,
    method: 'get'
  });
}

export function getMemberByUsername(params) {
  return request({
    url: `/memberManage/getMemberByUsername`,
    method: 'get',
    params
  });
}

export function memberConsultList(params) {
  return request({
    url: `/consultRecords/memberConsultList`,
    method: 'post',
    params
  });
}

export function addIMTeamKFer(data) {
  return request({
    url: webkk + `/order/flowTools/addIMTeamKFer`,
    method: 'post',
    data
  });
}

export function listNegotiations(params) {
  return request({
    url: webkk + `/order/flowTools/listNegotiations`,
    method: 'get',
    params
  });
}

export function createTeamByKF(params) {
  return request({
    url: webkk + '/kkim/team/createTeamByKF',
    method: 'post',
    params
  });
}

export function getImaccount(params) {
  return request({
    url: `/memberManage/getImaccount`,
    method: 'get',
    params
  });
}

export function getImaccount2(params) {
  return request({
    url: webkk + `/order/flowTools/getImaccount`,
    method: 'get',
    params
  });
}

export function sendBaopeiMsg2Team(params) {
  return request({
    url: webkk + `/order/flow/sendBaopeiMsg2Team`,
    method: 'get',
    params
  });
}

export function changeIMTeamName(data) {
  return request({
    url: webkk + `/order/flowTools/changeIMTeamName`,
    method: 'post',
    data
  });
}

export function getIMTeamListByUsername(params) {
  return request({
    url: webkk + `/order/flowTools/getIMTeamListByUsername`,
    method: 'get',
    params
  });
}

export function teamConsultList(params) {
  return request({
    url: `/consultRecords/teamConsultList`,
    method: 'post',
    params
  });
}

export function setTeamQuiet(data) {
  return request({
    url: webkk + `/order/flowTools/setTeamQuiet`,
    method: 'post',
    data
  });
}

export function sendYijiaCard(data) {
  return request({
    url: webkk + `/order/flowTools/sendYijiaCard`,
    method: 'post',
    data
  });
}

export function getMemberInfo(params) {
  return request({
    url: `/memberManage/getMemberInfo`,
    method: 'get',
    params
  });
}

export function getMemberInfo2(params) {
  return request({
    url: webkk + `/order/flowTools/getMemberInfo`,
    method: 'get',
    params
  });
}

export function sendKFerCard(data) {
  return request({
    url: webkk + `/order/flowTools/sendKFerCard`,
    method: 'post',
    data
  });
}

export function getSessionList(params) {
  return request({
    url: `/chatMessages/sessionList`,
    method: 'get',
    params
  });
}

export function getMsgList(params) {
  return request({
    url: `/chatMessages/msgList`,
    method: 'get',
    params
  });
}

export function bindUser(data) {
  return request({
    url: `/product/bindUser`,
    method: 'post',
    data
  });
}

export function reportList(params) {
  return request({
    url: `/member/report/list`,
    method: 'get',
    params
  });
}

export function flowTools(params) {
  return request({
    url: webkk + `/person/flowTools/list`,
    method: 'get',
    params
  });
}

export function createLuhao(data) {
  return request({
    url: `/product/createLuhao`,
    method: 'post',
    data
  });
}

export function verifyStats(params) {
  return request({
    url: `/product/verify/stats`,
    method: 'get',
    params
  });
}

export function personChatClick(params) {
  return request({
    url: webkk + `/person/flowTools/personChatClick`,
    method: 'get',
    params
  });
}

export function updateCheckFlag(data) {
  return request({
    url: `/chatMessages/updateCheckFlag`,
    method: 'post',
    data
  });
}

export function personChatAddNote(params) {
  return request({
    url: webkk + `/person/flowTools/personChatAddNote`,
    method: 'get',
    params
  });
}

export function memberSendProductList(params) {
  return request({
    url: webkk + `/person/flowTools/memberSendProductList`,
    method: 'get',
    params
  });
}

export function contractPreview(params) {
  return request({
    url: webkk + `/contract/preview`,
    method: 'get',
    params
  });
}

export function contractCreate(data) {
  return request({
    url: webkk + `/contract/create`,
    method: 'post',
    data
  });
}

export function startSignTask(data) {
  return request({
    url: webkk + `/contract/startSignTask`,
    method: 'post',
    data
  });
}

export function contractDetail(id) {
  return request({
    url: webkk + `/contract/detail/${id}`,
    method: 'get'
  });
}

export function contractList(params) {
  return request({
    url: `/contract/list`,
    method: 'get',
    params
  });
}

export function detailBySn(params) {
  return request({
    url: webkk + '/product/detailBySn',
    method: 'get',
    params
  });
}

export function changeStatus(status) {
  return request({
    url: webkk + `/order/flowTools/kf/changeStatus/${status}`,
    method: 'post'
  });
}
export function getUserInfo(params) {
  return request({
    url: webkk + '/order/flowTools/kf/userInfo1',
    method: 'get',
    params
  });
}

  

export function getDetail(id) {
  return request({
    url: webkk+'/product/detail/' + id,
    method: 'get',
  });
}
export function negotiaSellerNegoSet(params) {
  return request({
    url: webkk+'/negotia/sellerNegoSet',
    method: 'get',
    params,
  });
}