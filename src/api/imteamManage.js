import request from '@/utils/request';
const webkk = process.env.BASE_API_FED;

export function getImteamManageList(params) {
  return request({
    url: '/imteamManage/list',
    method: 'get',
    params: params
  });
}

export function imteamManageDelete(id) {
  return request({
    url: '/imteamManage/delete/' + id,
    method: 'get'
  });
}

export function imteamManageUpdate(id, data) {
  return request({
    url: '/imteamManage/update/' + id,
    method: 'post',
    data
  });
}

export function getMemberTalkManageList(params) {
  return request({
    url: '/memberTalkManage/list',
    method: 'get',
    params
  });
}

export function memberTalkManageDelete(id) {
  return request({
    url: '/memberTalkManage/delete/' + id,
    method: 'get'
  });
}

export function getCsonsultRecordsList(params) {
  return request({
    url: '/consultRecords/list',
    method: 'get',
    params
  });
}

export function consultRecordsDelete(id) {
  return request({
    url: '/consultRecords/delete/' + id,
    method: 'get'
  });
}

export function getMemberTalkManageStatics(id) {
  return request({
    url: '/memberTalkManage/statics',
    method: 'get'
  });
}

export function inTeam(id) {
  return request({
    url: webkk + `/order/flowTools/inTeam?teamId=${id}`,
    method: 'get'
  });
}

export function kferStatics() {
  return request({
    url: `/imteamManage/kferStatics`,
    method: 'get'
  });
}

export function kferPersonStatics() {
  return request({
    url: `/imteamManage/kferPersonStatics`,
    method: 'get'
  });
}

export function personChatStats(params) {
  return request({
    url: webkk + `/person/flowTools/personChatStats`,
    method: 'get',
    params
  });
}

export function personChatTodoList(params) {
  return request({
    url: webkk + `/person/flowTools/personChatTodoList`,
    method: 'get',
    params
  });
}

export function personChatDoneList(params) {
  return request({
    url: webkk + `/person/flowTools/personChatDoneList`,
    method: 'get',
    params
  });
}
