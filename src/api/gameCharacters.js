import request from '@/utils/request';

export function fetchList(params) {
  return request({
    url: '/gameCharacters/list',
    method: 'get',
    params
  });
}

export function gameCharactersCreate(data) {
  return request({
    url: '/gameCharacters/create',
    method: 'post',
    data
  });
}

export function gameCharactersUpdateInfo(id) {
  return request({
    url: '/gameCharacters/updateInfo/' + id,
    method: 'get'
  });
}

export function gameCharactersUpdate(id, data) {
  return request({
    url: '/gameCharacters/update/' + id,
    method: 'post',
    data
  });
}
