import request from '@/utils/request';
export function fetchDetail(id) {
  return request({
    url: '/memberManage/address/' + id,
    method: 'get'
  });
}

// /mall-bmc/memberManage/address/listAll?pageNum=1&pageSize=20&baopeiStatus=0   baopeiStatus :  0待审核；1未通过；2通过
export function fetchListAll(params) {
  return request({
    url: '/memberManage/address/listAll',
    method: 'get',
    params: params
  });
}

export function addressAdd(data) {
  return request({
    url: '/memberManage/address/add',
    method: 'post',
    data
  });
}

export function addressUpStatus(id, params) {
  return request({
    url: '/memberManage/address/upStatus/' + id,
    method: 'post',
    params
  });
}

export function addressUpdate(id, data) {
  return request({
    url: '/memberManage/address/update/' + id,
    method: 'post',
    data
  });
}
