import request from '@/utils/request'
export function getCollectionAccountsList(params) {
  return request({
    url: '/collectionAccounts/list',
    method: 'get',
    params
  })
}

export function collectionAccountsCreate(data) {
  return request({
    url: '/collectionAccounts/create',
    method: 'post',
    data
  })
}

export function collectionAccountsUpdate(id, data) {
  return request({
    url: '/collectionAccounts/update/' + id,
    method: 'post',
    data
  })
}

export function collectionAccountsInfo(id) {
  return request({
    url: '/collectionAccounts/updateInfo/' + id,
    method: 'get'
  })
}

