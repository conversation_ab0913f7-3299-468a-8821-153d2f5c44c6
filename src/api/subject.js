import request from '@/utils/request';
export function fetchListAll() {
  return request({
    url: '/subject/listAll',
    method: 'get'
  });
}

export function fetchList(params) {
  return request({
    url: '/subject/list',
    method: 'get',
    params: params
  });
}

export function getListProduct(params) {
  return request({
    url: '/home/<USER>/listProduct',
    method: 'get',
    params: params
  });
}

export function deleteProduct(params) {
  return request({
    url: '/home/<USER>/deleteProduct',
    method: 'get',
    params: params
  });
}

export function addProduct(data, params) {
  return request({
    url: '/home/<USER>/addProducts',
    method: 'post',
    data,
    params
  });
}
