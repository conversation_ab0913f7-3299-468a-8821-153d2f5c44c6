import request from '@/utils/request';
const webkk = process.env.BASE_API_FED;

export function deviceList(params) {
  return request({
    url: '/record/device/list',
    method: 'get',
    params
  });
}

export function deviceAdd(data) {
  return request({
    url: '/record/device/add',
    method: 'post',
    data
  });
}

export function deviceStop(id) {
  return request({
    url: `/record/device/stop/${id}`,
    method: 'post'
  });
}

export function deviceRestart(id) {
  return request({
    url: `/record/device/restart/${id}`,
    method: 'post'
  });
}

export function deviceDelete(id) {
  return request({
    url: `/record/device/delete/${id}`,
    method: 'post'
  });
}

export function createLuhaoV2(data) {
  return request({
    url: `/product/createLuhaoV2`,
    method: 'post',
    data
  });
}

export function recordTaskDetail(id) {
  return request({
    url: webkk + `/record/full_task/${id}`,
    method: 'get'
  });
}

export function taskUpdate(data) {
  return request({
    url: webkk + `/record/task/update`,
    method: 'post',
    data
  });
}


export function taskCancel(data) {
  return request({
    url: webkk + `/record/task/cancel`,
    method: 'post',
    data
  });
}

export function recordTaskSmsCode(data) {
  return request({
    url: `/product/recordTask/smsCode`,
    method: 'post',
    data
  });
}

export function taskList(params) {
  return request({
    url: `/record/task/list`,
    method: 'get',
    params
  });
}

// 重新发起
export function resumeTask(data) {
  return request({
    url: `/record/resumeTask`,
    method: 'post',
    data
  });
}

// 任务阶段列表
export function getStageList(id) {
  return request({
    url: `/record/task/stageList?taskId=${id}`,
    method: 'get'
  });
}

// 任务统计
export function getStats() {
  return request({
    url: `/record/task/stats`,
    method: 'get'
  });
}
