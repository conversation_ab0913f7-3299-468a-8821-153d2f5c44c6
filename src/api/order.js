import request from '@/utils/request';
const webkk = process.env.BASE_API_FED;
export function fetchList(params) {
  return request({
    url: '/order/list',
    method: 'get',
    params: params
  });
}

export function closeOrder(params) {
  return request({
    url: '/order/update/close',
    method: 'post',
    params: params
  });
}

export function deleteOrder(params) {
  return request({
    url: '/order/delete',
    method: 'post',
    params: params
  });
}

export function deliveryOrder(data) {
  return request({
    url: '/order/update/delivery',
    method: 'post',
    data: data
  });
}

export function getOrderDetail(id) {
  return request({
    url: '/order/' + id,
    method: 'get',
    noLoading: 1
  });
}

export function getFlowState(teamId) {
  return request({
    url: webkk + '/order/flow/detail',
    method: 'get',
    params: {
      teamId
    },
    noLoading: 1
  });
}

export function updateReceiverInfo(data) {
  return request({
    url: '/order/update/receiverInfo',
    method: 'post',
    data: data
  });
}

export function updateMoneyInfo(data) {
  return request({
    url: '/order/update/moneyInfo',
    method: 'post',
    data: data
  });
}

export function updateOrderNote(params) {
  return request({
    url: '/order/update/note',
    method: 'post',
    params: params
  });
}

export function orderList(params) {
  return request({
    url: '/negotia/orderList',
    method: 'get',
    params: params
  });
}

export function settlement(params) {
  return request({
    url: '/order/update/settlement',
    method: 'get',
    params: params
  });
}

export function queryBalance(params) {
  return request({
    url: `/order/queryBalance`,
    method: 'get',
    params
  });
}

export function negotiaCancel(params) {
  return request({
    url: '/negotia/cancel',
    method: 'get',
    params: params
  });
}
