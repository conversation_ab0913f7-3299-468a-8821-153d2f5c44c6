import request from '@/utils/request';

const webkk = process.env.BASE_API_FED;

export function getProductList(params) {
  return request({
    url: webkk + '/bigMember/product/list',
    method: 'get',
    params
  });
}

export function publishStatus(data, params) {
  return request({
    url: webkk + '/bigMember/product/update/publishStatus',
    method: 'post',
    data,
    params
  });
}

export function getOrderList(params) {
  return request({
    url: webkk + '/bigMember/order/list',
    method: 'get',
    params
  });
}

export function getOrderSellerList(params) {
  return request({
    url: webkk + '/bigMember/order/sellerList',
    method: 'get',
    params
  });
}

export function updatePrice(id, params) {
  return request({
    url: webkk + '/bigMember/product/updatePrice/' + id,
    method: 'post',
    params
  });
}

export function productUpdate(id, params) {
  return request({
    url: webkk + '/bigMember/product/updatePrice/' + id,
    method: 'post',
    params
  });
}

export function recoveryList(params) {
  return request({
    url: webkk + '/bigMember/product/recoveryList',
    method: 'get',
    params
  });
}
