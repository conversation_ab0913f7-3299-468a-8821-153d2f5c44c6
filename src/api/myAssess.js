import request from '@/utils/request'
const webkk = process.env.BASE_API_FED;

export function getNegotiaDetail(id) {
    return request({
      url: webkk+`/negotia/detailByTeam?tid=${id}`,
      method: 'get',
    });
  }

  export function sellerOfferPrice(params) {
    return request({
      url: webkk+'/negotia/sellerOfferPrice',
      method: 'post',
      params,
    });
  }
  export function offerPrice(params) {
    return request({
      url: webkk+'/negotia/buyerOfferPrice',
      method: 'post',
      params,
    });
  }
  
  export function sellerDo(params) {
    return request({
      url: webkk+'/negotia/sellerDo',
      method: 'post',
      params,
    });
  }

  export function negotiaCancel(params) {
    return request({
      url: webkk+'/negotia/cancel',
      method: 'post',
      params,
    });
  }