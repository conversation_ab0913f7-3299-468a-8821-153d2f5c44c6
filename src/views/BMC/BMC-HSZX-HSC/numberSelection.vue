<template>
  <div class="app-container">
    <!-- <el-card class="operate-container" shadow="never">
        <i class="el-icon-tickets" style="margin-top: 5px" />
        <span style="margin-top: 5px">数据列表</span>
        <el-button class="btn-add" size="mini" @click="handleAddProductCate()">
          添加
        </el-button>
      </el-card> -->
    <div style="width: 100%; display: flex">
      <div style="flex: 1" v-if="pmsSearchTags.length" class="spaceStart">
        <div class="playSearch_tit">快捷筛选：</div>
        <div>
          <!-- <div v-for="item in pmsSearchTags"> -->
          <el-checkbox-group
            v-model="checkedTags"
            @change="handleCheckedTagsChange"
          >
            <el-checkbox
              v-for="item in pmsSearchTags"
              :label="item.tagName"
              :key="item.tagName"
              >{{ item.tagName }}</el-checkbox
            >
          </el-checkbox-group>
          <!-- </div> -->
        </div>
      </div>
      <div style="width: 100px">
        <el-button size="mini" @click="handleSearchProductNext()">
          高级搜索
        </el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        ref="productCateTable"
        :data="list"
        style="width: 100%"
        border
      >
        <el-table-column label="商品编号" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.productSn }}</template>
        </el-table-column>
        <el-table-column label="商品图" width="100" align="center">
          <template slot-scope="scope">
            <div class="goodsItem_pic_img">
              <img
              style="width: 80px; height: 80px;border-radius: 12px;"
              :src="scope.row.pic"
              alt=""
            />
            <div @click.stop="showImagePriview(scope.row)"
                          class="goodsItem_pic_img_box">预览</div>
            </div>
            
          </template>
        </el-table-column>
        <el-table-column width="100" label="商品价格" align="center">
          <template slot-scope="scope">
            <div style="color: red">¥{{ scope.row.price }}</div>
          </template>
        </el-table-column>
        <el-table-column width="170" label="上架时间" align="center">
          <template slot-scope="scope">
            <div>
              {{ scope.row.publishTime | formatTimetoSS }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品名称" align="center">
          <template slot-scope="scope">
            <!-- <div class="text_linThree"> -->
              <el-tooltip
                class="tooltipItemBox"
                effect="light"
                :content="getWzText(scope.row.attrValueList,scope.row.name,scope.row)"
                placement="top"
                popper-class="custom-tooltip-width"
              >
                <div class="text_linThree custom-tooltip-text-label">{{getWzText(scope.row.attrValueList,scope.row.name,scope.row)}}</div>
              </el-tooltip>
             
            <!-- </div> -->
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              @click="handleXsProductNext(scope.$index, scope.row)"
            >
              添加为线索
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        :page-size="listQuery.pageSize"
        :page-sizes="[20, 40, 60]"
        :current-page.sync="listQuery.pageNum"
        :total="total"
        background
        layout="total,prev, pager, next,jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-drawer
      title="我是标题"
      :size="920"
      :visible.sync="searchDrawer"
      :with-header="false"
    >
      <div style="padding: 20px">
        <!-- height: 700px; overflow: auto -->
        <div style="width: 100%;">
          <el-input
            v-model="keyword2"
            placeholder="请输入内容"
            prefix-icon="el-icon-search"
            style="width: 321px; margin-bottom: 24px"
            clearable
            @input="handelOptsSearch"
          />
          <div
            v-if="keyword2 && (!optsSearchResult || !optsSearchResult.length)"
            style="
              color: rgba(0, 0, 0, 0.4);
              font-family: PingFang SC;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              letter-spacing: 0.56px;
            "
          >
            暂无符合条件的筛选项
          </div>
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <div
              v-for="item in optsSearchResult"
              :key="item.value"
              :class="getOptIsCheck(item) ? 'active' : ''"
              class="opt-item spaceAround"
              @click="handelOptClick(item)"
            >
              {{ item.value }}
            </div>
          </div>
          <CheckBoxList
            v-for="(item, index) in checkBoxAttrGroup"
            :key="item"
            :group-name="item"
            :index="index"
            :list="
              checkBoxAttributeList
                .filter((e) => e.nameGroup === item)
                .map((item) => ({
                  ...item,
                  valueSearchType: item.valueSearchType || 'must',
                  valueSearchTypeValue:
                    item.valueSearchType != 'must'
                      ? parseInt(item.valueSearchType.split('_')[1]) || 0
                      : '',
                  maxSholdValue: item.selectValue ? item.selectValue.length : 1,
                }))
            "
            @change="handelCheckboxAttrChange"
            @searchTypeClick="searchTypeClick"
          />
          <!-- 输入框点击搜索区域 -->
          <!--  -->
          <div style="margin-top: 10px">
            <div class="spaceStart flexWrap sxbox">
              <InputNumber
                v-for="(item, index) in inputAttributeList"
                :item="item"
                :key="index"
                @change="(v, i) => handelInputAttrChange(index, v, i)"
              />
            </div>

            <div class="keyword_box">
              <div class="spaceBetween">
                <div class="spaceStart">
                  <div class="playSearch_tit">关键词</div>
                  <el-input
                    v-model="keyword"
                    class="search_keyword"
                    placeholder="请输入您要查找账号/关键词"
                    @keyup.enter.native="searchListFun"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="border-top: dashed 1px #dcdcdc;padding-top: 10px;margin-top: -10px;">
          <div class="search_dzTitle">排序条件（可拖拽排序）</div>
          <div
            class="spaceStart sortItemBox"
            style="flex-wrap: wrap; margin-bottom: 10px"
          >
            <div
              v-for="(item, index) in comprehensiveData"
              :class="item.sort != '' ? 'active' : ''"
              :key="item.sortId"
              class="spaceStart sort_item"
              @click="sortChos(item)"
            >
              <div>{{ item.sortName }}</div>
              <IconFont
                v-if="item.sort == '' && item.value != ''"
                :size="15"
                style="margin: 0 0 0 4px"
                icon="sort"
              />

              <IconFont
                v-if="item.sort == 'asc' && item.value != ''"
                :size="15"
                style="margin: 0 0 0 4px"
                icon="asc"
              />
              <IconFont
                v-if="item.sort == 'desc' && item.value != ''"
                :size="15"
                style="margin: 0 0 0 4px"
                icon="desc"
              />
            </div>
          </div>
        </div>
        <div style="border-top: dashed 1px #dcdcdc;padding-top: 10px">
          <div class="search_dzTitle">其他条件</div>
          <div style="margin-bottom: 20px;flex-wrap: wrap;" class="spaceStart">
            <div class="labelTitle">是否排除号商</div>
            <el-radio v-model="excludeBmc" label="1">是</el-radio>
            <el-radio v-model="excludeBmc" label="0">否</el-radio>
            <div class="labelTitle" style="width: 120px; margin-left: 50px">
              筛选时间范围：
            </div>
            <el-date-picker
              v-model="startTime"
              type="datetime"
              default-time="00:00:00"
              placeholder="选择开始时间"
            >
            </el-date-picker>
            <el-date-picker
              v-model="endTime"
              type="datetime"
              default-time="23:59:59"
              placeholder="选择结束时间"
            >
            </el-date-picker>
            <div class="labelTitle" style="width: 85px">
              快捷时间：
            </div>
            <el-input 
              style="width: 180px;" 
              placeholder="请输入分钟" 
              v-model="startTimeNumber"
              oninput="value=value.replace(/[^\d]/g,'')"
            >
            <template slot="append">
                <el-select
                  style="width: 80px"
                  v-model="startTimeUnit"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in startTimeUnitList"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-input>
          </div>
        </div>
        <div
          v-if="(selectValueList().length ||selectNumValueList().length)"
          class="spaceStart"
          style="
            align-items: baseline;
            border-bottom: 0.5px solid #ff7a00;
            margin-bottom: 0px;
            padding-bottom: 20px;
            margin-top: 20px;
            max-height:  200px;
            overflow-y: auto;
          "
        >
          <span class="playSearch_tit" style="font-weight: 600">您已选择：</span>
          <div class="spaceStart flexWrap" style="flex: 1">
            <span
              v-for="item in selectValueList()"
              :key="item.value"
              class="opt-item"
              @click="handelOptClick(item)"
            >
              {{ item.value }}&nbsp;<i
                class="el-icon-close"
                style="font-size: 14px; cursor: pointer"
              ></i>
            </span>
            <span
              v-for="item in selectNumValueList()"
              :key="item.value"
              class="opt-item"
              @click="handelOptNumClick(item)"
            >
              {{ item.value }}&nbsp;<i
                class="el-icon-close"
                style="font-size: 14px; cursor: pointer"
              ></i>
            </span>
          </div>
        </div>
        <div class="config-footer" style="position: relative;top: 10px;">
          <el-button @click="searchDrawer = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- <swperImagePriview v-if="showViewer"  :tableData="tableData" :product="productObj" :productSn="productSn" :z-index="10000" :initial-index="imgViewer" :on-close="closeViewer"
      :url-list="arrDtPicForShow" :tableDataFlag="true" :gameSysinfoReadcount="gameSysinfoReadcount" :gameSysinfoCollectcount="gameSysinfoCollectcount" :price="price"/> -->
    <swperImagePriviewPage ref="swperImagePriviewPage"/>
  </div>
</template>

<script>
import {
  fetchList,
  deleteProductCate,
  updateShowStatus,
  updateNavStatus,
  importByProductCategory,
  deleteAll,
} from '@/api/productCate';
import swperImagePriview from '@/components/imagePriview/index.vue'
import swperImagePriviewPage from '@/components/imagePriview/page.vue'
import CheckBoxList from './components/checkBoxList.vue';
import InputNumber from './components/ipuntNumber.vue';
import defaultImage from '../../../assets/images/sl_img.png';
import Sortable from 'sortablejs';
import moment from 'moment';
import { GOOD_TITLE, GOOD_TITLE_OTHER } from '@/utils/const.js';
import {
  getProductAttributeAttrInfo,
  getSearchTagList,
  addSearchTag,
  updateSearchTag,
  deleteSearchTag,
  detailSearchTag,
  searchProductList2,
  productLeadCreate,
  getDetailByCode
} from '@/api/bmc';
export default {
  components: {
    CheckBoxList,
    InputNumber,
    swperImagePriview,
    swperImagePriviewPage
  },
  name: 'ProductCateList',
  filters: {
    levelFilter(value) {
      if (value === 0) {
        return '一级';
      } else if (value === 1) {
        return '二级';
      }
    },
    disableNextLevel(value) {
      if (value === 0) {
        return false;
      } else {
        return true;
      }
    },
  },
  data() {
    return {
      excludeBmc:'',
      startTimeNumber:'',
      startTimeUnit: '小时',
      startTimeUnitList:['分钟','小时','天'],
      queryOrderSortParams:[],
      startTime:'',
      endTime:'',
      tableData: [],
      productObj:{},
      productSn:'',
      showViewer:false,
      imgViewer:0,
      arrDtPicForShow:[],
      gameSysinfoReadcount:0,
      gameSysinfoCollectcount:0,
      price:0,
      gameSysinfoReadcount:'',
      gameSysinfoCollectcount:'',
      comprehensiveData:[],
      cateList: [],
      sortableInstance: null,
      list: null,
      total: null,
      listLoading: true,
      listQuery: {
        pageNum: 1,
        pageSize: 20,
      },
      
      searchDrawer: false,
      pmsSearchTags: [],
      keyword: '',
      keyword2: '',
      checkedTags: [],
      productCategoryId: '',
      parentId: 74,
      attributeData: [],
      checkBoxAttributeList: [],
      harborConfigList: [],
      searchParam: {
        productCategoryId: this.$route.query.id,
        pageNum: 1,
        pageSize: 20,
      },
      optsSearchResult: [],
      inputAttributeList: [],
    };
  },
  watch: {
    $route(route) {
      this.resetParentId();
    },
  },
  created() {
    this.productCategoryId = this.$route.query.id;
    this.doInit();
    this.resetParentId();
    // this.searchByCate(1);
    this.getTagList();
  },
  computed: {
    newCheckBoxAttrGroup() {
      console.log(this.checkBoxAttrGroup, 11111111);

      return this.checkBoxAttrGroup;
    },
  },
  mounted() {},
  methods: {
    getWzText(attrValueList,name,row) {
      if(row.productCategoryId!=82){
        return name
      }
      const newList = [...attrValueList].map((item) => ({
        name: item.name || item.productAttributeName,
        values: item.values || item.value.split(','),
      }));
      const titleList = GOOD_TITLE.concat(GOOD_TITLE_OTHER);
      const filterList = newList
        .filter((v) => {
          if (
            titleList.map((k) => k.label).includes(v.name) &&
            v.values &&
            v.values.length &&
            v.values[0] !== '0'
          ) {
            this.$set(
              v,
              'index',
              titleList.find((i) => i.label === v.name).index,
            );
            return v;
          }
        })
        .sort((a, b) => a.index - b.index);
      const titleInfo = filterList.map((v) => {
        let newName;
        if (v.name === '贵族等级') {
          newName = v.values[0].replace('贵族', 'V');
        } else if (GOOD_TITLE_OTHER.map((k) => k.label).includes(v.name)) {
          newName = v.values.length;
        } else {
          newName = v.values[0];
        }
        return (
          newName + titleList.find((i) => i.label === v.name).children.label
        );
      });
      return titleInfo.join(' ')+name;
    },
    initDragSort() {
      const el = document.querySelectorAll('.sortItemBox')[0];
      this.sortableInstance = Sortable.create(el, {
        onEnd: ({ oldIndex, newIndex }) => {
         
          
          const arr = [...this.comprehensiveData]; // 创建新数组
          const [page] = arr.splice(oldIndex, 1);
          arr.splice(newIndex, 0, page);
          this.comprehensiveData = JSON.parse(JSON.stringify(arr));
          this.$forceUpdate();
        },
      });
    },
    sortChos(date) {
      this.comprehensiveData.forEach((item, index) => {
        if (item.sortName === date.sortName) {
          const newValue =
            date.sort === '' ? 'desc' : date.sort === 'desc' ? 'asc' : '';
          this.$set(item, 'sort', newValue); // 响应式更新
        } else {
          // this.$set(item, 'selected', ''); // 响应式更新
        }
      });
      this.$forceUpdate();
    },
    formatClasData() {
      if (this.comprehensiveData&&this.comprehensiveData.length) {
        let arrList = [];
        arrList.push({
          sortName: '上架时间',
          sort: '',
          order: 'publishTime',
          sortId: 0,
        });
        arrList.push({
          sortName: '价格',
          sort: '',
          order: 'price',
          sortId: 1,
        });
        let sortId = 2; // 从2开始,因为前面已经有两个固定项(上架时间和价格)的sort分别是0和1
        this.sortCateList.forEach((item) => {
          let ele = _.cloneDeep(item);
          if (ele.searchSort) {
            let params = {
              sortName: `${ele.name}`,
              sort: '',
              order: ele.searchSort,
              sortId: sortId,
            };
            arrList.push(params);
            sortId++;
          }
        });
        arrList.forEach(item => {
        const exists = this.comprehensiveData.some(cItem => cItem.name === item.name);
        if (!exists) {
          // 生成随机 sortId（时间戳 + 随机数）
          const randomSortId = Date.now() + Math.floor(Math.random() * 10000);
          // 添加新项到 comprehensiveData
          this.comprehensiveData.push({
            ...item,
            sortId: randomSortId, // 赋予随机 ID
          });
        }
      });
        return;
      }
      this.comprehensiveData = [];
      this.comprehensiveData.push({
        sortName: '上架时间',
        sort: '',
        order: 'publishTime',
        sortId: 0,
      });
      this.comprehensiveData.push({
        sortName: '价格',
        sort: '',
        order: 'price',
        sortId: 1,
      });
      let sortId = 2; // 从2开始,因为前面已经有两个固定项(上架时间和价格)的sort分别是0和1
      this.sortCateList.forEach((item) => {
        let ele = _.cloneDeep(item);
        if (ele.searchSort) {
          let params = {
            sortName: `${ele.name}`,
            sort: '',
            order: ele.searchSort,
            sortId: sortId,
          };
          this.comprehensiveData.push(params);
          sortId++;
        }
      });
    },
    showImagePriview(item){
      this.$refs.swperImagePriviewPage.showImagePriview(item)
    },
    // getSarchArr() {
    //   let queryIntParams = undefined;
    //   const checkBoxParams = this.checkBoxAttributeList.map((e) => {
    //     return {
    //       ...e,
    //       value: e.selectValue.join(','),
    //     };
    //   });
    //   const findIndex = checkBoxParams.findIndex((ele) => {
    //     return ele.sort === 32306 && ele.ename;
    //   });
    //   let queryStrParams = []
    //   if (findIndex !== -1) {
    //     let { value, ename } = checkBoxParams[findIndex];
    //     if (value) {
    //       queryStrParams.push({
    //         key: ename,
    //         value,
    //       });
    //     }
    //     checkBoxParams.splice(findIndex, 1);
    //   }
    //   const inputParams = this.inputAttributeList
    //     .map((e) => {
    //       let [min, max] = e.selectValue || [];
    //       // 价格是公共属性，得放到queryIntParams
    //       if (e.name === '价格' && (!isNaN(min) || !isNaN(max))) {
    //         queryIntParams = [
    //           {
    //             min: !isNaN(min) ? parseInt(min, 10) : undefined,
    //             key: 'price',
    //             max: !isNaN(max) ? parseInt(max, 10) : undefined,
    //           },
    //         ];
    //       }
    //       return {
    //         ...e,
    //         value:
    //           !isNaN(min) || !isNaN(max) ? `${min || 0}-${max || 9999999}` : '',
    //       };
    //     })
    //     .filter((e) => e.name !== '价格');
    //   return checkBoxParams.concat(inputParams)
    // },
    // formatValue(value) {
    //   return value
    //     .replace(/[,]/g, '，')
    //     .replace(/\[核\]/g, '')
    //     .replace(/\[绝\]/g, '')
    //     .replace(/\[钱\]/g, '');
    // },
    // mergeOptions(productAttributeList, productAttributeValueList) {
    //   productAttributeList.sort((a, b) => {
    //     return a.type - b.type;
    //   });
    //   productAttributeList.sort((a, b) => {
    //     return b.sort - a.sort;
    //   });
    //   let tempList = [];
    //   productAttributeList.forEach((ele) => {
    //     if (ele.name == '营地ID') {
    //       const findIt = productAttributeValueList.find((item) => {
    //         return item.productAttributeId === ele.id;
    //       });
    //       this.wzryId = findIt && findIt.value;
    //     }
    //     if (ele.type === 1 || ele.type === 2) {
    //       const findV = productAttributeValueList.find((item) => {
    //         return item.productAttributeId === ele.id;
    //       });
    //       if (findV && findV.value) {
    //         tempList.push({
    //           name:
    //             ele.selectType == 2
    //               ? `【${ele.name}】${this.formatValue(findV.value)}`
    //               : ele.name,
    //           label: ele.name,
    //           value: this.formatValue(findV.value),
    //           sort: ele.sort,
    //           selectType: ele.selectType,
    //         });
    //       }
    //     }
    //   });
    //   return tempList;
    // },
    // showImagePriview(item) {
    //   console.log(item,1111111);
      
    //   getDetailByCode({ productSn: item.productSn }).then((res) => {

    //     const product = res.data.product
    //     let albumPicsJson = product.albumPicsJson ? product.albumPicsJson : [];
    //     let arr = []
    //     if (product.albumPics) {
    //       arr = product.albumPics.split(',').filter(item => item.trim() !== '');
    //     } else {
    //       albumPicsJson = JSON.parse(albumPicsJson)
    //       albumPicsJson.forEach(item => {
    //         if (arr.length < 10&&item.url) {
    //           arr.push(item.url)
    //         }
    //       })
    //     }
    //     this.productObj=item
    //     this.productSn=item.productSn
    //     this.gameSysinfoReadcount=item.gameSysinfoReadcount
    //     this.gameSysinfoCollectcount=item.gameSysinfoCollectcount
    //     this.price=item.price
    //     let oldArr = this.mergeOptions(
    //       res.data.productAttributeList,
    //       res.data.productAttributeValueList
    //     );
    //     let newArr = [];
    //     let newArr2 = [];
    //     oldArr.forEach((item) => {
    //       if (item.selectType == 2) {
    //         newArr2.push(item);
    //       } else {
    //         newArr.push(item);
    //       }
    //     });

    //     newArr.sort((a, b) => {
    //       return b.type - a.type;
    //     });
    //     newArr2.sort((a, b) => {
    //       return b.sort - a.sort;
    //     });
    //     if (res.data.product.description) {
    //       newArr2.push({
    //         name: `【卖家说】${res.data.product.description}`,
    //         value: '',
    //         sort: 11,
    //         selectType: 2,
    //       });
    //     }
    //     let allArr = newArr.concat(newArr2);
    //     let searchArr = this.getSarchArr()
    //     allArr.forEach(item => {
    //       let searchName = searchArr.find((j) => {
    //         return j.name == item.label
    //       })
    //       // console.log(searchName,99999)

    //       if (searchName && searchName.value && searchName.value.length > 0) {
    //         let nameList = searchName.value.split(',')
    //         nameList.sort((a, b) => b.length - a.length);
    //         nameList.forEach(keyword => {
    //           const regex = new RegExp(`(${keyword})`, 'gi');
    //           if (item.selectType == 2) {
    //             item.name = item.name.replace(regex, '<span style="color:#ff720c">$1</span>');
    //           } {
    //             item.value = item.value.replace(regex, '<span style="color:#ff720c">$1</span>');
    //           }

    //         });
    //       }

    //     })
    //     // console.log(JSON.stringify(allArr),8989)
    //     // console.log(searchArr,7777)
    //     // this.tableData = newArr.concat(newArr2);
    //     this.tableData = allArr
    //     console.log(this.tableData)
    //     this.playTableLoading = false
    //     arr.unshift(defaultImage);
    //     this.arrDtPicForShow = arr
    //     this.showViewer = true
    //   })
    //   // this.arrDtPicForShow = arr
    //   // this.showViewer = true
    // },
  
    doInit() {
      this.loadCateList(this.productCategoryId).then(() => {
        this.searchByCate();
      });
    },
    handleCheckedTagsChange(val) {
      if (!val.length) {
        this.searchConfigList = [];
        this.harborConfigList = [];
        this.cleanAllChoose();
        return;
      }

      // 根据当前选中标签进行分类  分成唯一和不是唯一两类
      let uniqueTags = [];
      let nonUniqueTags = [];
      val.forEach((tagName) => {
        const tagItem = this.pmsSearchTags.find(
          (item) => item.tagName === tagName
        );
        if (tagItem) {
          if (tagItem.isUnique == 1) {
            uniqueTags.push(tagName);
          } else {
            nonUniqueTags.push(tagName);
          }
        }
      });

      // 如果有多个 isUnique==1 的，只保留最后一个
      if (uniqueTags.length > 1) {
        uniqueTags = [uniqueTags[uniqueTags.length - 1]];
      }

      // 重新组装 checkedTags
      this.checkedTags = [...uniqueTags, ...nonUniqueTags];

      // 开始处理筛选逻辑
      this.searchConfigList = [];
      let pmsSearchTags = JSON.parse(JSON.stringify(this.pmsSearchTags));
      //根据val来调整顺序
      pmsSearchTags.sort((a, b) => {
        return (
          this.checkedTags.indexOf(a.tagName) -
          this.checkedTags.indexOf(b.tagName)
        );
      });
      console.log(pmsSearchTags,'执行了');
      
      //循环标签列表 查询当前是否选中标签 然后获取选中标签的内容
      pmsSearchTags.forEach((item) => {
        console.log(item);
        let searchConditions = JSON.parse(item.searchConditions);
        if (this.checkedTags.includes(item.tagName)) {
          this.excludeBmc=searchConditions.excludeBmc
          this.startTimeNumber=searchConditions.startTimeNumber
          this.startTimeUnit=searchConditions.startTimeUnit
          this.queryOrderSortParams=searchConditions.queryOrderSortParams
          this.comprehensiveData=searchConditions.queryOrderSortParams
          this.startTime=searchConditions.startTime,
          this.endTime=searchConditions.endTime
          // 如果还没选择 直接修改内容 不走多个合并逻辑
          if (this.searchConfigList.length == 0) {
            this.searchConfigList = searchConditions.attrValueList;
            this.keyword = searchConditions.keyword || '';
          } else {
            // 合并逻辑（如果需要的话）
            searchConditions.attrValueList.forEach((attrChildItem) => {
              const existingItem = this.searchConfigList.find(
                (childItem) => childItem.name === attrChildItem.name
              );
              //判断合并的是否和当前选中的一样 如果一样则有冲突 使用最新的覆盖上一次的  否则就是不存在冲突 直接push进去
              if (existingItem) {
                if (
                  !attrChildItem.valueSearchType ||
                  attrChildItem.valueSearchType === 'must'
                ) {
                  existingItem.selectValue = attrChildItem.selectValue;
                } else {
                  // 否则合并去重（保留先勾选的值，并合并后勾选的值）
                  existingItem.selectValue = [
                    ...new Set([
                      ...existingItem.selectValue,
                      ...attrChildItem.selectValue,
                    ]),
                  ];
                }
              } else {
                this.searchConfigList.push(attrChildItem);
              }
            });
          }
        }
      });

      // 隐藏属性处理
      this.harborConfigList = [];
      let attributeData = JSON.parse(JSON.stringify(this.attributeData));
      const hasPriceInOriginal = attributeData.some(
        (item) => item.name === '价格'
      );
      const priceItemInSearch = this.searchConfigList.find(
        (j) => j.name === '价格'
      );

      if (!hasPriceInOriginal && priceItemInSearch) {
        attributeData.unshift({
          name: '价格',
          inputType: 0,
          selectValue: priceItemInSearch.selectValue,
        });
      }
      //匹配到的就赋值 未匹配到就置空
      attributeData.forEach((item) => {
        const matchedConfig = this.searchConfigList.find(
          (j) => j.name === item.name
        );
        const allUnmatchedItems = this.searchConfigList.filter((config) => {
          return !attributeData.some((attr) => attr.name === config.name);
        });
        //获取隐藏属性
        if (matchedConfig) {
          item.selectValue = matchedConfig.selectValue;
          item.valueSearchType = matchedConfig.valueSearchType;
        } else {
          item.selectValue = [];
        }
        if (allUnmatchedItems.length) {
          this.harborConfigList = allUnmatchedItems;
        }
      });
      console.log(attributeData, 11111);
      this.getSeachConfig(attributeData);
      this.searchByCate();
    },
    getTagList() {
      getSearchTagList(this.productCategoryId, this.listQuery).then(
        (response) => {
          if (response.data.list && response.data.list.length) {
            this.pmsSearchTags = response.data.list.sort(
              (a, b) => a.sort - b.sort
            );
          }
        }
      );
    },
    getSeachConfig(data) {
      console.log(data, 222222);
      this.attributeData = JSON.parse(JSON.stringify(data));

      // 根据searchSortWeight排序
      const newCateList = data
        .filter((ele) => ele.type !== 0 && ele.searchType !== 0)
        .sort((a, b) => {
          const customA = JSON.parse(a.custom || '{}');
          const customB = JSON.parse(b.custom || '{}');
          return (
            ((customB && customB.searchSortWeight) || 0) -
            ((customA && customA.searchSortWeight) || 0)
          );
        });
      const cateList = data
        .filter(
          (ele) =>
            ele.type !== 0 &&
            ele.searchType !== 0 &&
            JSON.parse(ele.custom || '{}').showSearch !== false
        )
        .sort((a, b) => {
          return b.sort - a.sort;
        });
      // 复选框属性
      const checkBoxAttrGroup = [];
      this.checkBoxAttributeList = cateList
        .filter((e) => e.inputType !== 0) // 过滤掉输入框属性
        .map((e) => {
          const {
            inputList,
            selectType,
            name,
            searchType,
            nameGroup,
            custom,
            selectValue,
          } = e;

          let groupName = nameGroup || name;
          checkBoxAttrGroup.push(groupName);

          let optionList = inputList.split(','); // 子级数据
          let pOptionList = null; // 父级数据

          // 级联数据
          if (selectType === 3) {
            optionList = {};
            const treeData = JSON.parse(inputList);

            pOptionList = treeData.map((e) => e.parent_name);
            treeData.forEach((e) => {
              const { parent_name, childList } = e;
              optionList[parent_name] = childList;
            });
          }
          // 处理默认值
          let value = [];
          if (name === '账号专区') {
            value = ['在售专区'];
          }
          // value=this.getTagDetailSelectValue(name)||[]
          // if(this.tagDetail && Object.keys(this.tagDetail).length > 0){
          //   this.keyword=this.tagDetail.keyword
          //   this.tagDetail.attrValueList.forEach(item=>{
          //       if(item.name==name){
          //           value=item.selectValue
          //       }
          //   })
          // }
          if (name === '商品类型') {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('goodsType')) {
              value = optionList.includes(urlParams.get('goodsType'))
                ? [urlParams.get('goodsType')]
                : [];
            } else if (custom && custom !== '{}') {
              let newCustom = JSON.parse(custom);
              value = optionList.includes(newCustom.sdefault)
                ? [newCustom.sdefault]
                : [];
            } else {
              value = [optionList[0]];
            }
          }

          return {
            ...e,
            selectValue: selectValue || value,
            childValue: {}, // 只有级联数据才有
            hasClear: name != '商品类型' && searchType !== 3,
            optionList,
            pOptionList,
            defaultValue: value, // 清空选项时 可以设回初识默认值
            nameGroup: groupName,
            valueSearchType: e.valueSearchType || 'must',
          };
        });

      console.log(
        'checkBoxAttrGroup',
        [...new Set(checkBoxAttrGroup)],
        this.checkBoxAttributeList
      );
      this.checkBoxAttrGroup = [...new Set(checkBoxAttrGroup)];

      // 输入框属性搜索列表
      const inputAttributeList = cateList
        .filter((e) => e.inputType === 0 && e.name != '价格')
        .map((e) => {
          return {
            ...e,
            selectValue: e.selectValue || [],
            defaultValue: [], // 清空选项时 可以设回初识默认值
          };
        });

      // 价格公共属性插入
      this.inputAttributeList = [
        {
          'name': '价格',
          'selectType': 0,
          'inputType': 0,
          'inputList': '',
          'sort': 0,
          'filterType': 0,
          'searchType': 2,
          'type': 1,
          'searchSort': 0,
          selectValue: this.getTagDetailSelectValue('价格', data) || [],
          defaultValue: [],
        },
      ].concat(inputAttributeList);

      // 排序数据
      // this.comprehensiveData = [
      //   // {
      //   //   sortName: '综合排序',
      //   //   selected: '',
      //   //   searchSort: 'score',
      //   //   value: '',
      //   // },
      //   // {
      //   //   sortName: '最多人看',
      //   //   selected: '',
      //   //   searchSort: 'gameSysinfoReadcount',
      //   // },
      //   {
      //     sortName: '上架时间',
      //     selected: '',
      //     searchSort: 'publishTime',
      //   },
      //   {
      //     sortName: '价格',
      //     selected: '',
      //     searchSort: 'price',
      //   },
      // ].concat(
      //   newCateList
      //     .filter((e) => {
      //       if (!e.searchSort) return false;
      //       const custom = e.custom ? JSON.parse(e.custom) : {};
      //       return Object.keys(custom).length > 0;
      //     })
      //     .map((e) => {
      //       return {
      //         sortName: e.name,
      //         selected: '',
      //         searchSort: e.searchSort,
      //       };
      //     })
      // );
      let list = [];
          data.forEach((ele) => {
            if (ele.type !== 0 && ele.searchType !== 0) {
              ele.moreTxt = '展开全部';
              list.push(ele);
            }
          });
          this.sortCateList = list;
          this.formatClasData();
     
      return true;
    },
    getTagDetailSelectValue(name, data) {
      let value = [];
      data.forEach((item) => {
        if (item.name == name) {
          value = item.selectValue;
        }
      });
      return value;
    },
    loadCateList() {
      return getProductAttributeAttrInfo(this.productCategoryId).then(
        (response) => {
          if (response.code == 200) {
            return this.getSeachConfig(response.data);
          }
        }
      );
    },
    resetPage() {
      this.searchParam.pageNum = 1;
      this.totalPage = 0;
    },

    searchByCate(isPageNum, shaixuan) {
      this.loadFlag = false;
      if (isPageNum != 1) {
        this.resetPage();
      }

      if (shaixuan == 'shaixuan') {
        this.checkInputSearch();
      }

      // 搜集复选框搜索条件
      console.log(this.checkBoxAttributeList, 2222222);
      let checkBoxAttributeList = JSON.parse(
        JSON.stringify(this.checkBoxAttributeList)
      );

      // const checkBoxParams = this.checkBoxAttributeList.map((e) => {
      if (this.harborConfigList.length) {
        let arr = this.harborConfigList.filter((item) => {
          return item.inputType != 0;
        });
        checkBoxAttributeList = checkBoxAttributeList.concat(arr);
      }
      const checkBoxParams = checkBoxAttributeList.map((e) => {
        return {
          ...e,
          value: e.selectValue.join(','),
        };
      });

      // 搜集输入框条件,价格是公用条件要单独拎出
      let queryIntParams = undefined;
      let inputAttributeList = JSON.parse(
        JSON.stringify(this.inputAttributeList)
      );
      if (this.harborConfigList.length) {
        let arr = this.harborConfigList.filter((item) => {
          return item.inputType == 0;
        });
        inputAttributeList = inputAttributeList.concat(arr);
      }
      // const inputParams = this.inputAttributeList
      const inputParams = inputAttributeList
        .map((e) => {
          let [min, max] = e.selectValue || [];

          // 价格是公共属性，得放到queryIntParams
          if (e.name === '价格' && (!isNaN(min) || !isNaN(max))) {
            queryIntParams = [
              {
                min: !isNaN(min) ? parseInt(min, 10) : undefined,
                key: 'price',
                max: !isNaN(max) ? parseInt(max, 10) : undefined,
              },
            ];
          }
          return {
            ...e,
            value:
              !isNaN(min) || !isNaN(max) ? `${min || 0}-${max || 9999999}` : '',
          };
        })
        .filter((e) => e.name !== '价格');

      // 如果是32306，有ename，放入queryStrParams里,同时属性里要剔除。否则不用管。pc端不像h5端，按属性正常处理的，不用加回来
      // 比如区服的数据
      let queryStrParams = [];
      const findIndex = checkBoxParams.findIndex((ele) => {
        return ele.sort === 32306 && ele.ename;
      });

      if (findIndex !== -1) {
        let { value, ename } = checkBoxParams[findIndex];
        if (value) {
          queryStrParams.push({
            key: ename,
            value,
          });
        }
        checkBoxParams.splice(findIndex, 1);
      }

      // if (this.stockQuery) {
      //   let queryIntParams = data.queryIntParams;
      //   if (queryIntParams) {
      //     data.queryIntParams = data.queryIntParams.concat(
      //       this.stockQuery.queryIntParams,
      //     );
      //   } else {
      //     data.queryIntParams = this.stockQuery.queryIntParams;
      //   }
      // }
      this.listLoading = true;
      const searchParam = {
        ...this.searchParam, //分页等条件
        // memberId: this.userInfo.id,
      };
      const params = {
        attrValueList: checkBoxParams.concat(inputParams),
        keyword: this.keyword, // 关键词搜索
        startTime: this.startTime ? moment(this.startTime).format('YYYY-MM-DD HH:mm:ss') : '',
        endTime: this.endTime ? moment(this.endTime).format('YYYY-MM-DD HH:mm:ss') : '',
        sort: this.comprehensiveDataSort,
        order: this.comprehensiveDataOrder,
        queryStrParams,
        queryIntParams, // 价格搜索条件
        excludeBmc:this.excludeBmc,
        startTimeNumber:this.startTimeNumber,
        startTimeUnit:this.startTimeUnit,
        // startTime:this.startTime,
        // endTime:this.endTime,
        queryOrderSortParams:this.comprehensiveData
      };
      searchProductList2(searchParam, params).then((response) => {
        this.listLoading = false;
        if (response.code == 200) {
          let list = response.data.list || [];
          //   if(list.length<10){
          //     this.isScrollDisabled=true
          //   }else{
          //     this.isScrollDisabled=false
          //   }
          //   if (this.checkSn(list)) {
          //     return;
          //   }
          this.total = response.data.total;
          this.list = list;
          // if (isPageNum == 1) {
          //   this.list.push(...list);
          // } else {
          //   this.list = list;
          // }

          this.list.forEach((ele) => {
            const findtss = ele.attrValueList.find((item) => {
              return item.name === '已使用天赏石';
            });
            const findtss2 = ele.attrValueList.find((item) => {
              return item.name === '未使用天赏石';
            });
            const findtss3 = ele.attrValueList.find((item) => {
              return item.name === '账号专区';
            });
            const findtss4 = ele.attrValueList.find((item) => {
              return item.name === '账号类型';
            });
            const findtss5 = ele.attrValueList.find((item) => {
              return item.name === '职业';
            });
            const findtss6 = ele.attrValueList.find((item) => {
              return item.name === '稀有外观';
            });
            const findtss7 = ele.attrValueList.find((item) => {
              return item.name === '天赏祥瑞';
            });
            const findtss8 = ele.attrValueList.find((item) => {
              return item.name === '天赏发型';
            });
            const findtss9 = ele.attrValueList.find((item) => {
              return item.name === '灵韵数量';
            });
            const findtss10 = ele.attrValueList.find((item) => {
              return item.name === '天霓染';
            });
            const zxfindtss = ele.attrValueList.find((item) => {
              return item.name === '已使用女娲石数量';
            });
            const zxfindtss2 = ele.attrValueList.find((item) => {
              return item.name === '未使用女娲石数量';
            });
            // 灵韵数量
            ele.tssnum = 0;
            ele.tssnum = 0;
            ele.topAccount = '';
            ele.accountType = '';
            ele.careers = '';
            ele.appearance = '';
            ele.auspiciousSymbol = '';
            ele.hairstyle = '';
            ele.aura = '';
            ele.tianniRan = '';
            ele.zxtssnum = 0;
            if (zxfindtss && zxfindtss.intValue !== -1) {
              ele.zxtssnum = zxfindtss.intValue;
            }
            if (zxfindtss2 && zxfindtss2.intValue !== -1) {
              if (zxfindtss2.intValue === -1) {
                zxfindtss2.intValue = 0;
              }
              if (ele.zxtssnum === -1) {
                ele.zxtssnum = 0;
              }
              ele.zxtssnum = ele.zxtssnum + zxfindtss2.intValue;
            }
            if (findtss) {
              ele.tssnum = findtss.intValue;
            }
            if (findtss2) {
              ele.tssnum = ele.tssnum + findtss2.intValue;
            }
            if (findtss3) {
              ele.topAccount = findtss3.value;
            }
            if (findtss4) {
              ele.accountType = findtss4.value;
            }
            if (findtss5) {
              ele.careers = findtss5.value;
            }
            if (findtss6) {
              ele.appearance = findtss6.value;
            }
            if (findtss7) {
              ele.auspiciousSymbol = findtss7.value;
            }
            if (findtss8) {
              ele.hairstyle = findtss8.value;
            }
            if (findtss9) {
              ele.aura = findtss9.value;
            }
            if (findtss10) {
              ele.tianniRan = findtss10.value;
            }
          });
          setTimeout(() => {
            this.loadFlag = true;
          }, 500);
        }
      });
    },
    checkSn(list) {
      return false;
      let reg = /^[a-zA-Z][a-zA-Z0-9]*[0-9]$/;
      if (
        reg.test(this.keyword) &&
        list.length === 1 &&
        this.searchParam.pageNum === 1
      ) {
        const { productCategoryId, id, productSn } = list[0];
        this.keyword = '';
        this.$router.push({
          path: `/gd/${productSn}`,
        });
        return true;
      }
      return false;
    },

    deleteIndex() {
      this.$confirm('是否要删除所有索引', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteAll().then((res) => {
          if (res.code === 200) {
            this.$message.success('删除索引成功');
          }
        });
      });
    },
    cleanAllChoose(isAxios = true) {
      
      this.searchConfigList = [];
      this.harborConfigList = [];
      this.checkedTags = [];
      this.keyword = '';
      this.comprehensiveDataOrder = '';
      this.comprehensiveDataSort = '';
      this.comprehensiveData.forEach((item, index) => {
        item.selected = '';
        item.sort=''
      });

      this.inputAttributeList = this.inputAttributeList.map((e) => ({
        ...e,
        selectValue: e.defaultValue,
      }));
      this.checkBoxAttributeList = this.checkBoxAttributeList.map((e) => ({
        ...e,
        selectValue: e.defaultValue,
      }));
      this.$forceUpdate();

      this.serverDateSub = [];
      if (isAxios) {
        this.searchByCate();
      }
    },
    handleSearchProductNext() {
      this.searchDrawer = true;
      setTimeout(()=>{
        console.log(this.comprehensiveData,111222);
        
        this.initDragSort();
      })
    },
    refreshIndex(index, row) {
      importByProductCategory({
        id: row.id,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('刷新索引成功');
        }
      });
    },
    resetParentId() {
      this.listQuery.pageNum = 1;
      this.parentId = 74;
      // if (this.$route.query.parentId != null) {
      //   this.parentId = this.$route.query.parentId;
      // } else {
      //   this.parentId = 0;
      // }
    },
    handleAddProductCate() {
      this.$router.push('/pms/addProductCate');
    },
    handleSizeChange(val) {
      console.log(val,11122233);
      
      this.searchParam.pageSize = val;
      
      this.searchByCate();
    },
    handleCurrentChange(val) {
      this.searchParam.pageNum = val;
      this.searchByCate(1);
    },
    handleNavStatusChange(index, row) {
      const data = new URLSearchParams();
      const ids = [];
      ids.push(row.id);
      data.append('ids', ids);
      data.append('navStatus', row.navStatus);
      updateNavStatus(data).then((response) => {
        if (response.code == 200) {
          this.$message({
            message: '修改成功',
            type: 'success',
            duration: 1000,
          });
        }
      });
    },
    handleShowStatusChange(index, row) {
      const data = new URLSearchParams();
      const ids = [];
      ids.push(row.id);
      data.append('ids', ids);
      data.append('showStatus', row.showStatus);
      updateShowStatus(data).then((response) => {
        if (response.code == 200) {
          this.$message({
            message: '修改成功',
            type: 'success',
            duration: 1000,
          });
        }
      });
    },
    handleXsProductNext(index, row) {
      let params = {
        productSn: row.productSn,
        status: 'PENDING',
        type: 'RECYCLED',
        subTitle:this.getWzText(row.attrValueList,row.name,row)
      };
      productLeadCreate(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('添加成功');
        }
      });
    },
    handleShowNextLevel(index, row) {
      // row.attriCateId
      // this.$router.push({
      //   path: '/pms/productCateSecond',
      //   query: { parentId: row.id }
      // });
      this.$router.push({
        path: '/pms/productAttrTypeList',
        query: { cid: row.attriCateId },
      });
    },

    handleSearchConfigNext(index, row) {
      this.$router.push({
        path: '/BMC-HSZX/searchConfig',
        query: { id: row.id },
      });
    },
    handleTransferProduct(index, row) {
      console.log('handleAddProductCate');
    },
    handleUpdate(index, row) {
      this.$router.push({
        path: '/pms/updateProductCateList',
        query: { id: row.id },
      });
    },
    handleDelete(index, row) {
      this.$confirm('是否要删除该品牌', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteProductCate(row.id).then((response) => {
          if (response.code == 200) {
            this.$message({
              message: '删除成功',
              type: 'success',
              duration: 1000,
            });
            this.getList();
          }
        });
      });
    },
    //搜索关键词
    handelOptsSearch(v) {
      let result = [];
      if (v) {
        this.checkBoxAttributeList.forEach((e) => {
          if (e.selectType === 3) {
            // 级联数据
            for (let key in e.optionList) {
              const childList = e.optionList[key];
              childList.forEach((child) => {
                if (child.includes(v)) {
                  result.push({
                    name: e.name,
                    parent: key,
                    value: child,
                  });
                }
              });
            }
          } else {
            result = result.concat(
              e.optionList
                .filter((opt) => opt.includes(v))
                .map((opt) => ({ name: e.name, value: opt }))
            );
          }
        });
      }
      this.optsSearchResult = result;
    },
    // 筛选输入框区域值变化
    handelInputAttrChange(index, v) {
      const list = this.checkBoxAttributeList.concat(this.inputAttributeList);
      let flag = false;
      let hasValueName = '';
      // 找出当前已有值的属性名
      list.forEach((item) => {
        if (
          item.selectValue &&
          item.selectValue.length &&
          item.selectValue.some((val) => val !== '')
        ) {
          flag = true;
          hasValueName = item.name;
        }
      });
      // 如果已有值且不是当前要修改的属性,则不允许修改
      if (
        this.isUniqueFlag == 0 &&
        flag &&
        hasValueName !== this.inputAttributeList[index].name
      ) {
        this.$message.error('不是唯一只能选择一个属性');
        return;
      }
      this.$set(this.inputAttributeList[index], 'selectValue', v);
    },
    // 筛选复选框区域值变化
    handelCheckboxAttrChange(v, { name }) {
      const list = this.checkBoxAttributeList.concat(this.inputAttributeList);
      let flag = false;
      let hasValueName = '';
      // 找出当前已有值的属性名
      list.forEach((item) => {
        if (
          item.selectValue &&
          item.selectValue.length &&
          item.selectValue.some((val) => val !== '')
        ) {
          flag = true;
          hasValueName = item.name;
        }
      });
      // 如果已有值且不是当前要修改的属性,则不允许修改
      if (this.isUniqueFlag == 0 && flag && hasValueName !== name) {
        this.$message.error('不是唯一只能选择一个属性');
        return;
      }
      const index = this.checkBoxAttributeList.findIndex(
        (e) => e.name === name
      );

      this.$set(this.checkBoxAttributeList[index], 'selectValue', v);
    },
    searchTypeClick(list, v) {
      const index = this.checkBoxAttributeList.findIndex(
        (e) => e.name === list.name
      );
      this.$set(this.checkBoxAttributeList[index], 'valueSearchType', v);
    },
    selectValueList() {
        console.log(1111);
        
        let a = []      // inputAttributeList
        this.checkBoxAttributeList.forEach((e) => {
          const arr = e.selectValue.map((item) => ({
            name: e.name,
            value: item,
            parent:
              e.ename === 'gameAccountQufu' && e.selectType === 3
                ? item.split('|')[0]
                : '',
          }));
          a = a.concat(arr);
        });
  
        return a;
      },
      selectNumValueList(){
        let a = [];
        this.inputAttributeList.forEach((e)=>{
          let selectValueList = JSON.parse(JSON.stringify(e.selectValue))
          console.log(selectValueList,8888)
          // 如果两个值都为空,则不添加
          if(!selectValueList.length){
            return
          }
          if(selectValueList.every(val => val === '')) return
          // 如果有一个值不为空,则需要添加
          if(selectValueList[0] === '' || selectValueList[0] === null) {
            selectValueList[0] = 0
          }
          if(selectValueList[1] === '' || selectValueList[1] === null) {
            selectValueList[1] = 9999999
          }
          const arr = {
            name: e.name,
            value: e.name + selectValueList.join('-')
          }
          a = a.concat(arr)
        })
        return a
      },
       // 判断筛选项是否选中
       getOptIsCheck({ name, ename, selectType, parent, value }) {
        if (ename === 'gameAccountQufu' && selectType === 3) {
          value = parent + '|' + value;
        }
        return this.selectValueList().find((sV) => {
          return sV.value === value && sV.name === name;
        });
      },
      // 筛选项点击
      handelOptClick({ name, parent, value, selectType, ename }) {
        const index = this.checkBoxAttributeList.findIndex(
          (e) => e.name === name
        );
        const { selectValue, filterType } =
          this.checkBoxAttributeList[index] || {};
  
        let newV = selectValue;
  
        if (
          ename === 'gameAccountQufu' &&
          selectType === 3 &&
          !value.includes('|')
        ) {
          value = `${parent}|${value}`;
        }
  
        // 单选处理
        if (filterType !== 1) {
          if (selectValue.includes(value)) {
            newV = [];
          } else {
            newV = [value];
          }
        } else {
          // 多选处理 filterType=1 表示多选
          if (selectValue.includes(value)) {
            newV = selectValue.filter((e) => e !== value);
          } else {
            newV.push(value);
          }
        }
        this.$set(this.checkBoxAttributeList[index], 'selectValue', newV);
        this.$forceUpdate();
      },
      submitForm(){
        this.searchDrawer=false
        this.searchByCate();
      },
  },
};
</script>

<style>
.custom-tooltip-width{
  width: 600px !important;
  max-height: 300px !important;
  overflow: auto;
  border: none !important;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1) !important;
  background: #fff !important;
  border-radius: 12px !important;
  padding: 12px !important;
  font-size: 14px !important;
}
.custom-tooltip-text-label{
  font-size: 14px;
  color: #000;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
}
</style>
<style scoped lang="scss">
.playSearch_tit {
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  min-width: 100px;
  line-height: normal;
  letter-spacing: 0.8px;
}
.add-help {
  float: right;
  margin: 20px 20px 20px 0;
}
.opt-item {
  cursor: pointer;
  padding: 0px 22px;
  background: #f6f6f6;
  border-radius: 20px;
  margin-right: 12px;
  margin-bottom: 8px;
  transition: all 0.3s;
  line-height: 30px !important;
  height: 36px !important;
  flex-shrink: 0;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
  display: flex;
  align-items: center;

  &.active {
    background: #fff4ee;
    color: #ff6716;
    border-color: #fff4ee;
  }
}
.keyword_box {
  // margin: 0 0 20px 0;
  padding-bottom: 20px;
  .search_keyword {
    width: 265px;
    margin-right: 10px;

    /deep/ .el-input__inner {
      border-radius: 50px !important;
    }
  }
}

.flexWrap {
  flex-wrap: wrap;
}
.playSearch_tit {
  flex-shrink: 0;
  width: 110px;
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
}
.goodsItem_pic_img_box{
  width: 80px;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  font-family: PingFang Sc;
  border-radius: 12px;
display: none;
  top: 0px;
  left: 0px;
}
.goodsItem_pic_img{
  position: relative;
  border-radius: 12px;
}
.goodsItem_pic_img:hover  .goodsItem_pic_img_box {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  
}
.search_dzTitle {
  font-size: 18px;
  font-family: 'PingFang SC';
  font-weight: 400;
  color: #000;
}
.labelTitle {
  font-size: 16px;
  width: 120px;
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
}
.sort_item {
  margin-right: 20px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  margin-top: 10px;
}

</style>
