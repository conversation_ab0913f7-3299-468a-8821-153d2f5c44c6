<template>
  <div class="app-container">
    <!-- <el-card class="operate-container" shadow="never">
      <i class="el-icon-tickets" style="margin-top: 5px" />
      <span style="margin-top: 5px">数据列表</span>
      <el-button class="btn-add" size="mini" @click="handleAddProductCate()">
        添加
      </el-button>
    </el-card> -->
    <el-form
      style="width: 100%; margin-bottom: -20px"
      :inline="true"
      :model="listQuery"
      size="small"
      label-width="100px"
    >
      <el-form-item label="商品编号：">
        <el-input
          v-model="listQuery.productSn"
          class="input-width"
          placeholder="请输入商品编号"
          clearable
        ></el-input>
      </el-form-item>
      <!-- negoStatus -->
      <el-form-item label="游戏分类：">
        <el-select
          v-model="listQuery.productCategoryNames"
          placeholder="请选择选择"
          multiple
          collapse-tags
        >
          <el-option
            v-for="item in productCategoryNamesList"
            :key="item.name"
            :label="item.name"
            :value="item.name"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="线索状态：">
        <el-select
          v-model="listQuery.status"
          placeholder="请选择状态"
          multiple
          collapse-tags
        >
          <el-option
            v-for="item in statusType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品来源：">
        <el-select
          v-model="listQuery.sourceType"
          placeholder="请选择商品来源"
        >
          <el-option
            v-for="item in sourceTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button
          style="float: right"
          @click="handleResetSearch()"
          size="small"
        >
          重置
        </el-button>
        <el-button
          style="margin-right: 15px"
          type="primary"
          @click="handleSearchList()"
          size="small"
        >
          查询搜索
        </el-button>
      </el-form-item>
      <el-button
        class="btn-add"
        size="small"
        style="float: right"
        @click="handleAddProductCate()"
      >
        添加
      </el-button>
    </el-form>
    <!-- v-if="list && list.length > 0" -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        ref="productCateTable"
        :data="list"
        style="width: 100%"
        border
      >
      <el-table-column width="100" label="封面图" align="center">
          <template slot-scope="scope">
            <!-- <img
              style="width: 60px; height: 60px"
              :src="scope.row.pic"
              alt=""
            /> -->
            <div class="goodsItem_pic_img">
              <img
                style="width: 80px; height: 80px; border-radius: 12px"
                :src="scope.row.pic"
                alt=""
              />
              <div
                @click.stop="showImagePriview(scope.row)"
                class="goodsItem_pic_img_box"
              >
                预览
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="编号" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.id }}</template>
        </el-table-column>
        <el-table-column label="店主ID" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.leadMemberId }}</template>
        </el-table-column> -->
        <!-- <el-table-column label="号主IM" width="100" align="center">
          <template slot-scope="scope">{{
            scope.row.productMemberIm
          }}</template>
        </el-table-column> -->
        <el-table-column label="商品编号" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.productSn }}</template>
        </el-table-column>
        <el-table-column label="商品状态" width="100" align="center">
          <template slot-scope="scope">{{
            productStatus[scope.row.productStatus]
          }}</template>
        </el-table-column>
        <el-table-column label="线索状态" width="100" align="center">
          <template slot-scope="scope">{{
            productLeadStatus[scope.row.status]
          }}</template>
        </el-table-column>
        <el-table-column label="商品来源" width="100" align="center">
          <template slot-scope="scope">{{
            sourceTypeName[scope.row.sourceType]
          }}</template>
        </el-table-column>
        
        <!-- <el-table-column label="商品分类ID" width="150" align="center">
          <template slot-scope="scope">{{
            scope.row.productCategoryId
          }}</template>
        </el-table-column> -->
        <el-table-column label="商品分类名称" width="120" align="center">
          <template slot-scope="scope">{{
            scope.row.productCategoryName
          }}</template>
        </el-table-column>
        <el-table-column label="底价" width="100" align="center">
          <template slot-scope="scope">¥{{ scope.row.originalPrice }}</template>
        </el-table-column>
        <el-table-column label="现价" width="100" align="center">
          <template slot-scope="scope">¥{{ scope.row.price }}</template>
        </el-table-column>
        <el-table-column label="商品标题" min-width="200" align="center">
          <template slot-scope="scope">
            <!-- <div :title="scope.row.subTitle" class="text_linTwo">
              {{ scope.row.subTitle }}
            </div> -->
            <el-tooltip
                class="tooltipItemBox"
                effect="light"
                :content="scope.row.subTitle "
                placement="top"
                popper-class="custom-tooltip-width"
              >
                <div class="text_linThree custom-tooltip-text-label">{{ scope.row.subTitle }}</div>
              </el-tooltip>
          </template>
        </el-table-column>
        
        <!-- <el-table-column label="线索类型" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.type }}</template>
        </el-table-column> -->
     
        <el-table-column label="备注" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.note }}</template>
        </el-table-column>
        <el-table-column label="线索更新时间" width="180" align="center">
          <template slot-scope="scope">{{
            formatTime(scope.row.updatedAt)
          }}</template>
        </el-table-column>
        <!-- <el-table-column label="分类名称" align="center">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column> -->
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template slot-scope="scope">
            <!-- <el-button
              size="mini"
              @click="handleMsg(scope.$index, scope.row)"
              >进入会话
            </el-button> -->
            <el-button
              size="mini"
              @click="handleUpdate(scope.$index, scope.row)"
              >编辑状态
            </el-button>
            <!-- <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              >删除
            </el-button> -->
            <el-button
              size="mini"
              @click="handleBuyingNext(scope.$index, scope.row)"
              >查看商品
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="list && list.length > 0" class="pagination-container">
      <el-pagination
        :page-size="listQuery.pageSize"
        :page-sizes="[20, 40, 60]"
        :current-page.sync="listQuery.pageNum"
        :total="total"
        background
        layout="total, sizes,prev, pager, next,jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :title="dialogFormVisibleTitle"
      :visible.sync="dialogFormVisible"
      width="1000px"
    >
      <el-form label-width="100px" ref="form" :model="formValue">
        <el-form-item label="商品编号：">
          <el-input
            :disabled="dialogFormVisibleTitle == '编辑'"
            @change="searchProduct"
            style="width: 300px"
            v-model="formValue.productSn"
          ></el-input>
        </el-form-item>
        <el-table
          v-if="productList.length > 0"
          :data="productList"
          style="width: 100%; margin-bottom: 20px"
        >
          <el-table-column
            align="center"
            prop="productSn"
            label="商品编号"
            width="150"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="productCategoryName"
            label="分类名称"
            width="150"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="productCategoryName"
            label="商品图"
            width="120"
            style="padding: 0px"
          >
            <template style="padding: 0px" slot-scope="scope">
              <img style="width: 100%" :src="scope.row.pic" alt="" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="price" label="单价" width="100">
          </el-table-column>
          <!-- <el-table-column
            align="center"
            prop="originalPrice"
            label="底价"
            width="100"
          >
          </el-table-column> -->
          <el-table-column
            prop="productCategoryName"
            label="商品标题"
            align="center"
          >
            <template slot-scope="scope">
              <el-tooltip
                class="tooltipItemBox"
                effect="light"
                :content="getWzText(attrValueList,scope.row.name,scope.row)"
                placement="top"
                popper-class="custom-tooltip-width"
              >
                <div class="text_linThree custom-tooltip-text-label">{{ getWzText(attrValueList,scope.row.name,scope.row) }}</div>
              </el-tooltip>
              <!-- <div :title="scope.row.detailTitle" class="text_linTwo">
                {{ scope.row.detailTitle }}
              </div> -->
            </template>
          </el-table-column>
        </el-table>
        <el-form-item label="线索状态：">
          <el-select
            style="width: 300px"
            v-model="formValue.status"
            placeholder="请选择线索状态"
          >
            <el-option
              v-for="item in statusType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            type="textarea"
            style="width: 300px"
            v-model="formValue.note"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cloneBtn">取 消</el-button>
        <el-button
          :disabled="productList.length > 0 ? false : true"
          type="primary"
          @click="submitBtn"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <swperImagePriviewPage ref="swperImagePriviewPage" />
  </div>
</template>

<script>
import {
  productLeadList,
  productListAll,
  productLeadCreate,
  productLeadDelete,
  productLeadUpdate,
  getProductDetail,
  kkProductCategoryList,
} from '@/api/bmc';
import { GOOD_TITLE, GOOD_TITLE_OTHER } from '@/utils/const.js';
import moment from 'moment';

import swperImagePriviewPage from '@/components/imagePriview/page.vue';
export default {
  name: 'ProductCateList',
  components: {
    swperImagePriviewPage,
  },
  data() {
    return {
      productList: [],
      productCategoryNamesList: [],
      list: null,
      dialogFormVisibleTitle: '',
      dialogFormVisible: false,
      total: null,
      productCategoryList: [],
      listLoading: true,
      productStatus: {
        'ON_SALE': '在售',
        'DOWN_SHELF': '下架/删除', //下架(包括下架、删除)
        'SOLD': '已售', //已售
        'UNKNOWN': '未知', //未知状态
      },
      sourceTypeName:{
        0:'网站发布',
        2:'号商发布'
      },
      sourceTypeList:[
        {
          value:0,
          label:'网站发布'
        },{
          value:2,
          label:'号商发布'
        }
      ],
      productLeadStatus: {
        'PENDING': '待处理',
        'CONTACTED': '已联系',
        'NEGOTIATING': '议价中',
        'SCHEDULED': '交易中',
        'COMPLETED': '已完成',
        'INVALID': '无效线索',
        "CANCELLED":"已取消",
        'EXPIRED': '已过期',
      },
      statusType: [
        {
          label: '待处理',
          value: 'PENDING',
        },
        {
          label: '已联系',
          value: 'CONTACTED',
        },
        {
          label: '议价中',
          value: 'NEGOTIATING',
        },
        {
          label: '交易中',
          value: 'SCHEDULED',
        },
        {
          label: '已完成',
          value: 'COMPLETED',
        },
        {
          label: '无效线索',
          value: 'INVALID',
        },
        {
          label: '已取消',
          value: 'CANCELLED',
        },
        {
          label: '已过期',
          value: 'EXPIRED',
        },
      ],
      productUnitList: [
        {
          id: 1,
          name: 'NEW',
        },
        {
          id: 2,
          name: 'HOT',
        },
        {
          id: 0,
          name: '无',
        },
      ],
      formValue: {
        productSn: '',
        note: '',
        type: 'RECYCLED',
        status: '',
      },
      attrValueList:[],
      listQuery: {
        pageNum: 1,
        pageSize: 20,
        type: 'RECYCLED',
        productSn: '',
        status: [],
        productCategoryNames: [],
      },
      parentId: 0,
    };
  },
  watch: {},
  created() {
    this.getList();
    this.getKkProductCategoryList();
  },
  mounted() {
    // productListAll({ pageNum: 1, pageSize: 1000 }).then((res) => {
    //   this.productCategoryList = res.data.list;
    // });
  },
  methods: {
    getWzText(attrValueList,name,row) {
      
      if(row.productCategoryId!=82){
        return name
      }
      const newList = [...attrValueList].map((item) => ({
        name: item.name || item.productAttributeName,
        values: item.values || item.value.split(','),
      }));
      const titleList = GOOD_TITLE.concat(GOOD_TITLE_OTHER);
      const filterList = newList
        .filter((v) => {
          if (
            titleList.map((k) => k.label).includes(v.name) &&
            v.values &&
            v.values.length &&
            v.values[0] !== '0'
          ) {
            this.$set(
              v,
              'index',
              titleList.find((i) => i.label === v.name).index,
            );
            return v;
          }
        })
        .sort((a, b) => a.index - b.index);
      const titleInfo = filterList.map((v) => {
        let newName;
        if (v.name === '贵族等级') {
          newName = v.values[0].replace('贵族', 'V');
        } else if (GOOD_TITLE_OTHER.map((k) => k.label).includes(v.name)) {
          newName = v.values.length;
        } else {
          newName = v.values[0];
        }
        return (
          newName + titleList.find((i) => i.label === v.name).children.label
        );
      });
      return titleInfo.join(' ')+name;
    },
    formatTime(time) {
      time = new Date(time);
      return moment(time).format('YYYY-MM-DD HH:mm:ss');
    },
    showImagePriview(item) {
      this.$refs.swperImagePriviewPage.showImagePriview(item);
     
     
    },
    getKkProductCategoryList() {
      kkProductCategoryList({ pageNum: 1, pageSize: 100 }).then((res) => {
        this.productCategoryNamesList = res.data.list;
      });
    },
    handleMsg(index, row) {
      // const { store } = window.__xkit_store__;
      // console.log(store,2222)

      // return
      let productMemberIm = row.productMemberIm;
      const sessionId = `${productMemberIm}`;

      this.$router.push({
        path: `/productKF/imList?sessionId=${sessionId}`,
      });
    },
    searchProduct() {
      if (!this.formValue.productSn) {
        this.productList = [];
        return;
      }
      getProductDetail({ productSn: this.formValue.productSn }).then((res) => {
        if (res.data && res.data.product) {
          let arr = [];
          arr.push(res.data.product);
          this.productList = arr;
        }
        this.attrValueList=res.data.productAttributeValueList
      });
      //   let params = {
      //     pageNum: 1,
      //     pageSize: 20,
      //     productSn: this.formValue.productSn,
      //     productStatus: 'ON_SHELF',
      //   };

      //   getProductList(params).then((res) => {
      //     if (res.code == 200) {
      //       this.productList = res.data.list;
      //     }
      //   });
    },
    cloneBtn() {
      this.dialogFormVisible = false;
    },
    submitBtn() {
      this.$refs.form.validate((valid) => {
        let params={
          ...this.formValue,
          subTitle:this.getWzText(this.attrValueList,this.productList[0].name,this.productList[0]),
        }
        if (valid) {
          if (this.dialogFormVisibleTitle == '新增') {
            productLeadCreate(params).then((res) => {
              if (res.code != 200) return;
              this.$message.success('添加成功');
              this.dialogFormVisible = false;
              this.getList();
            });
          } else {
            productLeadUpdate(params).then((res) => {
              if (res.code != 200) return;
              this.$message.success('编辑成功');
              this.dialogFormVisible = false;
              this.getList();
            });
          }
        }
      });
    },
    // validatePositiveInteger(rule, value, callback) {
    //     if (!value) {
    //         return callback(new Error('请输入排序'));
    //     }
    //     const num = Number(value);
    //     if (Number.isInteger(num) && num > 0) {
    //         // 验证成功
    //         callback();
    //     } else {
    //         // 验证失败
    //         callback(new Error('请输入一个有效的正整数'));
    //     }
    // },
    handleBuyingNext(index, row) {
      window.open(`https://www.yokoye.com/gd/${row.productSn}`, '_blank');
    },
    handleAddProductCate() {
      console.log('开始执行 handleAddProductCate 函数');
      this.dialogFormVisibleTitle = '新增';
      this.productList = [];
      (this.formValue = {
        productSn: '',
        note: '',
        type: 'RECYCLED',
        status: '',
      }),
        (this.dialogFormVisible = true);
    },
    handleSearchList() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    handleResetSearch() {
      (this.listQuery.productSn = ''),
      (this.listQuery.sourceType=''),
        (this.listQuery.status = []),
        (this.listQuery.pageNum = 1);
      this.listQuery.productCategoryNames = [];
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let params = {
        ...this.listQuery,
      };
      if (params.status.length) {
        params.status = params.status;
      } else {
        delete params.status;
      }
      if (params.productCategoryNames.length) {
        params.productCategoryNames = params.productCategoryNames;
      } else {
        delete params.productCategoryNames;
      }
      console.log(params);
      productLeadList(params).then((response) => {
        this.listLoading = false;
        this.list = response.data.list;
        this.total = response.data.total;
      });
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1;
      this.listQuery.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getList();
    },
    handleUpdate(index, row) {
      if(row.productStatus=="DOWN_SHELF"){
        this.$message.warning('当前商品已下架，不可编辑');
        return
      }
      this.dialogFormVisibleTitle = '编辑';
      //   this.productList=[]

      this.formValue = JSON.parse(JSON.stringify(row));
      this.formValue.type = 'RECYCLED';
      // this.formValue.productCategoryId=row.id
      this.searchProduct();
      this.dialogFormVisible = true;
    },
    handleDelete(index, row) {
      this.$confirm('是否要删除该分类', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        productLeadDelete({ id: row.id, type: 'RECYCLED' }).then((response) => {
          this.$message({
            message: '删除成功',
            type: 'success',
            duration: 1000,
          });
          this.getList();
        });
      });
    },
  },
};
</script>
<style>
.custom-tooltip-width{
  width: 600px !important;
  max-height: 300px !important;
  overflow: auto;
  border: none !important;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1) !important;
  background: #fff !important;
  border-radius: 12px !important;
  padding: 12px !important;
  font-size: 14px !important;
}
.custom-tooltip-text-label{
  font-size: 14px;
  color: #000;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
}
</style>
<style scoped>
.goodsItem_pic_img_box {
  width: 80px;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  font-family: PingFang Sc;
  border-radius: 12px;
  display: none;
  top: 0px;
  left: 0px;
}
.goodsItem_pic_img {
  position: relative;
  border-radius: 12px;
}
.goodsItem_pic_img:hover .goodsItem_pic_img_box {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
