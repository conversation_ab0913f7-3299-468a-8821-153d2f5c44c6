<template>
  <div class="formula-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <div class="search-form">
          <el-form
            :model="searchForm"
            :inline="true"
            class="search-form-inline"
          >
            <el-form-item label="游戏">
              <el-select
                v-model="searchForm.game"
                placeholder="请选择游戏"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="game in gameOptions"
                  :key="game.value"
                  :label="game.label"
                  :value="game.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="公式名称">
              <el-input
                v-model="searchForm.formulaName"
                placeholder="请输入公式名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="success" @click="handleAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <el-table
          :data="tableData"
          v-loading="loading"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
          />

          <el-table-column
            prop="game"
            label="游戏"
            width="120"
            align="center"
          />

          <el-table-column
            prop="formulaName"
            label="公式名称"
            width="150"
            align="center"
          />

          <el-table-column
            prop="formula"
            label="公式"
            min-width="200"
            align="center"
            show-overflow-tooltip
          />

          <el-table-column
            prop="remark"
            label="备注"
            width="150"
            align="center"
            show-overflow-tooltip
          />

          <el-table-column
            label="操作"
            width="160"
            align="center"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(scope.row)"
              >
                修改
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/修改公式对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleDialogClose"
      class="formula-dialog"
    >
      <el-form
        :model="formulaForm"
        :rules="formulaRules"
        ref="formulaForm"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="游戏" prop="game">
              <el-select
                v-model="formulaForm.game"
                placeholder="请选择游戏"
                style="width: 100%"
              >
                <el-option
                  v-for="game in gameOptions"
                  :key="game.value"
                  :label="game.label"
                  :value="game.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公式名称" prop="formulaName">
              <el-input
                v-model="formulaForm.formulaName"
                placeholder="请输入公式名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 可选属性区域 -->
        <el-form-item label="可选属性">
          <div class="attribute-section">
            <div class="attribute-group">
              <span class="group-title">可选属性</span>
              <div class="attribute-buttons">
                <el-button
                  v-for="attr in attributes"
                  :key="attr"
                  size="small"
                  @click="insertToFormula(attr)"
                >
                  {{ attr }}
                </el-button>
              </div>
            </div>

            <div class="attribute-group">
              <span class="group-title">装备属性</span>
              <div class="attribute-buttons">
                <el-button
                  v-for="equip in equipments"
                  :key="equip"
                  size="small"
                  @click="insertToFormula(equip)"
                >
                  {{ equip }}
                </el-button>
              </div>
            </div>

            <div class="attribute-group">
              <span class="group-title">更多属性</span>
              <div class="attribute-buttons">
                <el-button
                  v-for="more in moreAttributes"
                  :key="more"
                  size="small"
                  @click="insertToFormula(more)"
                >
                  {{ more }}
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 运算符号 -->
        <el-form-item label="运算符号">
          <div class="operator-buttons">
            <el-button
              v-for="op in operators"
              :key="op.symbol"
              :type="op.type"
              size="small"
              @click="insertToFormula(op.symbol)"
            >
              {{ op.label }}
            </el-button>
          </div>
        </el-form-item>

        <!-- 公式输入框 -->
        <el-form-item label="公式" prop="formula">
          <el-input
            v-model="formulaForm.formula"
            type="textarea"
            :rows="4"
            placeholder="请输入公式或点击上方按钮插入"
            ref="formulaInput"
          />
        </el-form-item>

        <!-- 备注 -->
        <el-form-item label="备注">
          <el-input
            v-model="formulaForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>

        <!-- 说明文字 -->
        <el-form-item>
          <div class="formula-tips">
            <p style="color: #f56c6c; margin: 0">
              公式基本配置完成，系统将根据输入的参数进行运算，请确保公式的正确性和完整性，
              公式不支持的符号请勿使用，以免影响正常使用。
            </p>
          </div>
        </el-form-item>

        <!-- 测试表格 -->
        <el-form-item label="测试数据">
          <el-table :data="testData" border size="small">
            <el-table-column prop="name" label="名称" width="100" />
            <el-table-column prop="qq" label="QQ号" width="100" />
            <el-table-column prop="region" label="大区" width="100" />
            <el-table-column prop="service" label="服务器" width="100" />
            <el-table-column prop="level" label="区服等级" width="100" />
            <el-table-column prop="price" label="价格" width="80" />
            <el-table-column label="操作" width="80">
              <template>
                <el-button type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 10px">
            <el-button size="small">+</el-button>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Formula',
  data() {
    return {
      // 搜索表单
      searchForm: {
        game: '',
        formulaName: '',
      },

      // 游戏选项
      gameOptions: [
        { label: '王者荣耀', value: 'wzry' },
        { label: '和平精英', value: 'hpjy' },
        { label: '英雄联盟', value: 'yxlm' },
        { label: '原神', value: 'ys' },
        { label: '崩坏3', value: 'bh3' },
      ],

      // 表格数据
      tableData: [],
      loading: false,

      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 对话框
      dialogVisible: false,
      dialogTitle: '新增公式',
      isEdit: false,

      // 公式表单
      formulaForm: {
        id: '',
        game: '',
        formulaName: '',
        formula: '',
        remark: '',
      },

      // 表单验证规则
      formulaRules: {
        game: [{ required: true, message: '请选择游戏', trigger: 'change' }],
        formulaName: [
          { required: true, message: '请输入公式名称', trigger: 'blur' },
        ],
        formula: [{ required: true, message: '请输入公式', trigger: 'blur' }],
      },

      // 可选属性
      attributes: [
        '攻击属性',
        '数值属性',
        '防御属性',
        '装备评分',
        '系统评级',
        '作战能力',
      ],
      equipments: ['装备属性', '配件', '主手装备', '副手装备'],
      moreAttributes: [
        '打野属性',
        '丁字属性',
        '中路属性',
        '发育属性',
        '辅助属性',
        '射手属性',
        '战士属性',
        '法师属性',
        '坦克属性',
        '刺客属性',
        '支援属性',
        '控制属性',
        '爆发属性',
        '持续属性',
      ],

      // 运算符号
      operators: [
        { symbol: '+', label: '+', type: 'primary' },
        { symbol: '-', label: '-', type: 'primary' },
        { symbol: '*', label: '×', type: 'success' },
        { symbol: '/', label: '÷', type: 'warning' },
        { symbol: '(', label: '(', type: 'info' },
        { symbol: ')', label: ')', type: 'info' },
        { symbol: '=', label: '=', type: 'danger' },
      ],

      // 测试数据
      testData: [
        {
          name: '测试',
          qq: 'QQ',
          region: '手Q大区',
          service: '手Q的服务器',
          level: '1.00',
          price: '',
        },
      ],
    };
  },

  mounted() {
    this.loadTableData();
  },

  methods: {
    // 加载表格数据
    loadTableData() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        this.tableData = this.generateMockData();
        this.pagination.total = 25;
        this.loading = false;
      }, 500);
    },

    // 生成模拟数据
    generateMockData() {
      const mockData = [];
      const games = ['王者荣耀', '和平精英', '英雄联盟', '原神'];
      const formulas = [
        '攻击力计算',
        '防御力评估',
        '综合评分',
        '装备评级',
        '战力计算',
      ];

      for (let i = 1; i <= 10; i++) {
        mockData.push({
          id: i,
          game: games[Math.floor(Math.random() * games.length)],
          formulaName: formulas[Math.floor(Math.random() * formulas.length)],
          formula: `攻击属性 * 0.8 + 防御属性 * 0.2 + 装备评分 * 0.5`,
          remark: `公式${i}的备注说明`,
        });
      }

      return mockData;
    },

    // 搜索
    handleSearch() {
      console.log('搜索条件:', this.searchForm);
      this.pagination.currentPage = 1;
      this.loadTableData();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        game: '',
        formulaName: '',
      };
      this.pagination.currentPage = 1;
      this.loadTableData();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增公式';
      this.isEdit = false;
      this.formulaForm = {
        id: '',
        game: '',
        formulaName: '',
        formula: '',
        remark: '',
      };
      this.dialogVisible = true;
    },

    // 修改
    handleEdit(row) {
      this.dialogTitle = '修改公式';
      this.isEdit = true;
      this.formulaForm = {
        id: row.id,
        game: row.game,
        formulaName: row.formulaName,
        formula: row.formula,
        remark: row.remark,
      };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除公式 "${row.formulaName}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$message.success('删除成功');
          this.loadTableData();
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 插入到公式
    insertToFormula(text) {
      const textarea = this.$refs.formulaInput.$refs.textarea;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const value = this.formulaForm.formula;

      this.formulaForm.formula =
        value.substring(0, start) + text + value.substring(end);

      // 设置光标位置
      this.$nextTick(() => {
        textarea.focus();
        textarea.setSelectionRange(start + text.length, start + text.length);
      });
    },

    // 保存
    handleSave() {
      this.$refs.formulaForm.validate((valid) => {
        if (valid) {
          const action = this.isEdit ? '修改' : '新增';
          console.log(`${action}公式:`, this.formulaForm);
          this.$message.success(`${action}成功`);
          this.dialogVisible = false;
          this.loadTableData();
        }
      });
    },

    // 关闭对话框
    handleDialogClose() {
      this.$refs.formulaForm.resetFields();
      this.dialogVisible = false;
    },

    // 分页相关
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadTableData();
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadTableData();
    },
  },
};
</script>

<style scoped>
.formula-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-section {
  margin-bottom: 20px;
}

.search-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form-inline {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.table-section {
  margin-bottom: 20px;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 公式对话框样式 */
.formula-dialog .el-dialog__body {
  padding: 20px;
}

.attribute-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.attribute-group {
  margin-bottom: 15px;
}

.attribute-group:last-child {
  margin-bottom: 0;
}

.group-title {
  display: inline-block;
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
  font-size: 14px;
}

.attribute-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attribute-buttons .el-button {
  margin: 0;
  font-size: 12px;
  padding: 5px 10px;
}

.operator-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.operator-buttons .el-button {
  margin: 0;
  min-width: 40px;
  font-weight: 600;
}

.formula-tips {
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 12px;
  margin-top: 10px;
}

.dialog-footer {
  text-align: right;
}

/* 测试表格样式 */
.el-table--small .el-table__cell {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .formula-container {
    padding: 10px;
  }

  .search-form-inline {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form-inline .el-form-item {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .attribute-buttons {
    justify-content: flex-start;
  }

  .operator-buttons {
    justify-content: flex-start;
  }
}

/* 公式输入框样式 */
.el-textarea__inner {
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

/* 按钮hover效果 */
.attribute-buttons .el-button:hover,
.operator-buttons .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格行hover效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa !important;
}
</style>
