<template>
  <div class="setting-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <div class="search-form">
          <el-form
            :model="searchForm"
            :inline="true"
            class="search-form-inline"
          >
            <el-form-item label="游戏名称">
              <el-select
                v-model="searchForm.gameName"
                placeholder="请选择游戏名称"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="game in gameOptions"
                  :key="game.value"
                  :label="game.label"
                  :value="game.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="字段名称">
              <el-input
                v-model="searchForm.fieldName"
                placeholder="请输入字段名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <el-table
          :data="tableData"
          v-loading="loading"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column
            prop="systemId"
            label="系统编号"
            width="120"
            align="center"
          />

          <el-table-column
            prop="gameName"
            label="游戏名称"
            width="150"
            align="center"
          />

          <el-table-column
            prop="parentField"
            label="上级字段"
            width="150"
            align="center"
          />

          <el-table-column
            prop="fieldName"
            label="字段名称"
            width="150"
            align="center"
          />

          <el-table-column
            prop="estimate"
            label="估价"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text">¥{{ scope.row.estimate }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="200"
            align="center"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(scope.row)"
              >
                修改
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          />
        </div>
      </el-card>
    </div>

    <!-- 修改估价对话框 -->
    <el-dialog
      title="修改估价"
      :visible.sync="editDialogVisible"
      width="400px"
      :before-close="handleDialogClose"
    >
      <el-form
        :model="editForm"
        :rules="editRules"
        ref="editForm"
        label-width="80px"
      >
        <el-form-item label="系统编号">
          <el-input v-model="editForm.systemId" disabled />
        </el-form-item>

        <el-form-item label="游戏名称">
          <el-input v-model="editForm.gameName" disabled />
        </el-form-item>

        <el-form-item label="字段名称">
          <el-input v-model="editForm.fieldName" disabled />
        </el-form-item>

        <el-form-item label="估价" prop="estimate">
          <el-input-number
            v-model="editForm.estimate"
            :precision="2"
            :step="0.01"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Setting',
  data() {
    return {
      // 搜索表单
      searchForm: {
        gameName: '',
        fieldName: '',
      },

      // 游戏选项
      gameOptions: [
        { label: '王者荣耀', value: 'wzry' },
        { label: '和平精英', value: 'hpjy' },
        { label: '英雄联盟', value: 'yxlm' },
        { label: '原神', value: 'ys' },
        { label: '崩坏3', value: 'bh3' },
        { label: '明日方舟', value: 'mrfz' },
      ],

      // 表格数据
      tableData: [],
      loading: false,

      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 编辑对话框
      editDialogVisible: false,
      editForm: {
        id: '',
        systemId: '',
        gameName: '',
        fieldName: '',
        estimate: 0,
      },

      // 编辑表单验证规则
      editRules: {
        estimate: [
          { required: true, message: '请输入估价', trigger: 'blur' },
          { type: 'number', min: 0, message: '估价不能小于0', trigger: 'blur' },
        ],
      },
    };
  },

  mounted() {
    this.loadTableData();
  },

  methods: {
    // 加载表格数据
    loadTableData() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        this.tableData = this.generateMockData();
        this.pagination.total = 50; // 模拟总数
        this.loading = false;
      }, 500);
    },

    // 生成模拟数据
    generateMockData() {
      const mockData = [];
      const games = ['王者荣耀', '和平精英', '英雄联盟', '原神', '崩坏3'];
      const parentFields = ['角色属性', '装备属性', '技能属性', '道具属性'];
      const fieldNames = [
        '攻击力',
        '防御力',
        '生命值',
        '法术强度',
        '暴击率',
        '移速',
      ];

      for (let i = 1; i <= 10; i++) {
        mockData.push({
          id: i,
          systemId: `SYS${String(i).padStart(6, '0')}`,
          gameName: games[Math.floor(Math.random() * games.length)],
          parentField:
            parentFields[Math.floor(Math.random() * parentFields.length)],
          fieldName: fieldNames[Math.floor(Math.random() * fieldNames.length)],
          estimate: (Math.random() * 1000 + 100).toFixed(2),
        });
      }

      return mockData;
    },

    // 搜索
    handleSearch() {
      console.log('搜索条件:', this.searchForm);
      this.pagination.currentPage = 1;
      this.loadTableData();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        gameName: '',
        fieldName: '',
      };
      this.pagination.currentPage = 1;
      this.loadTableData();
    },

    // 修改
    handleEdit(row) {
      this.editForm = {
        id: row.id,
        systemId: row.systemId,
        gameName: row.gameName,
        fieldName: row.fieldName,
        estimate: parseFloat(row.estimate),
      };
      this.editDialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除系统编号为 ${row.systemId} 的记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 模拟删除API调用
          this.$message.success('删除成功');
          this.loadTableData();
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 保存修改
    handleSaveEdit() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          // 模拟保存API调用
          console.log('保存修改:', this.editForm);
          this.$message.success('修改成功');
          this.editDialogVisible = false;
          this.loadTableData();
        }
      });
    },

    // 关闭对话框
    handleDialogClose() {
      this.$refs.editForm.resetFields();
      this.editDialogVisible = false;
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadTableData();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadTableData();
    },
  },
};
</script>

<style scoped>
.setting-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-section {
  margin-bottom: 20px;
}

.search-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form-inline {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.table-section {
  margin-bottom: 20px;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price-text {
  color: #f56c6c;
  font-weight: 600;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .setting-container {
    padding: 10px;
  }

  .search-form-inline {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form-inline .el-form-item {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
