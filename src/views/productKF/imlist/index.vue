<template>
  <div>
    <div class="top_box spaceStart">
      <div
        class="item jq"
        :class="barclazz[key] && barclazz[key].clazz"
        v-for="(value, key) in topbar"
        :key="key"
      >
        {{ barclazz[key] ? barclazz[key].name : '' }}:{{ value }}
      </div>
    </div>

    <div class="kf-im-box">
      <div class="im-box" id="im-box">
        <IMApp
          ref="imapp"
          v-if="uikitInit"
          :flowId="flowId"
          :tools="tools"
          :sidim="sidim"
          :bidim="bidim"
          :orderDetail="orderDetail"
          @isTeamChange="isTeamChange"
          @getOrderDetail="getOrderDetail"
          @doRecordCard="doRecordCard"
          @p2pchange="p2pchange"
          @toolsClick="toolsClick"
          @getImAccount="getImAccount"
          @changeTtp="changeTtp"
        />
      </div>
      <div v-if="orderImAccount&&!isTtp" :key="refreshRecordCard" class="im-box-right record">
        <!-- <recordCard ref="recordCard"></recordCard> -->
        <recordCard2 ref="recordCardTwo" :orderImAccount="orderImAccount"></recordCard2>
      </div>
      <!-- <div v-if="CanShowRight" class="im-box-right">
        <div>
          <actionCard
            :flowId="flowId"
            :teamInfo="teamInfo"
            :teamPnote="teamPnote"
            :teamNote="teamNote"
            :bidim="bidim"
            :sidim="sidim"
            @doSendBaopeiMsg2Team="doSendBaopeiMsg2Team"
            @doRefresh="doRefresh"
          ></actionCard>
          <orderCard
            v-if="hasOrderDetail"
            :orderDetail="orderDetail"
          ></orderCard>
          <div class="empty emptyone" v-else>
            <p>暂无订单信息</p>
          </div>
          <stepCard v-if="hasFlow" :flow="flow"></stepCard>
          <div class="empty" v-else><p>暂无流程信息</p></div>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script>
import { Loading } from 'element-ui';

import _ from 'lodash';
import { getOrderDetail, getFlowState } from '@/api/order';
import actionCard from '@/components/IMCard/actionCard.vue';
import stepCard from '@/components/IMCard/stepCard.vue';
import detailCard from '@/components/IMCard/detailCard.vue';
import orderCard from '@/components/IMCard/orderCard.vue';
import IMApp from '@/components/IMApp/index.vue';
import recordCard from '@/components/IMCard/recordCard.vue';
import recordCard2 from '@/components/IMCard/recordCard2.vue';
import { IMUIKit } from '@/components/im-kit-ui';
import Vue from 'vue';
import {
  getIminfo,
  getStatics,
  getTools,
  sendBaopeiMsg2Team,
  flowTools
} from '@/api/kf.js';
import { getMemberDetail } from '@/api/member.js';
const barclazz = {
  '1': {
    name: '建群',
    clazz: 'jq'
  },
  '2': {
    name: '验号',
    clazz: 'yh'
  },
  '3': {
    name: '审核',
    clazz: 'sh'
  },
  '4': {
    name: '换绑',
    clazz: 'hb'
  },
  '5': {
    name: '成功',
    clazz: ' cg'
  }
};
export default {
  name: 'imlist',
  components: {
    IMApp,
    actionCard,
    stepCard,
    detailCard,
    orderCard,
    recordCard,
    recordCard2
  },
  data() {
    return {
      flowId: '',
      isTeam: null,
      isTtp:false,
      tools: [],
      hasFlow: false,
      hasOrderDetail: false,
      uikitInit: false,
      orderDetail: {},
      bidim: '',
      sidim: '',
      sellerId: '',
      flow: '',
      teamNote: '',
      teamPnote: '',
      teamInfo: {},
      topbar: {},
      barclazz: barclazz,
      teamId: '',
      orderId: '',
      refreshRecordCard: 0,
      orderImAccount:'',
    };
  },
  created() {
    this.initIm();
    // this.throttledGetDetailForCard = _.throttle(
    //   this.getDetailForCardImpl,
    //   5000
    // );
  },
  computed: {
    kfRight() {
      if (this.$store.getters.roles.includes('客服') && this.isTeam === false) {
        return true;
      } else {
        return false;
      }
    },
    CanShowRight() {
      // alert(this.isTeam)
      if (this.$store.getters.roles.includes('客服') && this.isTeam) {
        return true;
      } else {
        return false;
      }
    }
  },
  methods: {
    getImAccount(e){
      this.orderImAccount=e
      setTimeout(()=>{
        // isTeam
        this.$refs.recordCardTwo.initData(e,this.isTeam)
      })
      
    },
    changeTtp(e){
      this.isTtp=e
    },
    toolsClick(res) {
      const { store } = window.__xkit_store__;
      const selectedSession = store.uiStore.selectedSession;
      const tempList = selectedSession.split('-');
      const scene = tempList[0];
      if (scene === 'p2p') {
        this.tools = res.data || [];
      }
    },
    p2pchange(to) {
      this.refreshRecordCard++;
      // flowTools({
      //   memberIM: to
      // }).then(res => {
      //   if (res.code == 200) {
      //     this.tools = res.data || [];
      //   }
      // });
    },
    doRefresh() {
      const { store } = window.__xkit_store__;
      const selectedSession = store.uiStore.selectedSession;
      const tempList = selectedSession.split('-');
      const scene = tempList[0];
      tempList.shift();
      const to = tempList.join('-');
      const teamId = parseInt(to);
      let loadingInstance1 = Loading.service({ fullscreen: true });
      this.getOrderDetail(teamId);
      setTimeout(() => {
        loadingInstance1.close();
      }, 200);
    },
    doRecordCard(sn, type) {
      this.$refs.recordCard.doSearchAuto(sn, type);
    },
    isTeamChange(flag) {
      this.isTeam = flag;
      // if (!this.isTeam) {
      //   this.tools = [];
      // }
    },
    resetData() {
      this.teamId = '';
      this.hasFlow = false;
      this.tools = [];
      this.orderId = '';
      this.orderDetail = '';
      this.hasOrderDetail = false;
      this.sidim = '';
      this.bidim = '';
      this.sellerId = '';
      this.flowId = '';
      this.$store.dispatch('setSidMember', {});
      this.$store.dispatch('setBidMember', {});
      this.teamNote = '';
      this.teamPnote = '';
      this.teamInfo = {};
      this.flow = {};
    },
    doSendBaopeiMsg2Team() {
      const data = {
        flowTeamId: this.flowId,
        memberId: this.sellerId
      };
      sendBaopeiMsg2Team(data).then(res => {
        if (res.code == 200) {
        }
      });
    },
    getOrderDetail(teamId) {
      // const { orderId, bid, sid } = options;
      // this.bid = bid;
      // this.sid = sid;
      this.teamId = teamId;
      if (teamId && this.$store.getters.roles.includes('客服')) {
        // this.resetData();
        // getTools({
        //   teamId
        // }).then(res => {
        //   this.tools = res.data || [];
        // });

        getFlowState(teamId).then(res => {
          const result = res.data || {};
          this.flowId = result.id;
          this.orderId = result.orderId;
          if (this.orderId) {
            getOrderDetail(this.orderId).then(res => {
              this.orderDetail = res.data;
              this.hasOrderDetail = true;
            });
          } else {
            this.hasOrderDetail = false;
            this.orderDetail = {};
          }
          this.sidim = result.sellerim;
          this.bidim = result.buyerim;
          this.sellerId = result.sellerId;
          getMemberDetail(result.sellerId).then(res => {
            this.$store.dispatch('setSidMember', res.data);
          });
          getMemberDetail(result.buyerId).then(res => {
            this.$store.dispatch('setBidMember', res.data);
          });

          this.teamNote = result.teamNote;
          this.teamPnote = result.teamPnote;
          this.teamInfo = result;
          if (result.processFlow) {
            this.flow = JSON.parse(result.processFlow);
            this.hasFlow = true;
          }
        });
      }
    },
    // getDetailForCardImpl(sessionId) {
    //   const teamId = sessionId.replace('team-', '');
    //   this.$refs.imapp && this.$refs.imapp.getDetailForCard(teamId);
    // },
    initIm() {
   
      getIminfo().then(res => {
        const { data } = res;
        this.accid = data.accid;
        this.getStatics();

        const initOptions = {
          authType: 1,
          // appkey: '7eec32972b9d966179c5acee4c6f0141',
          appkey: 'b14909500c44a20d3b8b252fab213ca2',
          account: data.accid, // 请填写你的account
          token: data.token // 请填写你的token
        };
        const localOptions = {
          // 添加好友模式，默认需要验证
          addFriendNeedVerify: false,
          // 群组被邀请模式，默认不需要验证
          teamBeInviteMode: 'noVerify',
          // 单聊消息是否显示已读未读 默认 false
          p2pMsgReceiptVisible: true,
          // 群聊消息是否显示已读未读 默认 false
          teamMsgReceiptVisible: true,
          // 是否需要@消息 默认 true
          needMention: true,
          // 是否显示在线离线状态 默认 true
          loginStateVisible: true,
          // 是否允许转让群主
          allowTransferTeamOwner: true,
          sendMsgBefore: (options, type) => {
            const { store } = window.__xkit_store__;
            // const pushContent = getMsgContentTipByType({
            //   body: options.body,
            //   type
            // });
            const yxAitMsg = options.ext
              ? options.ext.yxAitMsg
              : { forcePushIDsList: '[]', needForcePush: false };

            // 如果是 at 消息，需要走离线强推
            const { forcePushIDsList, needForcePush } = yxAitMsg
              ? // @ts-ignore
                store.msgStore._formatExtAitToPushInfo(yxAitMsg, options.body)
              : { forcePushIDsList: '[]', needForcePush: false };

            const pushPayload = JSON.stringify({
              pushTitle: '您收到一条新的消息提醒，请注意查看'
            });

            const pushInfo = {
              needPush: true,
              needPushBadge: true,
              pushPayload,
              needForcePush,
              forcePushIDsList
            };
            options.antiSpamUsingYidun = false;
            const result = { ...options, pushInfo };
            return result;
          }
        };
        const funcOptions = {
          db: true
        };
       
        Vue.prototype.$uikit = new IMUIKit({
          initOptions,
          singleton: true,
          sdkVersion: 1,
          localOptions,
          funcOptions
        });
        if (Vue.prototype.$uikit) {
          this.uikitInit = true;
        }
        if(this.$route.query&&this.$route.query.sessionId){
          
          setTimeout(()=>{
            const { store } = window.__xkit_store__;
            const sessionId = `p2p-${this.$route.query.sessionId}`;
            if (store.sessionStore.sessions.get(sessionId)) {
              store.uiStore.selectSession(sessionId);
            } else {
              store.sessionStore.insertSessionActive('p2p', this.$route.query.sessionId);
            }
            // store.uiStore.selectSession(this.$route.query.sessionId);
          },100)
        }
      });
    },
    getStatics() {
      getStatics({
        flowIM: this.accid
      }).then(res => {
        this.topbar = res.data;
      });
    },
    pullStatics() {
      // this.siv = setInterval(() => {
      //   if (this.accid) {
      //     this.getStatics();
      //   }
      // }, 30000);
    }
  },
  beforeDestroy() {
    this.siv && clearInterval(this.siv);
    this.siv = null;
  },
  watch: {
    '$route.query.sessionId': function(newVal) {
      
      if (newVal) {
        const { store } = window.__xkit_store__;
        const sessionId = `p2p-${newVal}`;
        if (store.sessionStore.sessions.get(sessionId)) {
          store.uiStore.selectSession(sessionId);
        } else {
          store.sessionStore.insertSessionActive('p2p', newVal);
        }
      }
    }
  },
  // watch: {
  //   '$route.query': {
  //     handler(newQuery, oldQuery) {
  //       if (newQuery.sessionId) {
  //       let sessionId=newQuery.sessionId
  //       const { store } = window.__xkit_store__;
  //       if (sessionId) {
  //       store.uiStore.selectSession(sessionId);
  //     } else {
  //       store.sessionStore.insertSessionActive('p2p', sessionId);
  //     }
  //     }
  //       console.log('新的 query 参数:', newQuery);
  //     },
  //     deep: true,
  //     immediate:true
  //   }
  // },
  mounted() {
  //  if(this.$route.query&&this.$route.query.sessionId){
  //  setTimeout(()=>{
  //   const { store } = window.__xkit_store__;
  //   store.uiStore.selectSession(this.$route.query.sessionId);
  //  },100)
  //   console.log(222222111111111111);
    
  //  }
    this.pullStatics();
  }
};
</script>

<style lang="scss" scoped>
.empty {
  display: flex;
  min-height: 200px;
  justify-items: center;
  align-items: center;
  justify-content: space-around;
}
.emptyone {
  border-bottom: 1px solid #ccc;
}
.top_box {
  margin: 10px 0 10px 10px;
  .item {
    height: 32px;
    padding: 5px 16px;
    text-align: center;
    color: #fff;
    border-radius: 4px;
    background: #f5f6f7;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
    flex: none;
    margin-right: 10px;
  }
  .jq {
    background: #60cfa7;
  }
  .yh {
    background: #f9b751;
  }
  .sh {
    background: #be65d9;
  }
  .hb {
    background: #537ff4;
  }
  .cg {
    background: #e9749d;
  }
}
.kf-im-box {
  width: 1600px;
  margin: 10px 0 0 10px;
  display: flex;
  justify-content: start;
  min-height: calc(100vh - 100px);
}
.im-box {
  height: 670px;
  position: relative;
}
.im-box-right {
  width: 420px;
  height: 800px;
  border-left: 0;
  box-shadow: 0px 0px 13px #e5e5e5;
  overflow: auto;
  margin-left: 1px;
  background-color: #fff;
}
.im-box-right.record {
  width: 600px;
}
/deep/ .chat-message-input-wrap .chat-message-input-content {
  min-height: 68px;
}
// /deep/ .isMute {
//   .ant-badge-count {
//     width: 5px;
//     height: 5px;
//     overflow: hidden;
//     border-radius: 5px;
//     line-height: 2px;
//     padding: 0;
//     min-width: 5px;
//   }
// }
</style>
