<template>
  <div class="home-container">
    <!-- 左侧主要内容区域 -->
    <div class="main-content">
      <!-- 顶部三个卡片区域 -->
      <div class="top-cards-row">
        <!-- 商品卡片 -->
        <div class="top-card purple-card">
          <div class="card-header">
            <span class="card-title">商品</span>
            <span class="card-dots">...</span>
          </div>
          <div class="card-body">
            <div class="card-center-text">在售商品</div>
          </div>
        </div>

        <!-- 销售台卡片 -->
        <div class="top-card blue-card">
          <div class="card-header">
            <span class="card-title">销售台</span>
            <div class="card-dots-group">
              <span class="card-dots">...</span>
              <span class="card-dots">...</span>
              <span class="card-dots">...</span>
            </div>
          </div>
          <div class="card-body">
            <div class="card-stats-row">
              <div class="stat-column">
                <div class="stat-label">交易中</div>
              </div>
              <div class="stat-column">
                <div class="stat-label">售后中</div>
              </div>
              <div class="stat-column">
                <div class="stat-label">交易完成</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 回收台卡片 -->
        <div class="top-card purple-card">
          <div class="card-header">
            <span class="card-title">回收台</span>
            <div class="card-dots-group">
              <span class="card-dots">...</span>
              <span class="card-dots">...</span>
              <span class="card-dots">...</span>
            </div>
          </div>
          <div class="card-body">
            <div class="card-stats-row">
              <div class="stat-column">
                <div class="stat-label">交易中</div>
              </div>
              <div class="stat-column">
                <div class="stat-label">售后中</div>
              </div>
              <div class="stat-column">
                <div class="stat-label">交易完成</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据概览区域 -->
      <div class="data-overview-section">
        <div class="section-header">
          <div class="section-icon-bar"></div>
          <span class="section-title">数据概览</span>
        </div>
        <div class="overview-grid">
          <div @click="recyclingOrders" class="overview-card">
            <div class="overview-icon green-icon">
              <span class="icon-text">♻</span>
            </div>
            <span class="overview-label">回收订单</span>
          </div>
          <div @click="salesOrders" class="overview-card">
            <div class="overview-icon blue-icon">
              <span class="icon-text">📊</span>
            </div>
            <span class="overview-label">销售订单</span>
          </div>
          <div class="overview-card">
            <div class="overview-icon red-icon">
              <span class="icon-text">💰</span>
            </div>
            <span class="overview-label">日常支出</span>
          </div>
          <div class="overview-card">
            <div class="overview-icon yellow-icon">
              <span class="icon-text">💵</span>
            </div>
            <span class="overview-label">日常收入</span>
          </div>
          <div @click="fbGoods" class="overview-card">
            <div class="overview-icon purple-icon">
              <span class="icon-text">📝</span>
            </div>
            <span class="overview-label">商品发布</span>
          </div>
        </div>
      </div>

      <!-- 数据中心区域 -->
      <div class="data-center-section">
        <div class="section-header">
          <div class="section-icon-bar"></div>
          <span class="section-title">数据中心</span>
          <div class="section-actions">
            <span class="refresh-link">刷新</span>
          </div>
        </div>
        <div class="data-center-grid">
          <!-- <div class="data-item">
            <div class="data-title">用户注册</div>
            <div class="data-value">今日 ***</div>
          </div> -->
          <div class="data-item">
            <div class="data-title">活跃用户</div>
            <div class="data-value">7日内活跃 ***</div>
          </div>
          <div class="data-item">
            <div class="data-title">商品上新</div>
            <div class="data-value">今日 ***</div>
            <div class="data-value">昨日 ***</div>
          </div>
          <div class="data-item">
            <div class="data-title">在售商品</div>
            <div class="data-value">*** 个</div>
          </div>
        </div>
      </div>

      <!-- 商家互联区域 -->
      <div class="merchant-section">
        <div class="section-header">
          <div class="section-icon-bar"></div>
          <span class="section-title">商家互联</span>
        </div>
        <div class="merchant-grid">
          
          <div class="merchant-item">
            <div class="merchant-icon pink-bg">
              <span class="icon-text">🏪</span>
            </div>
            <div class="merchant-info">
              <div class="merchant-title">今日订单总数</div>
              <div class="merchant-value">0</div>
            </div>
          </div>
          <div class="merchant-item">
            <div class="merchant-icon blue-bg">
              <span class="icon-text">🏙</span>
            </div>
            <div class="merchant-info">
              <div class="merchant-title">售后处理</div>
              <div class="merchant-value">3</div>
            </div>
          </div>
          <div class="merchant-item">
            <div class="merchant-icon blue-bg">
              <span class="icon-text">💲</span>
            </div>
            <div class="merchant-info">
              <div class="merchant-title">今日销售额</div>
              <div class="merchant-value">0</div>
            </div>
          </div>
          <div class="merchant-item">
            <div class="merchant-icon orange-bg">
              <span class="icon-text">📦</span>
            </div>
            <div class="merchant-info">
              <div class="merchant-title">昨日销售额</div>
              <div class="merchant-value">17</div>
            </div>
          </div>
          
        </div>
      </div>

      <!-- 我的应用区域 -->
      <!-- <div class="my-apps-section">
        <div class="section-header">
          <div class="section-icon-bar"></div>
          <span class="section-title">我的应用</span>
          <div class="section-actions">
            <span class="more-link">更多</span>
          </div>
        </div>
      </div> -->
    </div>

    <!-- 右侧边栏 -->
    <div class="right-sidebar">
      <!-- 推广测试标签 -->
      <!-- <div class="promo-badge">推广测试</div> -->

      <!-- 用户信息卡片 -->
      <div class="user-info-card">
        <div class="user-avatar">
          <div class="avatar-circle">
            <span class="avatar-text">麦</span>
          </div>
        </div>
        <div class="user-details">
          <div class="user-name">麦麦号</div>
          <div class="user-badges">
            <span class="badge-item blue-badge">实名认证</span>
            <span class="badge-item green-badge">企业认证</span>
          </div>
          <div class="user-info-text">
            <div class="info-line">
              <span class="info-label">账户:</span>
              <span class="info-value">用户**********</span>
              <!-- <span class="info-link">已绑定: maimaihao.com</span> -->
            </div>
            <div class="info-line">
              <span class="info-label">手机号:</span>
              <span class="info-value">***********</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 账户信息 -->
      <div class="account-section">
        <div class="account-header">
          <span class="account-title">账户信息</span>
          <span class="verified-badge">已实名</span>
        </div>
        <div class="account-list">
          <div class="account-row">
            <div class="account-icon pink-bg">
              <span class="icon-text">👤</span>
            </div>
            <span class="account-label">账户资料</span>
            <span class="account-value">***</span>
          </div>
          <div class="account-row">
            <div class="account-icon green-bg">
              <span class="icon-text">💳</span>
            </div>
            <span class="account-label">账户资金</span>
          </div>
          <div class="account-row">
            <div class="account-icon purple-bg">
              <span class="icon-text">⭐</span>
            </div>
            <span class="account-label">实名等级</span>
            <span class="account-value">***</span>
          </div>
          <div class="account-row">
            <div class="account-icon orange-bg">
              <span class="icon-text">🏬</span>
            </div>
            <span class="account-label">店铺</span>
            <span class="account-value">000</span>
            <div class="account-actions">
              <span class="action-button">联系优化</span>
              <span class="action-button">优化实施</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 特办事项 -->
      <div class="special-section">
        <div class="special-header">
          <span class="special-title">特办事项</span>
        </div>
        <div class="special-grid">
          <div class="special-item">
            <div class="special-icon blue-bg">
              <span class="icon-text">📦</span>
            </div>
            <span class="special-label">闲置商品</span>
            <span class="special-count">5</span>
          </div>
          <div class="special-item">
            <div class="special-icon yellow-bg">
              <span class="icon-text">⚙</span>
            </div>
            <span class="special-label">售后处理</span>
            <span class="special-count">10</span>
          </div>
          <div class="special-item">
            <div class="special-icon orange-bg">
              <span class="icon-text">📋</span>
            </div>
            <span class="special-label">外部订单</span>
            <span class="special-count">15</span>
          </div>
          <div class="special-item">
            <div class="special-icon green-bg">
              <span class="icon-text">💬</span>
            </div>
            <span class="special-label">咨询商品</span>
            <span class="special-count">20</span>
          </div>
          <div class="special-item">
            <div class="special-icon blue-bg">
              <span class="icon-text">✓</span>
            </div>
            <span class="special-label">保单核验</span>
            <span class="special-count">25</span>
          </div>
        </div>
      </div>

      <!-- 公告通知 -->
      <div class="announcement-section">
        <div class="announcement-header">
          <span class="announcement-title">公告通知</span>
          <div class="announcement-actions">
            <span class="more-link">更多</span>
          </div>
        </div>
        <div class="announcement-body">
          <div class="announcement-banner">
            <div class="banner-content">
              <h3 class="banner-title">关于电子台同功能上线的公告</h3>
              <button class="banner-button">立即查看</button>
            </div>
            <div class="banner-illustration">
              <!-- 3D插图区域 -->
              <div class="illustration-graphic"></div>
            </div>
          </div>
          <div class="announcement-news">
            <div class="news-item">
              <span class="news-bullet">•</span>
              <span class="news-text">推广服务与系统升级公告200字</span>
            </div>
            <div class="news-item">
              <span class="news-bullet">•</span>
              <span class="news-text">C2C交易系统上线</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      // 可以在这里添加数据
    };
  },
  mounted() {
  
    // 组件挂载后的逻辑
  },
  methods: {
    // 方法定义
    fbGoods() {
      // 跳转到商品发布页面
      this.$router.push('/BMC-DDZX/BMC-SPGL-FBSP');
    },
    //回收订单
    recyclingOrders(){
      this.$router.push('/BMC-HSZX/BMC-HSZX-HSDD');
    },
    // 销售订单
    salesOrders(){
      this.$router.push('/BMC-DDZX/BMC-XSDD');
    },
  },
};
</script>

<style scoped>
/* 整体容器 */
.home-container {
  display: flex;
  min-height: 100vh;

  background-color: #f0f2f5;
  gap: 16px;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  min-width:800px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 顶部三个卡片区域 */
.top-cards-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.top-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  color: white;
  position: relative;
}

.purple-card {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.blue-card {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: white;
}

.card-dots {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  cursor: pointer;
}

.card-dots-group {
  display: flex;
  gap: 8px;
}

.card-body {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.card-center-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

.card-stats-row {
  display: flex;
  width: 100%;
  justify-content: space-around;
}

.stat-column {
  text-align: center;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

/* 数据概览区域 */
.data-overview-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.section-icon-bar {
  width: 4px;
  height: 16px;
  background: #3b82f6;
  margin-right: 8px;
  border-radius: 2px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.section-actions {
  margin-left: auto;
}

.refresh-link,
.more-link {
  color: #3b82f6;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
}

.refresh-link:hover,
.more-link:hover {
  color: #2563eb;
}

/* 数据概览网格 - 关键的hover放大效果 */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
}

.overview-card {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #fff;
  position: relative;
  will-change: transform;

  /* 强制GPU加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;

  /* 优化字体渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 确保卡片尺寸为整数 */
  box-sizing: border-box;
  /* width: calc(100% - 24px); 根据实际情况调整 */
  height: auto;
}

.overview-card:hover {
  transform: scale(1.05) translateZ(0);
}

.green-card {
  background: #f0fdf4;
}

/* .blue-card {
  background: #eff6ff;
} */

.red-card {
  background: #fef2f2;
}

.yellow-card {
  background: #fffbeb;
}

/* .purple-card {
  background: #faf5ff;
} */

.orange-card {
  background: #fff7ed;
}

.overview-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* margin-bottom: 8px; */
  font-size: 18px;
}

.green-icon {
  background: #22c55e;
  color: white;
}

.blue-icon {
  background: #3b82f6;
  color: white;
}

.red-icon {
  background: #ef4444;
  color: white;
}

.yellow-icon {
  background: #f59e0b;
  color: white;
}

.purple-icon {
  background: #8b5cf6;
  color: white;
}

.orange-icon {
  background: #f97316;
  color: white;
}

.overview-label {
  font-size: 14px;
  color: #6b7280;
  text-align: center;
  font-weight: 400;
  margin-left: 6px;
}

.icon-text {
  font-size: 16px;
}

/* 数据中心区域 */
.data-center-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-center-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32px;
}

.data-item {
  display: flex;
  flex-direction: column;
}

.data-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 400;
}

.data-value {
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 4px;
  font-weight: 400;
}

/* 商家互联区域 */
.merchant-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.merchant-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.merchant-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.merchant-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.blue-bg {
  background: #3b82f6;
}

.pink-bg {
  background: #ec4899;
}

.orange-bg {
  background: #f97316;
}

.green-bg {
  background: #22c55e;
}

.purple-bg {
  background: #8b5cf6;
}

.yellow-bg {
  background: #f59e0b;
}

.merchant-info {
  flex: 1;
}

.merchant-title {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 400;
}

.merchant-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

/* 我的应用区域 */
.my-apps-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 右侧边栏 */
.right-sidebar {
  flex: 0 0 360px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
}

/* 推广测试标签 */
.promo-badge {
  position: absolute;
  top: -8px;
  right: 8px;
  background: #3b82f6;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  z-index: 10;
  font-weight: 500;
}

/* 用户信息卡片 */
.user-info-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 16px;
}

.user-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.user-badges {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.badge-item {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.blue-badge {
  background: #dbeafe;
  color: #1d4ed8;
}

.green-badge {
  background: #dcfce7;
  color: #166534;
}

.user-info-text {
  font-size: 12px;
  color: #6b7280;
}

.info-line {
  margin-bottom: 4px;
}

.info-label {
  color: #9ca3af;
}

.info-value {
  color: #1f2937;
  margin-left: 4px;
}

.info-link {
  color: #3b82f6;
  margin-left: 8px;
  text-decoration: none;
}

/* 账户信息 */
.account-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.account-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.verified-badge {
  background: #dcfce7;
  color: #166534;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.account-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.account-label {
  flex: 1;
  font-size: 14px;
  color: #1f2937;
  font-weight: 400;
}

.account-value {
  font-size: 14px;
  color: #6b7280;
}

.account-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  border: none;
  font-weight: 400;
}

.action-button:hover {
  background: #e5e7eb;
}

/* 特办事项 */
.special-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.special-header {
  margin-bottom: 20px;
}

.special-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.special-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.special-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f9fafb;
  padding: 12px;
  border-radius: 6px;
}

.special-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.special-label {
  flex: 1;
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
}

.special-count {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

/* 公告通知 */
.announcement-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.announcement-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.announcement-actions {
  margin-left: auto;
}

.announcement-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.announcement-banner {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  position: relative;
  overflow: hidden;
}

.banner-content {
  position: relative;
  z-index: 2;
}

.banner-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.banner-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.banner-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.banner-illustration {
  position: absolute;
  right: -20px;
  top: -20px;
  width: 120px;
  height: 120px;
  opacity: 0.3;
}

.illustration-graphic {
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="50" r="30" fill="%23ffffff" opacity="0.2"/></svg>')
    no-repeat center;
  background-size: contain;
}

.announcement-news {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.news-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.news-bullet {
  color: #3b82f6;
  font-weight: bold;
  margin-top: 2px;
}

.news-text {
  flex: 1;
  font-weight: 400;
}
/* 响应式设计 */
/* @media (max-width: 1200px) {
  .home-container {
    flex-direction: column;
  }

  .right-sidebar {
    flex: none;
    width: 100%;
  }

  .top-cards-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .overview-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .merchant-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .data-center-grid {
    grid-template-columns: repeat(2, 1fr);
  }
} */

/* @media (max-width: 768px) {
  .home-container {
    padding: 12px;
    gap: 12px;
  }

  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .merchant-grid {
    grid-template-columns: 1fr;
  }

  .data-center-grid {
    grid-template-columns: 1fr;
  }

  .user-info-card {
    flex-direction: column;
    text-align: center;
  }

  .special-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .home-container {
    padding: 8px;
    gap: 8px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .top-card {
    padding: 16px;
  }

  .data-overview-section,
  .data-center-section,
  .merchant-section,
  .my-apps-section,
  .user-info-card,
  .account-section,
  .special-section,
  .announcement-section {
    padding: 16px;
  }
} */
</style>
