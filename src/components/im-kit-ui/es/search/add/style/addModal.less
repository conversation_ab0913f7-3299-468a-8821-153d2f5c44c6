@import '../style/theme.less';
@add-prefix-modal-cls: ~'@{add-prefix}-modal';

.@{add-prefix-modal-cls} {
  .@{ant-prefix}-modal-content {
    border-radius: @yx-border-radius-8;

    .@{ant-prefix}-modal-header {
      border-radius: @yx-border-radius-8 @yx-border-radius-8 0 0;
    }

    .@{ant-prefix}-modal-body {
      margin-top: 13px;
    }

    .@{ant-prefix}-modal-footer {
      padding: 10px 24px;
      border-top: none;
    }
  }

  &-empty-content {
    margin-top: 16px;
    position: absolute;
    width: 200px;
    height: 16px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: @yx-primary-font-size;
    line-height: 16px;
    color: @yx-primary-content-empty-color;
  }

  &-content {
    display: flex;
    align-items: center;
    margin-top: 24px;
  }

  &-info {
    height: 36px;
    margin: 0 12px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    flex: 1;
    min-width: 0;

    &-name {
      height: 16px;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      font-size: @yx-primary-font-size;
      line-height: 16px;
      color: @yx-primary-text-color;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    &-account {
      height: 14px;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      font-size: @yx-font-size-12;
      line-height: 14px;
      color: @yx-text-color-2;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
