var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
import React, { useState, useMemo, useCallback } from 'react';
import { Modal } from 'antd';
import { SearchInput, CrudeAvatar, useTranslation } from '../../../../common';
import { AutoSizer, List } from 'react-virtualized';
var SearchItem = function (_a) {
    var onClick = _a.onClick, _b = _a.prefix, prefix = _b === void 0 ? 'search' : _b, props = __rest(_a, ["onClick", "prefix"]);
    var _prefix = "".concat(prefix, "-search-modal");
    return (React.createElement("div", { className: "".concat(_prefix, "-content-section-item"), onClick: onClick },
        React.createElement(CrudeAvatar, __assign({}, props)),
        React.createElement("span", { className: "".concat(_prefix, "-content-section-item-name") }, props.alias || props.nick || props.account)));
};
var SearchModal = function (_a) {
    var visible = _a.visible, friends = _a.friends, teams = _a.teams, onCancel = _a.onCancel, onResultItemClick = _a.onResultItemClick, renderEmpty = _a.renderEmpty, renderSearchResultEmpty = _a.renderSearchResultEmpty, _b = _a.prefix, prefix = _b === void 0 ? 'search' : _b, _c = _a.commonPrefix, commonPrefix = _c === void 0 ? 'common' : _c;
    var _prefix = "".concat(prefix, "-search-modal");
    var t = useTranslation().t;
    var _d = __read(useState(''), 2), searchText = _d[0], setSearchText = _d[1];
    var sections = useMemo(function () {
        return [
            {
                id: 'friends',
                list: friends,
            },
            {
                id: 'groups',
                list: teams,
            },
        ].filter(function (item) { return !!item.list.length; });
    }, [friends, teams]);
    var searchedSections = useMemo(function () {
        var finalSections = sections
            .map(function (item) {
            if (item.id === 'friends') {
                return __assign(__assign({}, item), { list: item.list.filter(function (item) {
                        return (item.alias ||
                            item.nick ||
                            item.account).includes(searchText);
                    }) });
            }
            if (item.id === 'groups') {
                return __assign(__assign({}, item), { list: item.list.filter(function (item) {
                        return (item.name || item.teamId).includes(searchText);
                    }) });
            }
            return __assign({}, item);
        })
            .filter(function (item) { return !!item.list.length; });
        var res = [];
        finalSections.forEach(function (item) {
            if (item.id === 'friends') {
                res.push('friends');
                item.list.forEach(function (item) {
                    res.push(item);
                });
            }
            else if (item.id === 'groups') {
                res.push('groups');
                item.list.forEach(function (item) {
                    res.push(item);
                });
            }
        });
        return res;
    }, [sections, searchText]);
    var handleSearchChange = function (value) {
        setSearchText(value);
    };
    var resetState = useCallback(function () {
        setSearchText('');
    }, []);
    var handleItemClick = useCallback(function (data) {
        onResultItemClick(data);
        resetState();
    }, [onResultItemClick, resetState]);
    var handleCancel = function () {
        onCancel();
        resetState();
    };
    var rowRenderer = useCallback(function (_a) {
        var index = _a.index, key = _a.key, style = _a.style;
        var item = searchedSections[index];
        if (typeof item === 'string') {
            return (React.createElement("div", { style: style, key: key },
                React.createElement("div", { className: "".concat(_prefix, "-content-section-title") }, t(item === 'friends' ? 'searchFriendTitle' : 'searchTeamTitle'))));
        }
        return (React.createElement("div", { style: style, key: key },
            React.createElement(SearchItem, { key: item.account ||
                    item.teamId, onClick: function () { return handleItemClick(item); }, prefix: prefix, account: item.account ||
                    item.teamId, avatar: item.avatar, nick: item.nick || item.name, alias: item.alias || '' })));
    }, [_prefix, prefix, searchedSections, t, handleItemClick]);
    return (React.createElement(Modal, { className: _prefix, title: React.createElement(SearchInput, { value: searchText, prefix: commonPrefix, onChange: handleSearchChange }), onCancel: handleCancel, visible: visible, width: 630, footer: null, destroyOnClose: true }, !sections.length ? (renderEmpty ? (renderEmpty()) : (React.createElement("div", { className: "".concat(_prefix, "-empty") }, t('searchEmptyText')))) : !searchedSections.length ? (renderSearchResultEmpty ? (renderSearchResultEmpty()) : (React.createElement("div", { className: "".concat(_prefix, "-empty") }, t('searchNoResText')))) : (React.createElement("div", { className: "".concat(_prefix, "-content") },
        React.createElement(AutoSizer, null, function (_a) {
            var height = _a.height, width = _a.width;
            return (React.createElement(List, { height: height, overscanRowCount: 10, rowCount: searchedSections.length, rowHeight: 52, rowRenderer: rowRenderer, width: width }));
        })))));
};
export default SearchModal;
