declare const LocaleConfig: {
    saveText: string;
    setText: string;
    saveSuccessText: string;
    saveFailedText: string;
    addFriendSuccessText: string;
    applyFriendSuccessText: string;
    addFriendFailedText: string;
    applyFriendFailedText: string;
    okText: string;
    cancelText: string;
    deleteText: string;
    recallText: string;
    forwardText: string;
    forwardFailedText: string;
    sendBtnText: string;
    replyText: string;
    commentText: string;
    recentSessionText: string;
    you: string;
    deleteFriendText: string;
    confirmDeleteText: string;
    confirmDeleteFriendText: string;
    deleteFriendSuccessText: string;
    deleteFriendFailedText: string;
    blackText: string;
    removeBlackText: string;
    blackSuccessText: string;
    blackFailedText: string;
    removeBlackSuccessText: string;
    removeBlackFailedText: string;
    maxSelectedText: string;
    selectedText: string;
    friendsText: string;
    strangerText: string;
    emailErrorText: string;
    uploadLimitText: string;
    uploadLimitUnit: string;
    uploadImgFailedText: string;
    accountText: string;
    nickText: string;
    genderText: string;
    phoneText: string;
    emailText: string;
    signText: string;
    accountPlaceholder: string;
    teamIdPlaceholder: string;
    nickPlaceholder: string;
    genderPlaceholder: string;
    phonePlaceholder: string;
    emailPlaceholder: string;
    signPlaceholder: string;
    searchInputPlaceholder: string;
    searchTeamMemberPlaceholder: string;
    searchText: string;
    man: string;
    woman: string;
    unknow: string;
    welcomeText: string;
    notSupportMessageText: string;
    applyTeamText: string;
    applyTeamSuccessText: string;
    rejectText: string;
    acceptText: string;
    inviteTeamText: string;
    applyFriendText: string;
    acceptResultText: string;
    rejectResultText: string;
    beRejectResultText: string;
    passResultText: string;
    rejectTeamInviteText: string;
    updateTeamAvatar: string;
    updateTeamName: string;
    updateTeamIntro: string;
    updateTeamInviteMode: string;
    updateTeamUpdateTeamMode: string;
    updateAllowAt: string;
    updateTeamMute: string;
    onlyTeamOwner: string;
    teamAll: string;
    closeText: string;
    openText: string;
    inviteText: string;
    aliasText: string;
    updateAliasSuccessText: string;
    updateAliasFailedText: string;
    sendText: string;
    noPermission: string;
    unreadText: string;
    readText: string;
    allReadText: string;
    amap: string;
    txmap: string;
    bdmap: string;
    callDurationText: string;
    callCancelText: string;
    callRejectedText: string;
    callTimeoutText: string;
    callBusyText: string;
    onDismissTeamText: string;
    onRemoveTeamText: string;
    textMsgText: string;
    customMsgText: string;
    audioMsgText: string;
    videoMsgText: string;
    fileMsgText: string;
    callMsgText: string;
    geoMsgText: string;
    imgMsgText: string;
    notiMsgText: string;
    robotMsgText: string;
    tipMsgText: string;
    unknowMsgText: string;
    deleteSessionText: string;
    muteSessionText: string;
    unmuteSessionText: string;
    deleteStickTopText: string;
    addStickTopText: string;
    beMentioned: string;
    teamListTitle: string;
    friendListTitle: string;
    blackListTitle: string;
    msgListTitle: string;
    blackListDesc: string;
    teamMenuText: string;
    friendMenuText: string;
    blackMenuText: string;
    msgMenuText: string;
    acceptedText: string;
    acceptFailedText: string;
    rejectedText: string;
    rejectFailedText: string;
    getApplyMsgFailedText: string;
    addFriendText: string;
    createTeamText: string;
    joinTeamText: string;
    joinTeamSuccessText: string;
    beRemoveTeamText: string;
    addButtonText: string;
    addSuccessText: string;
    addFailedText: string;
    createButtonText: string;
    createTeamSuccessText: string;
    createTeamFailedText: string;
    chatButtonText: string;
    getRelationFailedText: string;
    accountNotMatchText: string;
    teamIdNotMatchText: string;
    searchButtonText: string;
    searchTeamPlaceholder: string;
    teamTitle: string;
    teamAvatarText: string;
    addTeamMemberText: string;
    searchEmptyText: string;
    searchNoResText: string;
    searchFriendTitle: string;
    searchTeamTitle: string;
    notSupportJoinText: string;
    sendToText: string;
    sendUsageText: string;
    sendEmptyText: string;
    teamMutePlaceholder: string;
    enterTeamText: string;
    leaveTeamText: string;
    teamMuteText: string;
    muteAllTeamSuccessText: string;
    unmuteAllTeamSuccessText: string;
    muteAllTeamFailedText: string;
    unmuteAllTeamFailedText: string;
    updateTeamSuccessText: string;
    updateTeamFailedText: string;
    leaveTeamSuccessText: string;
    leaveTeamFailedText: string;
    dismissTeamSuccessText: string;
    dismissTeamFailedText: string;
    addTeamMemberSuccessText: string;
    addTeamMemberFailedText: string;
    addTeamMemberConfirmText: string;
    removeTeamMemberText: string;
    removeTeamMemberConfirmText: string;
    removeTeamMemberSuccessText: string;
    removeTeamMemberFailedText: string;
    teamTitleConfirmText: string;
    teamAvatarConfirmText: string;
    teamIdText: string;
    teamSignText: string;
    teamTitlePlaceholder: string;
    teamSignPlaceholder: string;
    teamOwnerText: string;
    teamManagerText: string;
    teamManagerEditText: string;
    teamManagerEmptyText: string;
    teamOwnerAndManagerText: string;
    updateTeamManagerSuccessText: string;
    updateTeamManagerFailText: string;
    userNotInTeam: string;
    teamManagerLimitText: string;
    teamInviteModeText: string;
    teamAtModeText: string;
    teamMemberText: string;
    teamInfoText: string;
    teamPowerText: string;
    dismissTeamText: string;
    transferOwnerText: string;
    newGroupOwnerText: string;
    beAddTeamManagersText: string;
    beRemoveTeamManagersText: string;
    transferTeamFailedText: string;
    transferToText: string;
    transferTeamSuccessText: string;
    transferOwnerConfirmText: string;
    dismissTeamConfirmText: string;
    leaveTeamTitle: string;
    leaveTeamConfirmText: string;
    personUnit: string;
    leaveTeamButtonText: string;
    sendMsgFailedText: string;
    getHistoryMsgFailedText: string;
    sendBlackFailedText: string;
    recallMessageText: string;
    recallReplyMessageText: string;
    reeditText: string;
    addChatMemberText: string;
    chatHistoryText: string;
    noMoreText: string;
    receiveText: string;
    strangerNotiText: string;
    nickInTeamText: string;
    editNickInTeamText: string;
    updateMyMemberNickSuccess: string;
    updateMyMemberNickFailed: string;
    updateBitConfigMaskSuccess: string;
    updateBitConfigMaskFailed: string;
    imgText: string;
    videoText: string;
    onlineText: string;
    offlineText: string;
    Laugh: string;
    Happy: string;
    Sexy: string;
    Cool: string;
    Mischievous: string;
    Kiss: string;
    Spit: string;
    Squint: string;
    Cute: string;
    Grimace: string;
    Snicker: string;
    Joy: string;
    Ecstasy: string;
    Surprise: string;
    Tears: string;
    Sweat: string;
    Angle: string;
    Funny: string;
    Awkward: string;
    Thrill: string;
    Cry: string;
    Fretting: string;
    Terrorist: string;
    Halo: string;
    Shame: string;
    Sleep: string;
    Tired: string;
    Mask: string;
    ok: string;
    AllRight: string;
    Despise: string;
    Uncomfortable: string;
    Disdain: string;
    ill: string;
    Mad: string;
    Ghost: string;
    Angry: string;
    Unhappy: string;
    Frown: string;
    Broken: string;
    Beckoning: string;
    Ok: string;
    Low: string;
    Nice: string;
    Applause: string;
    GoodJob: string;
    Hit: string;
    Please: string;
    Bye: string;
    First: string;
    Fist: string;
    GiveMeFive: string;
    Knife: string;
    Hi: string;
    No: string;
    Hold: string;
    Think: string;
    Pig: string;
    NoListen: string;
    NoLook: string;
    NoWords: string;
    Monkey: string;
    Bomb: string;
    Cloud: string;
    Rocket: string;
    Ambulance: string;
    Poop: string;
};
export default LocaleConfig;
