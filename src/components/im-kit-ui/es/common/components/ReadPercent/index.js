var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
import React, { useEffect, useState } from "react";
import { useTranslation } from "../../hooks/useTranslation";
import { Popover } from "antd";
import CommonIcon from "../CommonIcon";
import { useStateContext } from "../../hooks/useStateContext";
export var ReadPercent = function (_a) {
    var unread = _a.unread, read = _a.read, _b = _a.radius, radius = _b === void 0 ? 8 : _b, _c = _a.hoverable, hoverable = _c === void 0 ? false : _c, _d = _a.prefix, prefix = _d === void 0 ? "common" : _d, msg = _a.msg;
    var _prefix = "".concat(prefix, "-percent");
    var t = useTranslation().t;
    var _e = useStateContext(), nim = _e.nim, store = _e.store;
    var _f = __read(useState(null), 2), detailContent = _f[0], setDetailContent = _f[1];
    useEffect(function () {
        if (!msg)
            return;
        var fetchDetail = function () { return __awaiter(void 0, void 0, void 0, function () {
            var teamId_1, idServer_1, data, unreadAccounts_1, teamMembers, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 5, , 6]);
                        teamId_1 = msg.to, idServer_1 = msg.idServer;
                        return [4 /*yield*/, new Promise(function (resolve, reject) {
                                //@ts-ignore
                                nim.nim.getTeamMsgReadAccounts({
                                    teamMsgReceipt: { teamId: teamId_1, idServer: idServer_1 },
                                    done: function (err, _, resData) {
                                        if (err)
                                            reject(err);
                                        else
                                            resolve(resData);
                                    },
                                });
                            })];
                    case 1:
                        data = _a.sent();
                        unreadAccounts_1 = data.unreadAccounts;
                        unreadAccounts_1 = unreadAccounts_1.filter(function (ele) { return ele != "kkassistant5"; });
                        if (!(unreadAccounts_1 && unreadAccounts_1.length)) return [3 /*break*/, 3];
                        return [4 /*yield*/, new Promise(function (resolve, reject) {
                                //@ts-ignore
                                nim.nim.getUsers({
                                    accounts: unreadAccounts_1,
                                    done: function (err, users) {
                                        if (err)
                                            reject(err);
                                        resolve(users);
                                    },
                                });
                            })];
                    case 2:
                        teamMembers = _a.sent();
                        if (teamMembers && teamMembers.length) {
                            setDetailContent(React.createElement("div", null,
                                React.createElement("div", null, "".concat(t("unreadText"), " ").concat(teamMembers.length, "  ").concat(t("personUnit"))),
                                React.createElement("div", null, teamMembers.map(function (member, index) { return (React.createElement("div", { className: "".concat(_prefix, "-wrap-unread-item"), key: index }, member.nick)); })),
                                React.createElement("div", null, "".concat(t("readText"), " ").concat(read, " ").concat(t("personUnit")))));
                        }
                        else {
                            setDetailContent(React.createElement("span", null, percent >= 100
                                ? t("allReadText")
                                : "".concat(t("unreadText"), " ").concat(unread, " ").concat(t("personUnit"), " | ").concat(t("readText"), " ").concat(read, " ").concat(t("personUnit"))));
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        setDetailContent(React.createElement("span", null, percent >= 100
                            ? t("allReadText")
                            : "".concat(t("unreadText"), " ").concat(unread, " ").concat(t("personUnit"), " | ").concat(t("readText"), " ").concat(read, " ").concat(t("personUnit"))));
                        _a.label = 4;
                    case 4: return [3 /*break*/, 6];
                    case 5:
                        error_1 = _a.sent();
                        setDetailContent(React.createElement("span", null, percent >= 100
                            ? t("allReadText")
                            : "".concat(t("unreadText"), " ").concat(unread, " ").concat(t("personUnit"), " | ").concat(t("readText"), " ").concat(read, " ").concat(t("personUnit"))));
                        return [3 /*break*/, 6];
                    case 6: return [2 /*return*/];
                }
            });
        }); };
        fetchDetail();
    }, [msg, nim, store, t]); // 依赖项包括msg、nim、store和t
    var percent = (read / (unread + read)) * 100 || 0;
    var renderSvg = function () {
        return percent >= 100 ? (React.createElement(CommonIcon, { style: { fontSize: radius * 2 }, type: "icon-yidu", className: "".concat(_prefix, "-wrap-icon") })) : (React.createElement("svg", { className: "".concat(_prefix, "-wrap-svg"), height: radius * 2, width: radius * 2, viewBox: "0 0 ".concat(radius * 2, " ").concat(radius * 2) },
            React.createElement("circle", { className: "".concat(_prefix, "-wrap-svg-bg"), r: radius - 1, cx: radius, cy: radius, strokeWidth: "1.5" }),
            React.createElement("circle", { className: "".concat(_prefix, "-wrap-svg-content"), r: radius / 2, cx: radius, cy: radius, fill: "transparent", strokeWidth: radius, strokeDasharray: "calc(".concat(percent, " * ").concat(radius, " * 3.14 / 100) calc(").concat(radius, " * 3.14)"), transform: "rotate(-90 ".concat(radius, " ").concat(radius, ")") })));
    };
    return (React.createElement("div", { className: "".concat(_prefix, "-wrap") }, hoverable ? (React.createElement(Popover, { placement: "top", content: detailContent }, renderSvg())) : (renderSvg())));
};
