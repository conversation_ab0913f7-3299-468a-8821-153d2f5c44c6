@import '~antd/lib/style/themes/variable.less';
@import './theme.less';

@friend-select-prefix-cls: ~'@{friend-select-prefix}';

.@{friend-select-prefix-cls} {
  &-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 10px;
  }

  &-left {
    height: 100%;
    overflow-y: auto;
    flex: 1;
  }

  &-right {
    height: 100%;
    flex: 1;
  }

  &-divider {
    height: 100%;
    border-color: @yx-border-color-7;
  }

  &-subtitle-item {
    padding: 8px 0;
    color: @yx-text-color-3;
    font-size: @yx-primary-font-size;
    border-bottom: 1px solid @yx-border-color-7;
  }

  &-selected-title {
    font-size: @yx-primary-font-size;
    color: @yx-text-color-3;
    height: 22px;
  }

  &-selected-content {
    height: calc(100% - 22px);
    overflow-y: auto;
  }

  &-item {
    display: flex;
    align-items: center;
    padding: 4px 0;

    &-checkbox.@{ant-prefix}-checkbox-wrapper {
      margin-right: 8px;
    }

    &-label {
      margin-left: 8px;
      color: @yx-primary-text-color;
      font-size: @yx-font-size-12;
    }
  }
}
