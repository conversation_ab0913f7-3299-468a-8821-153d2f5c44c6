/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
/* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
html {
  --ant-primary-color: #1890ff;
  --ant-primary-color-hover: #40a9ff;
  --ant-primary-color-active: #096dd9;
  --ant-primary-color-outline: rgba(24, 144, 255, 0.2);
  --ant-primary-1: #e6f7ff;
  --ant-primary-2: #bae7ff;
  --ant-primary-3: #91d5ff;
  --ant-primary-4: #69c0ff;
  --ant-primary-5: #40a9ff;
  --ant-primary-6: #1890ff;
  --ant-primary-7: #096dd9;
  --ant-primary-color-deprecated-pure: ;
  --ant-primary-color-deprecated-l-35: #cbe6ff;
  --ant-primary-color-deprecated-l-20: #7ec1ff;
  --ant-primary-color-deprecated-t-20: #46a6ff;
  --ant-primary-color-deprecated-t-50: #8cc8ff;
  --ant-primary-color-deprecated-f-12: rgba(24, 144, 255, 0.12);
  --ant-primary-color-active-deprecated-f-30: rgba(230, 247, 255, 0.3);
  --ant-primary-color-active-deprecated-d-02: #dcf4ff;
  --ant-success-color: #52c41a;
  --ant-success-color-hover: #73d13d;
  --ant-success-color-active: #389e0d;
  --ant-success-color-outline: rgba(82, 196, 26, 0.2);
  --ant-success-color-deprecated-bg: #f6ffed;
  --ant-success-color-deprecated-border: #b7eb8f;
  --ant-error-color: #ff4d4f;
  --ant-error-color-hover: #ff7875;
  --ant-error-color-active: #d9363e;
  --ant-error-color-outline: rgba(255, 77, 79, 0.2);
  --ant-error-color-deprecated-bg: #fff2f0;
  --ant-error-color-deprecated-border: #ffccc7;
  --ant-warning-color: #faad14;
  --ant-warning-color-hover: #ffc53d;
  --ant-warning-color-active: #d48806;
  --ant-warning-color-outline: rgba(250, 173, 20, 0.2);
  --ant-warning-color-deprecated-bg: #fffbe6;
  --ant-warning-color-deprecated-border: #ffe58f;
  --ant-info-color: #1890ff;
  --ant-info-color-deprecated-bg: #e6f7ff;
  --ant-info-color-deprecated-border: #91d5ff;
}
.common-parse-session-text-wrapper {
  padding: 12px 16px;
}
.common-parse-session-emoji-icon {
  font-size: 18px;
}
.common-parse-session-image .ant-image-img {
  max-width: 300px;
  min-width: 150px;
  max-height: 300px;
  min-height: 150px;
  object-fit: cover;
  vertical-align: middle;
}
.common-parse-session-video {
  max-width: 300px;
  min-width: 150px;
  max-height: 300px;
  min-height: 150px;
  object-fit: cover;
  vertical-align: middle;
}
.common-parse-session-mention {
  color: #2a6bf2;
}
.common-parse-session-noti {
  padding: 22px 16px;
  line-height: 16px;
  color: #999999;
  text-align: center;
}
.common-parse-session-noti-transfer {
  color: #2a6bf2;
  margin-right: 5px;
}
.common-parse-session-audio-container {
  padding: 12px 16px;
}
.common-parse-session-audio-in,
.common-parse-session-audio-out {
  width: 50px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
.common-parse-session-audio-in .common-parse-session-audio-icon-wrapper,
.common-parse-session-audio-out .common-parse-session-audio-icon-wrapper {
  font-size: 20px;
}
.common-parse-session-audio-in .common-parse-session-audio-icon-wrapper:hover,
.common-parse-session-audio-out .common-parse-session-audio-icon-wrapper:hover {
  animation: audio-play 5s;
}
.common-parse-session-audio-in {
  flex-direction: row-reverse;
}
.common-parse-session-audio-in .common-parse-session-audio-icon-wrapper {
  transform: rotate(180deg);
}
.common-parse-session-location-card {
  position: relative;
  background-color: #fff;
  width: 288px;
}
.common-parse-session-location-card .common-parse-session-location-title {
  padding: 8px 10px 0 10px;
}
.common-parse-session-location-card .common-parse-session-location-subTitle {
  padding: 0 10px 8px 10px;
  color: #999999;
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.common-parse-session-location-card img {
  width: 100%;
}
.common-parse-session-file-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
}
.common-parse-session-file-box .common-parse-session-file-icon {
  font-size: 32px;
}
.common-parse-session-file-box .common-parse-session-file-info {
  padding-left: 16px;
  display: flex;
  flex-direction: column;
}
.common-parse-session-upload-container {
  position: relative;
}
.common-parse-session-upload-img {
  max-width: 300px;
  min-width: 150px;
  max-height: 300px;
  min-height: 150px;
  object-fit: cover;
  vertical-align: middle;
}
.common-parse-session-upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.common-parse-session-upload-progress {
  cursor: pointer;
}
.common-parse-session-upload-progress .ant-progress-circle.ant-progress-status-exception .ant-progress-text {
  color: #ffffff;
}
.common-parse-session-reply-wrapper {
  font-size: 12px;
  color: #666666;
  padding: 12px 16px;
  border-bottom: 1px solid #bbd2ed;
}
.common-parse-session-reply-wrapper .common-parse-session-text-wrapper {
  padding: 0;
}
.common-parse-session-reply-wrapper .common-parse-session-image-container {
  margin: 0;
}
.common-parse-session-reply-wrapper .common-parse-session-file-box {
  padding: 0;
}
.common-parse-session-map-menu-popover {
  top: 0 !important;
  bottom: 0 !important;
}
.common-parse-session-map-menu {
  width: 60px;
}
.common-parse-session-map-menu a {
  color: #666666;
  display: block;
  text-align: center;
  margin: 10px 0px;
}
