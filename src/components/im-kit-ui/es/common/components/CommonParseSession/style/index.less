@import '~antd/lib/style/themes/variable.less';
@import './theme.less';

@prefix: ~'@{parse-session-cls}';

.@{prefix}-text-wrapper {
  padding: 12px 16px;
}

.@{prefix}-emoji-icon {
  font-size: @yx-font-size-18;
}

// .@{prefix}-image-container {
//   margin: 12px 16px;
// }

.@{prefix}-image {
  .@{ant-prefix}-image-img {
    max-width: 300px;
    min-width: 150px;
    max-height: 300px;
    min-height: 150px;
    object-fit: cover;
    vertical-align: middle;
  }
}

.@{prefix}-video {
  max-width: 300px;
  min-width: 150px;
  max-height: 300px;
  min-height: 150px;
  object-fit: cover;
  vertical-align: middle;
}

.@{prefix}-mention {
  color: @yx-text-color-4;
}

.@{prefix}-noti {
  padding: 22px 16px;
  line-height: 16px;
  color: @yx-text-color-2;
  text-align: center;
}

.@{prefix}-noti-transfer {
  color: @yx-text-color-4;
  margin-right: 5px;
}

.@{prefix}-audio-container {
  padding: 12px 16px;
}
.@{prefix}-audio-in,
.@{prefix}-audio-out {
  width: 50px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  .@{prefix}-audio-icon-wrapper {
    font-size: @yx-font-size-20;
    &:hover {
      animation: audio-play 5s;
    }
  }
}
.@{prefix}-audio-in {
  flex-direction: row-reverse;
  .@{prefix}-audio-icon-wrapper {
    transform: rotate(180deg);
  }
}

.@{prefix}-location-card {
  // border: 1px solid @yx-border-color-1;
  // border-radius: @yx-primary-border-radius;
  position: relative;
  background-color: #fff;
  width: 288px;
  .@{prefix}-location-title {
    padding: 8px 10px 0 10px;
  }
  .@{prefix}-location-subTitle {
    padding: 0 10px 8px 10px;
    color: @yx-text-color-2;
    font-size: @yx-font-size-12;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    // margin-bottom: 8px;
  }
  img {
    width: 100%;
  }
}

.@{prefix}-file-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;

  .@{prefix}-file-icon {
    font-size: @yx-font-size-32;
  }

  .@{prefix}-file-info {
    padding-left: 16px;
    display: flex;
    flex-direction: column;
  }
}

.@{prefix}-upload-container {
  position: relative;
}
.@{prefix}-upload-img {
  max-width: 300px;
  min-width: 150px;
  max-height: 300px;
  min-height: 150px;
  object-fit: cover;
  vertical-align: middle;
}
.@{prefix}-upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.@{prefix}-upload-progress {
  cursor: pointer;
  .@{ant-prefix}-progress-circle.@{ant-prefix}-progress-status-exception .@{ant-prefix}-progress-text{
    color: @yx-primary-color;
  }
}

.@{prefix}-reply-wrapper {
  font-size: @yx-font-size-12;
  color: @yx-text-color-1;
  padding: 12px 16px;
  border-bottom: 1px solid #bbd2ed;
  // display: flex;
  // .@{prefix}-reply-nick {
  //   white-space: nowrap;
  // }
  .@{prefix}-text-wrapper {
    padding: 0;
  }
  .@{prefix}-image-container {
    margin: 0;
  }
  .@{prefix}-file-box {
    padding: 0;
  }
  // cursor: pointer;
}

.@{prefix}-map-menu-popover {
  // right: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
}

.@{prefix}-map-menu {
  width: 60px;
  a {
    color: @yx-text-color-1;
    display: block;
    text-align: center;
    margin: 10px 0px;
  }
}
