import React, { useCallback, useMemo } from 'react';
import { Utils, useTranslation } from '../../../common';
import { FriendItem } from './FriendItem';
import { Spin, Empty } from 'antd';
import { AutoSizer, List } from 'react-virtualized';
export var FriendList = function (_a) {
    var list = _a.list, _b = _a.loading, loading = _b === void 0 ? false : _b, onItemClick = _a.onItemClick, afterSendMsgClick = _a.afterSendMsgClick, renderFriendListHeader = _a.renderFriendListHeader, renderFriendListEmpty = _a.renderFriendListEmpty, _c = _a.prefix, prefix = _c === void 0 ? 'contact' : _c, _d = _a.commonPrefix, commonPrefix = _d === void 0 ? 'common' : _d;
    var _prefix = "".concat(prefix, "-friend");
    var t = useTranslation().t;
    var dataSource = useMemo(function () {
        var group = Utils.groupByPy(list, {
            firstKey: 'alias',
            secondKey: 'nick',
            thirdKey: 'account',
        }, false);
        return group.map(function (item) { return item.data; }).flat();
    }, [list]);
    var rowRenderer = useCallback(function (_a) {
        var index = _a.index, key = _a.key, style = _a.style;
        var item = dataSource[index];
        return (React.createElement("div", { style: style, key: key },
            React.createElement(FriendItem, { key: item.account, account: item.account, onItemClick: onItemClick, afterSendMsgClick: afterSendMsgClick, prefix: prefix, commonPrefix: commonPrefix })));
    }, [afterSendMsgClick, commonPrefix, dataSource, onItemClick, prefix]);
    return (React.createElement("div", { className: "".concat(_prefix, "-wrapper") },
        React.createElement("div", { className: "".concat(_prefix, "-title") }, renderFriendListHeader
            ? renderFriendListHeader()
            : t('friendListTitle')),
        React.createElement("div", { className: "".concat(_prefix, "-list") }, loading ? (React.createElement(Spin, null)) : !list.length ? (renderFriendListEmpty ? (renderFriendListEmpty()) : (React.createElement(Empty, { style: { marginTop: 10 } }))) : (React.createElement(AutoSizer, null, function (_a) {
            var height = _a.height, width = _a.width;
            return (React.createElement(List, { height: height, overscanRowCount: 10, rowCount: dataSource.length, rowHeight: 46, rowRenderer: rowRenderer, width: width }));
        })))));
};
