.contact-friend-item {
  cursor: pointer;
  padding: 5px 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
  border-radius: 4px;
}
.contact-friend-item:hover {
  background-color: #ecf0f4;
}
.contact-friend-item-label {
  margin-left: 12px;
  font-size: 14px;
  color: #333333;
}
.contact-friend-item-state-online {
  color: #333333;
}
.contact-friend-item-state-offline {
  color: #666666;
}
