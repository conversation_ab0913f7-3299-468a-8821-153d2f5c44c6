@import './theme.less';

@friend-wrapper-prefix-cls: ~'@{friend-list-prefix}-wrapper';
@friend-list-prefix-cls: ~'@{friend-list-prefix}-list';
@friend-title-prefix-cls: ~'@{friend-list-prefix}-title';
@friend-subtitle-prefix-cls: ~'@{friend-list-prefix}-subtitle';

.@{friend-wrapper-prefix-cls} {
  background-color: @yx-background-color-1;
  width: 100%;
  height: 100%;
}

.@{friend-title-prefix-cls} {
  color: @yx-primary-text-color;
  font-size: @yx-font-size-16;
  font-weight: 500;
  border-bottom: 1px solid @yx-border-color-7;
  padding: 22px 16px;
  height: 71px;
}

.@{friend-list-prefix-cls} {
  width: 100%;
  height: calc(100% - 71px);
  overflow-y: auto;
}

.@{friend-subtitle-prefix-cls} {
  padding: 16px 8px 8px;
  margin: 0 16px;
  border-bottom: 1px solid @yx-border-color-7;
  font-size: @yx-primary-font-size;
  color: @yx-text-color-3;
  margin-bottom: 10px;
}
