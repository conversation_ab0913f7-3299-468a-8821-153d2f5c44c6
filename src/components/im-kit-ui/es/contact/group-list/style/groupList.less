@import './theme.less';

@group-list-prefix-cls: ~'@{group-list-prefix}-wrapper';
@group-title-prefix-cls: ~'@{group-list-prefix}-title';
@group-content-prefix-cls: ~'@{group-list-prefix}-content';

.@{group-list-prefix-cls} {
  background-color: @yx-background-color-1;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.@{group-title-prefix-cls} {
  color: @yx-primary-text-color;
  font-size: @yx-font-size-16;
  font-weight: 500;
  border-bottom: 1px solid @yx-border-color-7;
  padding: 22px 16px;
}

.@{group-content-prefix-cls} {
  padding: 0 16px;
  height: calc(100% - 71px);
}
