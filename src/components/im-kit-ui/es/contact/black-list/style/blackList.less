@import './theme.less';

@black-list-prefix-cls: ~'@{black-list-prefix}-wrapper';
@black-title-prefix-cls: ~'@{black-list-prefix}-title';
@black-content-prefix-cls: ~'@{black-list-prefix}-content';

.@{black-list-prefix-cls} {
  background-color: @yx-background-color-1;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.@{black-title-prefix-cls} {
  color: @yx-primary-text-color;
  font-size: @yx-font-size-16;
  font-weight: 500;
  border-bottom: 1px solid @yx-border-color-7;
  padding: 22px 16px;

  &-remark {
    color: @yx-text-color-1;
    font-size: @yx-primary-font-size;
    font-weight: normal;
  }
}

.@{black-content-prefix-cls} {
  padding: 0 16px;
}
