.conversation-item {
  width: 100%;
  display: flex;
  padding: 12px;
  background-color: #ffffff;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  align-items: center;
}
.conversation-item-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 0 6px 0 10px;
  flex: 1;
  min-width: 0;
}
.conversation-item-content-name {
  font-size: 14px;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.conversation-item-content-msg {
  font-size: 12px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}
.conversation-item-content-msg-body {
  font-size: 12px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.conversation-item-content-mention {
  font-size: 12px;
  color: #e74646;
}
.conversation-item-content-read-status {
  margin-right: 4px;
}
.conversation-item-content-read-icon {
  font-size: 14px;
  color: #2a6bf2 !important;
  position: relative;
  top: 1px;
}
.conversation-item-state {
  display: flex;
  flex-direction: column;
  width: fit-content;
  align-items: flex-end;
  white-space: nowrap;
}
.conversation-item-state-date {
  font-size: 12px;
  color: #999999;
}
.conversation-item-state-mute {
  color: #656a72;
  height: 22px;
}
.conversation-item-top {
  background-color: #f7f9fa;
}
.conversation-item-select {
  background-color: #ebf3fc;
}
.conversation-item:hover {
  background-color: #ebf3fc;
}
