@import './theme.less';

@conversation-item-prefix-cls: ~'@{conversation-prefix}-item';

.@{conversation-item-prefix-cls} {
  width: 100%;
  display: flex;
  padding: 12px;
  background-color: @yx-primary-color;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  align-items: center;

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 0 6px 0 10px;
    flex: 1;
    min-width: 0;

    &-name {
      font-size: @yx-primary-font-size;
      color: @yx-primary-text-color;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &-msg {
      font-size: @yx-font-size-12;
      color: @yx-text-color-2;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
    }
    &-msg-body {
      font-size: @yx-font-size-12;
      color: @yx-text-color-2;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &-mention {
      font-size: @yx-font-size-12;
      color: @yx-text-color-7;
    }
    &-read-status {
      margin-right: 4px;
    }
    &-read-icon {
      font-size: @yx-primary-font-size;
      color: @yx-text-color-4 !important;
      position: relative;
      top: 1px;
    }
  }

  &-state {
    display: flex;
    flex-direction: column;
    width: fit-content;
    align-items: flex-end;
    white-space: nowrap;

    &-date {
      font-size: @yx-font-size-12;
      color: @yx-text-color-2;
    }

    &-mute {
      color: @yx-icon-color-1;
      height: 22px;
    }
  }

  &-top {
    background-color: @yx-primary-color-hover;
  }

  &-select {
    background-color: @yx-primary-color-active;
  }

  &:hover {
    background-color: @yx-primary-color-active;
  }
}
