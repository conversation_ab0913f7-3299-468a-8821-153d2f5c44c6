import { makeAutoObservable } from "mobx";
var SessionStore = /** @class */ (function () {
    function SessionStore() {
        this.sessionType = ""; // 初始值
        makeAutoObservable(this);
    }
    // 提供一个方法来设置sessionType
    SessionStore.prototype.setSessionType = function (type) {
        this.sessionType = type;
    };
    return SessionStore;
}());
// 创建SessionStore的实例
var sessionStoreInstance = new SessionStore();
// 将实例作为默认导出
export default sessionStoreInstance;
