/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
/* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
html {
  --ant-primary-color: #1890ff;
  --ant-primary-color-hover: #40a9ff;
  --ant-primary-color-active: #096dd9;
  --ant-primary-color-outline: rgba(24, 144, 255, 0.2);
  --ant-primary-1: #e6f7ff;
  --ant-primary-2: #bae7ff;
  --ant-primary-3: #91d5ff;
  --ant-primary-4: #69c0ff;
  --ant-primary-5: #40a9ff;
  --ant-primary-6: #1890ff;
  --ant-primary-7: #096dd9;
  --ant-primary-color-deprecated-pure: ;
  --ant-primary-color-deprecated-l-35: #cbe6ff;
  --ant-primary-color-deprecated-l-20: #7ec1ff;
  --ant-primary-color-deprecated-t-20: #46a6ff;
  --ant-primary-color-deprecated-t-50: #8cc8ff;
  --ant-primary-color-deprecated-f-12: rgba(24, 144, 255, 0.12);
  --ant-primary-color-active-deprecated-f-30: rgba(230, 247, 255, 0.3);
  --ant-primary-color-active-deprecated-d-02: #dcf4ff;
  --ant-success-color: #52c41a;
  --ant-success-color-hover: #73d13d;
  --ant-success-color-active: #389e0d;
  --ant-success-color-outline: rgba(82, 196, 26, 0.2);
  --ant-success-color-deprecated-bg: #f6ffed;
  --ant-success-color-deprecated-border: #b7eb8f;
  --ant-error-color: #ff4d4f;
  --ant-error-color-hover: #ff7875;
  --ant-error-color-active: #d9363e;
  --ant-error-color-outline: rgba(255, 77, 79, 0.2);
  --ant-error-color-deprecated-bg: #fff2f0;
  --ant-error-color-deprecated-border: #ffccc7;
  --ant-warning-color: #faad14;
  --ant-warning-color-hover: #ffc53d;
  --ant-warning-color-active: #d48806;
  --ant-warning-color-outline: rgba(250, 173, 20, 0.2);
  --ant-warning-color-deprecated-bg: #fffbe6;
  --ant-warning-color-deprecated-border: #ffe58f;
  --ant-info-color: #1890ff;
  --ant-info-color-deprecated-bg: #e6f7ff;
  --ant-info-color-deprecated-border: #91d5ff;
}
.chat-message-input {
  padding: 16px;
}
.chat-message-input-wrap {
  position: relative;
  background: #ffffff;
  border: 1px solid #dde0e5;
  border-radius: 4px;
}
.chat-message-input-wrap .chat-message-input-reply-wrap {
  position: relative;
  padding: 12px 15px 0;
}
.chat-message-input-wrap .chat-message-input-reply-wrap .chat-message-input-reply-container {
  height: 30px;
  background: #f1f5f8;
  border-radius: 4px;
  color: #999999;
  display: flex;
  align-items: center;
  padding: 0 10px;
  font-size: 12px;
}
.chat-message-input-wrap .chat-message-input-reply-wrap .chat-message-input-reply-container .chat-message-input-reply-content {
  border-left: 1px solid #dde0e5;
  margin-left: 8px;
  padding-left: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}
.chat-message-input-wrap .chat-message-input-content {
  display: flex;
  position: relative;
}
.chat-message-input-wrap .chat-message-input-popup-container {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  bottom: 0;
}
.chat-message-input-wrap .chat-message-input-popup-container .ant-popover-inner-content {
  width: 100%;
}
.chat-message-input-wrap .chat-message-input-at-popover {
  right: 0 !important;
  left: 0 !important;
}
.chat-message-input-wrap .chat-message-input-at-popover .ant-popover-inner {
  border-radius: 4px;
}
.chat-message-input-wrap .chat-message-input-at-popover .ant-popover-inner-content {
  padding: 0;
}
.chat-message-input-wrap .chat-message-input-textarea {
  resize: 'none';
  line-height: '22px';
  padding: 12px;
  border: none;
  outline: none;
}
.chat-message-input-wrap .chat-message-input-textarea::-webkit-input-placeholder {
  display: block;
  content: 'line@ A line#';
  /*  A 表示换行 test */
}
.chat-message-input-wrap .chat-message-input-icon-box {
  padding: 6px 0 2px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.chat-message-input-wrap .chat-message-input-icon-box .ant-btn {
  padding: 0;
  border: none;
  height: auto;
  margin-right: 12px;
  background: none;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-loading-spin {
  font-size: 17px;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-emoji,
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-image,
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-file {
  cursor: pointer;
  color: #656a72;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-upload {
  line-height: 0px;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-upload .ant-upload {
  line-height: 0px;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-emoji {
  font-size: 20px;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-image {
  font-size: 20px;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-file {
  font-size: 18px;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-msg {
  font-size: 20px;
  color: #d7dadd;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-msg-select {
  font-size: 20px;
  color: #537ff4;
}
.chat-message-input-wrap .chat-message-input-icon-box .chat-message-input-icon-msg-select:hover {
  font-size: 20px;
  color: #1861df;
}
.chat-message-input-emoji-box {
  overflow: hidden;
}
.chat-message-input-emoji-box .ant-popover-inner-content {
  width: 414px;
  max-height: 327px;
  padding: 16px 14px;
}
.chat-message-input-emoji-box .chat-message-input-emoji-item {
  display: inline-block;
  width: 22px;
  height: 22px;
  margin: 7px 10px;
  cursor: pointer;
}
.chat-message-input-emoji-box .chat-message-input-emoji-item .chat-message-input-emoji-item-icon {
  font-size: 23px;
}
.chat-message-input-emoji-box .chat-message-input-emoji-item:hover {
  background: #d7e4ff;
}
