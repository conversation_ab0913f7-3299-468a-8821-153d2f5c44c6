@import '~antd/lib/style/themes/variable.less';
@import '../../../style/theme.less';

@prefix-cls: ~'@{chat-prefix}-at-member';

.@{prefix-cls}-wrap {
  padding: 10px 0;
  max-height: 160px;
  overflow-y: auto;
  width: 100%;
  .@{prefix-cls}-item {
    padding: 0 10px;
    display: flex;
    align-items: center;
    height: 40px;
    cursor: pointer;
    &.@{prefix-cls}-item-active {
      background: @yx-background-color-5;
    }
    .@{prefix-cls}-all-icon {
      width: 28px;
      height: 28px;
      background-color: @yx-text-color-4;
      color: #fff;
      border-radius: 50%;
      text-align: center;
      height: 28px;
      font-size: 16px;
    }
    .@{prefix-cls}-label {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 10px;
    }
  }
}
