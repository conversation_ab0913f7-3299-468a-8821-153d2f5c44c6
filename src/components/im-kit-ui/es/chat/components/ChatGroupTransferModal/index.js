var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
import React, { useCallback, useMemo, useState } from 'react';
import { message } from 'antd';
import { ComplexAvatarContainer, useStateContext, useTranslation, } from '../../../common';
import { SelectModal } from '../../../common';
var GroupTransferModal = function (_a) {
    var members = _a.members, onOk = _a.onOk, visible = _a.visible, onCancel = _a.onCancel, teamId = _a.teamId, _b = _a.commonPrefix, commonPrefix = _b === void 0 ? 'common' : _b;
    var _c = __read(useState(''), 2), selectedMemberId = _c[0], setSelectedMemberId = _c[1]; // 选中的成员 ID
    var t = useTranslation().t;
    var store = useStateContext().store;
    var handleCancel = function () {
        onCancel();
        setSelectedMemberId('');
    };
    var handleOk = function () { return __awaiter(void 0, void 0, void 0, function () {
        var error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    return [4 /*yield*/, store.teamStore.transferTeamActive({
                            account: selectedMemberId,
                            teamId: teamId,
                        })];
                case 1:
                    _a.sent();
                    message.success(t('transferTeamSuccessText'));
                    onOk();
                    return [3 /*break*/, 3];
                case 2:
                    error_1 = _a.sent();
                    switch (error_1 === null || error_1 === void 0 ? void 0 : error_1.code) {
                        // 无权限
                        case 802:
                            message.error(t('noPermission'));
                            break;
                        default:
                            message.error(t('transferTeamFailedText'));
                            break;
                    }
                    return [3 /*break*/, 3];
                case 3: return [2 /*return*/];
            }
        });
    }); };
    var handleSelect = useCallback(function (value) {
        setSelectedMemberId(value[0].key);
    }, []);
    var datasource = useMemo(function () {
        var _showMembers = members.map(function (item) {
            return __assign(__assign({}, item), { key: item.account, disabled: item.type === 'owner', label: store.uiStore.getAppellation({
                    account: item.account,
                    teamId: item.teamId,
                }) });
        });
        return _showMembers;
    }, [[members]]);
    var itemAvatarRender = useCallback(function (data) {
        return (React.createElement(ComplexAvatarContainer, { prefix: commonPrefix, canClick: false, account: data.key, size: 32 }));
    }, [commonPrefix]);
    return (React.createElement(SelectModal, { title: t('transferOwnerText'), searchPlaceholder: t('searchTeamMemberPlaceholder'), leftTitle: t('teamMemberText'), visible: visible, datasource: datasource, itemAvatarRender: itemAvatarRender, onSelectChange: handleSelect, type: "radio", min: 1, okText: t('okText'), onOk: handleOk, onCancel: handleCancel, prefix: commonPrefix }));
};
export default GroupTransferModal;
