var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
import React, { useState } from 'react';
import { Modal, Radio, Input, message, Button } from 'antd';
import { ComplexAvatarContainer, useStateContext, useTranslation, } from '../../../common';
import { SearchOutlined, CloseOutlined } from '@ant-design/icons';
var GroupActionModal = function (_a) {
    var title = _a.title, members = _a.members, onOk = _a.onOk, visible = _a.visible, onCancel = _a.onCancel, teamId = _a.teamId, onTeamMemberSearchChange = _a.onTeamMemberSearchChange, groupSearchText = _a.groupSearchText, _b = _a.prefix, prefix = _b === void 0 ? 'chat' : _b, _c = _a.commonPrefix, commonPrefix = _c === void 0 ? 'common' : _c;
    var _d = __read(useState(''), 2), selectedMemberId = _d[0], setSelectedMemberId = _d[1]; // 选中的成员 ID
    var handleMemberSelect = function (e) {
        setSelectedMemberId(e.target.value);
    };
    var t = useTranslation().t;
    var store = useStateContext().store;
    var _prefix = "".concat(prefix, "-group-action-modal");
    var handleCancel = function () {
        onCancel();
        setSelectedMemberId('');
    };
    var handleOk = function () {
        try {
            onOk(selectedMemberId);
        }
        catch (error) {
            message.error(t('transferTeamFailedText'));
        }
    };
    var handleSearch = function (searchText) {
        onTeamMemberSearchChange(searchText);
    };
    var handleSelectReset = function () {
        setSelectedMemberId('');
    };
    return (React.createElement(Modal, { title: title, visible: visible, onOk: handleOk, onCancel: handleCancel, okButtonProps: { disabled: !selectedMemberId }, closable: false, width: 720 },
        React.createElement("div", { className: "".concat(_prefix, "-content") },
            React.createElement("div", { className: "".concat(_prefix, "-content-left") },
                React.createElement(Input, { prefix: React.createElement(SearchOutlined, { style: { color: '#b3b7bc' } }), allowClear: true, value: groupSearchText, className: "".concat(_prefix, "-content-input"), placeholder: t('searchTeamMemberPlaceholder'), onChange: function (e) { return handleSearch(e.target.value); } }),
                members.length ? (React.createElement("div", null,
                    React.createElement("div", { className: "".concat(_prefix, "-content-sub-title") }, t('teamMemberText')),
                    React.createElement(Radio.Group, { style: { width: '100%' }, value: selectedMemberId, defaultValue: null, onChange: handleMemberSelect }, members.map(function (member) {
                        return (React.createElement("div", { key: member.account, className: "".concat(_prefix, "-content-member ").concat(member.account === selectedMemberId
                                ? "".concat(_prefix, "-content-member-focus")
                                : '') },
                            React.createElement(Radio, { value: member.account, disabled: member.type === 'owner' }),
                            React.createElement(ComplexAvatarContainer, { prefix: commonPrefix, canClick: false, account: member.account, size: 32 }),
                            React.createElement("span", { className: "".concat(_prefix, "-content-member-text") }, store.uiStore.getAppellation({
                                account: member.account,
                                teamId: member.teamId,
                            }))));
                    })))) : (React.createElement("div", { className: "".concat(_prefix, "-content-no-result") }, t('searchNoResText')))),
            React.createElement("div", { className: "".concat(_prefix, "-content-right") },
                React.createElement("div", { className: "".concat(_prefix, "-content-sub-title") },
                    t('selectedText'),
                    selectedMemberId ? 1 : 0,
                    "/1"),
                selectedMemberId ? (React.createElement("div", { className: "".concat(_prefix, "-content-select") },
                    React.createElement(ComplexAvatarContainer, { prefix: commonPrefix, canClick: false, account: selectedMemberId, size: 32 }),
                    React.createElement("span", { className: "".concat(_prefix, "-content-right-text") }, store.uiStore.getAppellation({
                        account: selectedMemberId,
                        teamId: teamId,
                    })),
                    React.createElement(Button, { style: { marginLeft: 'auto' }, type: "text", icon: React.createElement(CloseOutlined, { className: "".concat(_prefix, "-content-close") }), onClick: handleSelectReset }))) : null))));
};
export default GroupActionModal;
