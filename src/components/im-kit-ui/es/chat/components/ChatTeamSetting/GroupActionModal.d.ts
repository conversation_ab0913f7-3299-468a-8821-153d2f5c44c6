import React from 'react';
import { TeamMember } from 'nim-web-sdk-ng/dist/NIM_BROWSER_SDK/TeamServiceInterface';
import { FriendProfile } from 'nim-web-sdk-ng/dist/NIM_BROWSER_SDK/FriendServiceInterface';
interface GroupActionModalProps {
    title: string;
    visible: boolean;
    members: (TeamMember & Partial<FriendProfile>)[];
    onOk: (account: string) => void;
    onCancel: () => void;
    prefix?: string;
    commonPrefix?: string;
    teamId: string;
    groupSearchText: string;
    onTeamMemberSearchChange: (searchText: string) => void;
}
declare const GroupActionModal: React.FC<GroupActionModalProps>;
export default GroupActionModal;
