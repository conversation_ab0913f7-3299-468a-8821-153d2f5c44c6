<template>
    <div :style="{ height: height + 'px' }">
      <div v-if="value" :style="{ width: width + 'px',height: height + 'px' }"  style="position: relative">
        <el-image :style="{ width: width + 'px',height: height + 'px' }" :src="value" :preview-src-list="[value]">
        </el-image>
        <i class="el-icon-delete del" @click="handleRemove(index)"></i>
      </div>
  
      <div v-if="!value" :style="{ width: width + 'px',height: height + 'px' }" class="uploadBoxBorder" style="position: relative ">
        <i class="el-icon-plus"></i>
        <input
          ref="upload"
          type="file"
          @change="beforeUpload"
          accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"
          class="picUpload_btn"
        />
      </div>
  
      <el-dialog :visible.sync="dialogVisible" :append-to-body="true">
        <img :src="fileList[0].url" width="100%" alt="" />
      </el-dialog>
      <img
        ref="waterImg"
        style="height: 0;width:0;"
        src="../../assets/images/water_pc.png"
      />
    </div>
  </template>
  <script>
  import axios from 'axios';
  import { policy } from '@/api/oss';
  import ImageCompressor from 'js-image-compressor';
  import { fileByBase64, base64ToFile } from '@/utils/index';
  const webkk = process.env.BASE_API_FED;
  export default {
    name: 'SingleUpload',
    props: {
      value: String,
      isDeletWaterList: {
        type: Number,
        default: 1
      },
      height: {
        type: String,
        default: '100'
      },
      width: {
        type: String,
        default: '100'
      }
    },
    data() {
      return {
        dataObj: {
          policy: '',
          signature: '',
          key: '',
          ossaccessKeyId: '',
          dir: '',
          host: ''
          // callback:'',
        },
        dialogVisible: false,
        useOss: true, // 使用oss->true;使用MinIO->false
        ossUploadUrl: 'https://images2.kkzhw.com',
        minioUploadUrl: `${webkk}/minio/upload`
      };
    },
    computed: {
      imageUrl() {
        return this.value;
      },
      imageName() {
        if (this.value != null && this.value !== '') {
          return this.value.substr(this.value.lastIndexOf('/') + 1);
        } else {
          return null;
        }
      },
      fileList() {
        return [
          {
            name: this.imageName,
            url: this.imageUrl
          }
        ];
      },
      showFileList: {
        get: function() {
          return (
            this.value !== null && this.value !== '' && this.value !== undefined
          );
        },
        set: function(newValue) {}
      }
    },
    methods: {
      compressionImage(file) {
        return new Promise((resolve, reject) => {
          new ImageCompressor({
            file: file,
            quality: 0.2,
            convertSize: 100000, // 1MB 的都要压缩
            redressOrientation: false,
            beforeCompress: function(result) {
              // console.log('压缩之前图片尺寸大小: ', result.size);
              // console.log('mime 类型: ', result.type);
            },
            success: function(result) {
              // console.log('压缩之后图片尺寸大小: ', result.size);
              // console.log('mime 类型: ', result.type);
              // console.log(
              //   '压缩率： ',
              //   (((file.size - result.size) / file.size) * 100).toFixed(2) + '%'
              // );
              let file = new File([result], result.name, { type: result.type });
              resolve(file);
            },
            error(e) {
              reject(e);
            }
          });
        });
      },
      async imgToCanvas(base64) {
        // 创建img元素
        const img = document.createElement('img');
        img.setAttribute('src', base64);
        await new Promise(resolve => (img.onload = resolve));
        // 创建canvas DOM元素，并设置其宽高和图片一样
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        // 坐标(0,0) 表示从此处开始绘制，相当于偏移。
        canvas.getContext('2d').drawImage(img, 0, 0);
        return canvas;
      },
      /**
       * canvas添加水印
       * @param  canvas 对象
       * @param text 水印文字
       */
      async addWatermark(canvas, text) {
        const ctx = canvas.getContext('2d');
        // 给上传的图片添加-水印图片
        const pattern = ctx.createPattern(this.$refs.waterImg, 'repeat');
        ctx.fillStyle = pattern;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        return canvas;
      },
      /**
       * canvas转成img
       * @param {canvas对象} canvas
       */
      convasToImg(canvas) {
        // 新建Image对象，可以理解为DOM
        const image = new Image();
        // canvas.toDataURL 返回的是一串Base64编码的URL
        // 指定格式 PNG
        image.src = canvas.toDataURL('image/png');
        return image;
      },
      emitInput(val) {
        this.$emit('input', val);
      },
      handleRemove(file, fileList) {
        this.emitInput('');
      },
      handlePreview(file) {
        this.dialogVisible = true;
      },
      customUpload(file) {
        const formData = new FormData();
        const data = this.dataObj;
        Object.keys(data).forEach(key => {
          if (key !== 'fileName') {
            formData.append(key, data[key]);
          }
        });
        formData.append('success_action_status', '200');
        formData.append('file', file, file.name);
        axios
          .post(this.ossUploadUrl, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          })
          .then(res => {
            if (res.status === 200) {
              this.handleUploadSuccess(file);
            }
          });
      },
      addWater(file) {
        return new Promise((resolve, reject) => {
          if (this.isDeletWaterList !== 1) {
            fileByBase64(file, async base64 => {
              // 2. 调用方法2：把base64转换为Canvas
              const tempCanvas = await this.imgToCanvas(base64);
              // 3.调用方法3： 写入水印到Canvas
              const canvas = await this.addWatermark(tempCanvas);
              // 4. 调用方法4：把Canvas转换为image文件
              const img = this.convasToImg(canvas);
              // 5.调用方法5：被image转换为File文件(第二个参数为文件名)
              const newFile = base64ToFile(img.src, file.name);
              resolve(newFile);
            });
          } else {
            resolve(file);
          }
        });
      },
      rename(file, fineName) {
        const copyFile = new File([file], fineName, {
          type: file.type
        });
        return copyFile;
      },
      async beforeUpload(event) {
        const files = event.target.files;
        const file = files[0];
        const _self = this;
  
        const copyFile = await policy()
          .then(response => {
            const ext = file.name.split('.').pop();
            const fileName = response.data.fileName;
            _self.dataObj.policy = response.data.policy;
            _self.dataObj.signature = response.data.signature;
            _self.dataObj.ossaccessKeyId = response.data.accessKeyId;
            _self.dataObj.key = response.data.dir + `/${fileName}.${ext}`;
            _self.dataObj.dir = response.data.dir;
            _self.dataObj.host = response.data.host;
            const copyFile = this.rename(file, `${fileName}.${ext}`);
            // _self.dataObj.callback = response.data.callback;
            return copyFile;
          })
          .catch(err => {
            console.log(err);
            return false;
          });
        if (!copyFile) {
          return false;
        }
        // const copyFile = this.rename(file);
        let newFile = await this.addWater(copyFile);
        // newFile = await this.compressionImage(newFile);
        return new Promise((resolve, reject) => {
          if (!this.useOss) {
            // 不使用oss不需要获取策略
            resolve(newFile);
          } else {
            // resolve(newFile);
            this.customUpload(newFile);
          }
        });
      },
      handleUploadSuccess(file) {
        this.showFileList = true;
        this.fileList.pop();
        let url;
        if (!this.useOss) {
          // 不使用oss直接获取图片路径
          // url = res.data.url;
        } else {
          url = this.dataObj.host + '/' + this.dataObj.dir + '/' + file.name;
        }
        this.fileList.push({ name: file.name, url: url });
        this.emitInput(this.fileList[0].url);
        this.$refs.upload.value = '';
      }
    }
  };
  </script>
  <style lang="scss" scoped>
  .del {
    position: absolute;
    top: 5px;
    right: 10px;
    width: 20px;
    height: 20px;
    line-height: 20px;
    background-color: #fff;
    text-align: center;
  }
  .picUpload_pic {
    width: 100px;
    height: 100px;
  }
  /deep/ .el-upload-list--picture-card {
    .is-ready {
      display: none !important;
    }
  }
  .pic-box {
    display: flex;
  }
  .picUpload_btn {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  .uploadBoxBorder{
    // width: 100px;
    // height: 100px;
    border:1px dashed #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  </style>
  