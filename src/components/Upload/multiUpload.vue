<template>
  <div>
    <div class="spaceStart" style="align-items: flex-start;flex-wrap: wrap;">
      <div class="custom-image-box">
        <div
          ref="upload"
          class="spaceStart"
          style="flex-wrap: wrap;align-items: flex-start;"
        >
          <div
            v-for="(item, index) in fileList"
            :key="item.url"
            class="custom-image-item"
          >
            <el-select
              v-if="hasImgType"
              @change="v => changeImgType(v, index)"
              v-model="item.name"
            >
              <el-option
                v-for="ele in options"
                :key="ele.name"
                :label="ele.name"
                :value="ele.name"
              ></el-option>
            </el-select>
            <el-image
              class="custom-img"
              :preview-src-list="fileList.map(ele => ele.url)"
              :src="item.url"
              alt="uploaded image"
            />
            <el-button
              icon="el-icon-delete"
              @click="handleRemove(index)"
            ></el-button>
          </div>
          <div class="el-upload--picture-card" style="position: relative;">
            <i class="el-icon-plus" />
            <input
              type="file"
              ref="inputList"
              multiple
              @change="uploadList"
              class="picUpload_btn"
              accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"
            />
          </div>
        </div>
      </div>
      <!-- <el-upload
        ref="upload"
        :multiple="true"
        action=""
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        :on-success="handleUploadSuccess"
        :on-preview="handlePreview"
        :limit="maxCount"
        :http-request="customUpload"
        :on-exceed="handleExceed"
        list-type="picture-card"
      >
        <i class="el-icon-plus" />
      </el-upload> -->
    </div>
    <el-dialog :visible.sync="dialogVisible" :append-to-body="true">
      <el-image
        :src="dialogImageUrl"
        :preview-src-list="[dialogImageUrl]"
        width="100%"
        alt=""
      />
    </el-dialog>
    <img
      ref="waterImg"
      style="height: 0;width:0;"
      src="../../assets/images/water_pc.png"
    />
  </div>
</template>
<script>
import _ from 'lodash';
import { policy } from '@/api/oss';
import Sortable from 'sortablejs';
import ImageCompressor from 'js-image-compressor';
import { fileByBase64, base64ToFile } from '@/utils/index';
import axios from 'axios';
const webkk = process.env.BASE_API;
export default {
  name: 'MultiUpload',
  props: {
    hasImgType: {
      type: Boolean,
      default: false
    },
    // 图片属性数组
    value: Array,
    // 最大上传图片数量
    maxCount: {
      type: Number,
      default: 100
    },
    isDeletWaterList: {
      type: Number,
      default: 1
    },
    options: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      dataObj: {
        policy: '',
        signature: '',
        key: '',
        ossaccessKeyId: '',
        dir: '',
        host: ''
      },
      dialogVisible: false,
      dialogImageUrl: null,
      useOss: true, // 使用oss->true;使用MinIO->false
      ossUploadUrl: 'https://images2.kkzhw.com',
      minioUploadUrl: `${webkk}/minio/upload`
    };
  },
  computed: {
    fileList() {
      const fileList = [];
      for (let i = 0; i < this.value.length; i++) {
        if (this.hasImgType) {
          fileList.push({ url: this.value[i].url, name: this.value[i].name });
        } else {
          fileList.push({ url: this.value[i] });
        }
      }
      return fileList;
    }
  },
  mounted() {
    this.initDragSort();
  },
  methods: {
    changeImgType() {
      this.emitInput(this.fileList);
    },
    compressionImage(file) {
      return new Promise((resolve, reject) => {
        new ImageCompressor({
          file: file,
          quality: 0.2,
          convertSize: 100000, // 1MB 的都要压缩
          redressOrientation: false,
          beforeCompress: function(result) {
            // console.log('压缩之前图片尺寸大小: ', result.size);
            // console.log('mime 类型: ', result.type);
          },
          success: function(result) {
            // console.log('压缩之后图片尺寸大小: ', result.size);
            // console.log('mime 类型: ', result.type);
            // console.log(
            //   '压缩率： ',
            //   (((file.size - result.size) / file.size) * 100).toFixed(2) + '%'
            // );
            let file = new File([result], result.name, { type: result.type });
            resolve(file);
          },
          error(e) {
            reject(e);
          }
        });
      });
    },
    async imgToCanvas(base64) {
      // 创建img元素
      const img = document.createElement('img');
      img.setAttribute('src', base64);
      await new Promise(resolve => (img.onload = resolve));
      // 创建canvas DOM元素，并设置其宽高和图片一样
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      // 坐标(0,0) 表示从此处开始绘制，相当于偏移。
      canvas.getContext('2d').drawImage(img, 0, 0);
      return canvas;
    },
    // async datadragEnd(evt) {
    //   evt.preventDefault();
    //   let arr = this.fileList;
    //   this.$emit('dragSuccsessList', arr, this.nameKey);
    // },
    // onMove({ relatedContext, draggedContext }) {
    //   const relatedElement = relatedContext.item;
    //   const draggedElement = draggedContext.item;
    // },
    /**
     * canvas添加水印
     * @param  canvas 对象
     * @param text 水印文字
     */
    async addWatermark(canvas, text) {
      const ctx = canvas.getContext('2d');
      // 给上传的图片添加-水印图片
      const pattern = ctx.createPattern(this.$refs.waterImg, 'repeat');
      ctx.fillStyle = pattern;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      return canvas;
    },
    /**
     * canvas转成img
     * @param {canvas对象} canvas
     */
    convasToImg(canvas) {
      // 新建Image对象，可以理解为DOM
      const image = new Image();
      // canvas.toDataURL 返回的是一串Base64编码的URL
      // 指定格式 PNG
      image.src = canvas.toDataURL('image/png');
      return image;
    },
    initDragSort() {
      const el = this.$refs.upload;
      Sortable.create(el, {
        onEnd: ({ oldIndex, newIndex }) => {
          // 交换位置
          const arr = this.fileList;
          const page = arr[oldIndex];
          if (!page) {
            return;
          }
          arr.splice(oldIndex, 1);
          arr.splice(newIndex, 0, page);
          this.emitInput(arr);
        }
      });
    },
    emitInput(fileList) {
      const value = [];
      for (let i = 0; i < fileList.length; i++) {
        if (this.hasImgType) {
          value.push({
            url: fileList[i].url,
            name: fileList[i].name
          });
        } else {
          value.push(fileList[i].url);
        }
      }
      this.$emit('input', value);
    },
    handleRemove(index) {
      this.fileList.splice(index, 1);
      this.emitInput(this.fileList);
    },
    handlePreview(file) {
      this.dialogVisible = true;
      this.dialogImageUrl = file.url;
    },
    addWater(file) {
      return new Promise((resolve, reject) => {
        if (this.isDeletWaterList !== 1) {
          fileByBase64(file, async base64 => {
            // 2. 调用方法2：把base64转换为Canvas
            const tempCanvas = await this.imgToCanvas(base64);
            // 3.调用方法3： 写入水印到Canvas
            const canvas = await this.addWatermark(tempCanvas);
            // 4. 调用方法4：把Canvas转换为image文件
            const img = this.convasToImg(canvas);
            // 5.调用方法5：被image转换为File文件(第二个参数为文件名)
            const newFile = base64ToFile(img.src, file.name);
            resolve(newFile);
          });
        } else {
          resolve(file);
        }
      });
    },
    rename(file, fineName) {
      // const timeStamp = new Date().getTime();
      // const name = `${timeStamp}_${file.name}`;
      const copyFile = new File([file], fineName, {
        type: file.type
      });
      copyFile.uid = file.uid;
      // const index = this.$refs.upload.uploadFiles.findIndex(ele => {
      //   return ele.uid === file.uid;
      // });
      // this.$refs.upload.uploadFiles[index].raw = copyFile;
      // this.$refs.upload.uploadFiles[index].name = copyFile.name;
      // this.$refs.upload.uploadFiles[index].url = URL.createObjectURL(copyFile);
      return copyFile;
    },
    customUpload(file) {
      const formData = new FormData();
      const data = this.dataObj[file.uid];
      Object.keys(data).forEach(key => {
        if (key !== 'fileName') {
          formData.append(key, data[key]);
        }
      });
      formData.append('success_action_status', '200');
      formData.append('file', file, file.name);
      axios
        .post(this.ossUploadUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        .then(res => {
          if (res.status === 200) {
            this.handleUploadSuccess(file);
          }
        });
    },
    uploadList(event) {
      const files = event.target.files;
      for (let i = 0; i < files.length; i++) {
        let file = files[i];
        file.uid = _.uniqueId('file_');
        this.beforeUpload(file).then(data => {
          this.customUpload(data);
        });
      }
    },
    async beforeUpload(file) {
      const _self = this;
      const copyFile = await policy()
        .then(response => {
          const ext = file.name.split('.').pop();
          const uid = file.uid;
          const fileName = response.data.fileName;
          _self.dataObj[uid] = {};
          _self.dataObj[uid].fileName = fileName;
          _self.dataObj[uid].policy = response.data.policy;
          _self.dataObj[uid].signature = response.data.signature;
          _self.dataObj[uid].ossaccessKeyId = response.data.accessKeyId;
          _self.dataObj[uid].key = response.data.dir + `/${fileName}.${ext}`;
          _self.dataObj[uid].dir = response.data.dir;
          _self.dataObj[uid].host = response.data.host;
          const copyFile = this.rename(file, `${fileName}.${ext}`);
          // _self.dataObj.callback = response.data.callback;
          return copyFile;
        })
        .catch(err => {
          console.log(err);
          return false;
        });
      if (!copyFile) {
        return false;
      }

      let newFile = await this.addWater(copyFile);
      // newFile = await this.compressionImage(newFile);
      newFile.uid = file.uid;
      return new Promise((resolve, reject) => {
        resolve(newFile);
      });
    },
    handleUploadSuccess(file) {
      const uid = file.uid;
      let url = this.dataObj[uid].host + '/' + this.dataObj[uid].key;
      let obj = {
        name: file.name,
        url
      };
      if (this.hasImgType) {
        obj.name = '';
      }
      this.fileList.push(obj);
      this.emitInput(this.fileList);
      this.$refs.inputList.value = '';
      // if (fList.every(v => v.status === 'success')) {
      //   const list = fList.filter(item => item.percentage === 100);
      //   list.forEach(ele => {
      //     const uid = ele.uid;
      //     let url =
      //       this.dataObj[uid].host +
      //       '/' +
      //       this.dataObj[uid].dir +
      //       '/' +
      //       ele.name;
      //     // if (!this.useOss) {
      //     //   // 不使用oss直接获取图片路径
      //     //   url = res.data.url
      //     // }

      //     this.fileList.push({ name: ele.name, url: url });
      //   });
      //   this.emitInput(this.fileList);
      // }
    },
    handleExceed(files, fileList) {
      this.$message({
        message: '最多只能上传' + this.maxCount + '张图片',
        type: 'warning',
        duration: 1000
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.picUpload_btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.custom-image-item {
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
  position: relative;
  .el-select {
    width: 120px !important;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
  }
}

.custom-image-item .custom-img {
  width: 200px; /* 根据需要设置 */
  height: 200px; /* 根据需要设置 */
  object-fit: cover;
  cursor: pointer;
}

.custom-image-item .el-button {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 0 0 5px 0;
  cursor: pointer;
}
/deep/ .el-upload--picture-card {
  height: 200px;
  width: 200px;
  text-align: center;
}
/deep/ .el-upload-list__item {
  display: none;
}
/deep/ .el-upload-list--picture-card {
  .is-ready {
    display: none !important;
  }
}
</style>
