<template>
  <el-dialog
    :close-on-click-modal="false"
    width="40%"
    :visible.sync="dialogVisible"
    append-to-body
    top="4vh"
    title="会话统计"
    :before-close="onCancel"
  >
    <div v-if="isShowDetail">
      <div>{{ showDetailTitle }}</div>
      <el-table
        ref="productTable"
        :data="detailList"
        style="width: 100%"
        border
      >
        <el-table-column type="index" width="60" label="序号"></el-table-column>
        <el-table-column label="用户IM" align="center">
          <template slot-scope="scope">
            <el-link @click="goChat(scope.row)" type="primary">{{
              scope.row.buyerim
            }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center">
          <template slot-scope="scope">
            {{ util.timeFormat(scope.row.updateTime) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="spaceEnd">
        <el-link @click="isShowDetail = false" type="primary">返回</el-link>
      </div>
      <div class="pagination-container">
        <el-pagination
          :page-size="listQuery.pageSize"
          :page-sizes="[20, 40, 60]"
          :current-page.sync="listQuery.pageNum"
          :total="total"
          background
          layout="total, sizes,prev, pager, next,jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <div v-else>
      <el-table :data="list2" style="width: 100%" border>
        <el-table-column type="index" width="60" label="序号"></el-table-column>
        <el-table-column label="类型" width="160" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.name }}</p>
          </template>
        </el-table-column>
        <el-table-column label="今日" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.today_new }}</p>
          </template>
        </el-table-column>
        <el-table-column label="昨日" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.yesterday_new }}</p>
          </template>
        </el-table-column>
        <el-table-column label="本月" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.month_new }}</p>
          </template>
        </el-table-column>
        <el-table-column label="总数" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.total }}</p>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-link @click="showDetail(scope.row)" type="primary"
              >查看</el-link
            >
          </template>
        </el-table-column> -->
      </el-table>
      <el-table :data="list" style="width: 100%;margin-top:20px" border>
        <el-table-column type="index" width="60" label="序号"></el-table-column>
        <el-table-column label="类型" width="160" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.name }}</p>
          </template>
        </el-table-column>
        <el-table-column label="今日" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.today_new }}</p>
          </template>
        </el-table-column>
        <el-table-column label="昨日" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.yesterday_new }}</p>
          </template>
        </el-table-column>
        <el-table-column label="本月" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.month_new }}</p>
          </template>
        </el-table-column>
        <el-table-column label="总数" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.total }}</p>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-link @click="showDetail(scope.row)" type="primary"
              >查看</el-link
            >
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div v-if="!isShowDetail" slot="footer" class="dialog-footer">
      <el-button @click="onCancel">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  kferStatics,
  kferPersonStatics,
  personChatStats
} from '@/api/imteamManage';
import util from '@/utils/index';
const defQuery = {
  pageNum: 1,
  pageSize: 20
};
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      util,
      showDetailTitle: '',
      detailList: [],
      list: [],
      list2: [],
      listQuery: Object.assign({}, defQuery),
      isShowDetail: false
    };
  },
  created() {
    this.getKferPersonStatics();
  },
  methods: {
    getKferPersonStatics() {
      kferPersonStatics().then(res => {
        this.list = res.data;
      });
      kferStatics().then(res => {
        this.list2 = res.data;
      });
    },
    onCancel() {
      this.$emit('hide');
    },
    showDetail(item) {
      // teamState
      this.listQuery.teamState = item.team_state;
      personChatStats(this.listQuery).then(res => {
        if (res.code == 200) {
          this.detailList = res.data.list || [];
          this.isShowDetail = true;
          this.showDetailTitle = item.name;
        }
      });
    },
    handleSizeChange() {
      this.listQuery.pageNum = 1;
      personChatStats(this.listQuery).then(res => {
        if (res.code == 200) {
          this.detailList = res.data.list || [];
        }
      });
    },
    handleCurrentChange(pageNum) {
      this.listQuery.pageNum = pageNum;
      personChatStats(this.listQuery).then(res => {
        if (res.code == 200) {
          this.detailList = res.data.list || [];
        }
      });
    },
    goChat(item) {
      const imaccount = item.buyerim;
      const { store } = window.__xkit_store__;
      const sessionId = `p2p-${imaccount}`;
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', imaccount);
      }
      this.onCancel();
    }
  }
};
</script>

<style scoped>
.price {
  margin-right: 10px;
}
</style>
