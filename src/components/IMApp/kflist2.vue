<template>
  <div>
    <div class="table-container">
      <!-- <el-button type="primary" class="add-help" @click="createHelp"
          >新建</el-button
        > -->
      <el-tabs
        class="tabs"
        type="card"
        v-model="activeName"
        @tab-click="changeTab"
      >
        <el-tab-pane
          v-for="(item, index) in cateList"
          :key="index"
          :label="item.name"
          :name="item.name"
        ></el-tab-pane>
      </el-tabs>
      <el-table
        v-loading="listLoading"
        ref="productTable"
        :data="list"
        style="width: 100%"
        border
      >
        <!-- <el-table-column type="selection" width="60" align="center" /> -->
        <el-table-column label="编号" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.id }}</template>
        </el-table-column>
        <el-table-column label="名称" width="120" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.name }}</p>
          </template>
        </el-table-column>
        <el-table-column label="类型" width="120" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.productCategoryName }}</p>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.publishStatus | verifyStatusFilter }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <p>
              <el-button type="danger" size="mini" @click="addIt(scope.row)">
                发送客服
              </el-button>
            </p>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          :current-page.sync="listQuery.pageNum"
          :page-size="listQuery.pageSize"
          :page-sizes="[10, 30, 40]"
          :total="total"
          background
          layout="total, sizes,prev, pager, next,jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <el-dialog
        v-if="showAddModel"
        :visible.sync="showAddModel"
        width="80%"
        top="1vh"
      >
        <el-card>
          <div slot="header" class="clearfix">
            <span>编辑信息</span>
          </div>
          <el-form
            ref="helpForm"
            :model="value"
            :rules="rules"
            :label-width="formLabelWidth"
            class="form-box"
          >
            <el-form-item label="类型">
              <el-select v-model="value.productCategoryId">
                <el-option
                  v-for="item in productCategoryOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="名称">
              <el-input v-model="value.name" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-radio-group v-model="value.publishStatus">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="头像">
              <single-upload v-model="value.pic" />
            </el-form-item>
            <el-form-item label="SKU">
              <el-select
                v-model="value.productAttributeCategoryId"
                @change="handleProductAttrChange"
              >
                <el-option
                  v-for="item in productAttributeCategoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <div v-if="extList['ext1'].needShow" shadow="never" class="ext1">
              <tedian
                class="tedian-box"
                :detail-options="extList['ext1'].detailOptions"
                :opetion-date="extList['ext1'].opetionDate"
                @changequfu="changequfu"
              />
            </div>
            <div v-if="extList['ext2'].needShow" shadow="never" class="ext2">
              <tedian
                class="tedian-box"
                :detail-options="extList['ext2'].detailOptions"
                :opetion-date="extList['ext2'].opetionDate"
                @changequfu="changequfu"
              />
            </div>
            <div v-if="extList['ext3'].needShow" shadow="never" class="ext3">
              <tedian
                class="tedian-box"
                :detail-options="extList['ext3'].detailOptions"
                :opetion-date="extList['ext3'].opetionDate"
                @changequfu="changequfu"
              />
            </div>
            <div v-if="extList['ext4'].needShow" shadow="never" class="ext4">
              <tedian
                class="tedian-box"
                :detail-options="extList['ext4'].detailOptions"
                :opetion-date="extList['ext4'].opetionDate"
                @changequfu="changequfu"
              />
            </div>
            <el-form-item label="">
              <tedian
                :detail-options="detailOptions"
                :opetion-date="opetionDate"
              />
            </el-form-item>
          </el-form>
          <div class="m-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitForm('helpForm')"
              >确 定</el-button
            >
          </div>
        </el-card>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { fetchList as fetchProductAttrCateList } from '@/api/productAttrCate';
import { fetchListKF, getHelpDetailKF } from '@/api/help';
import {
  fetchListWithChildren,
  fetchList as fetchListCate
} from '@/api/productCate';
import {
  productKFCreate,
  productKFUpdate,
  productKFDelete
} from '@/api/product';
import { fetchListAll as fetchSubjectList } from '@/api/subject';
import { formatDate } from '@/utils/date';
import { fetchList as fetchListProductCate } from '@/api/productCate';
import SingleUpload from '@/components/Upload/singleUpload';
import { fetchList as fetchProductAttrList } from '@/api/productAttr';
import tedian from '@/components/tedian';
import util from '@/utils/index';

import { sendKFerCard } from '@/api/kf.js';

const defaultListQuery = {
  pageNum: 1,
  pageSize: 10,
  brandId: 63
};

const STARTTIME = '09:00';
const ENDTIME = '00:30';
let idUUid = 0;
export default {
  name: 'Kefuguanli',
  components: { SingleUpload, tedian },
  filters: {
    formatCreateTime(time) {
      const date = new Date(time);
      return formatDate(date, 'YYYY-MM-DD HH:mm:ss');
    },
    verifyStatusFilter(state) {
      return state ? '启用' : '禁用';
    },
    verifyStatusFilter2(state) {
      return state ? '禁用' : '启用';
    }
  },
  props: {
    toIM: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      metaId: '',
      activeName: '',
      extList: {
        ext1: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext2: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext3: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext4: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        }
      },
      productAttributeCategoryOptions: [],
      detailOptions: [],
      opetionDate: [],
      // 所有专题列表
      subjectList: [],
      // 专题左右标题
      subjectTitles: ['待选择', '已选择'],
      // 所有专题列表
      prefrenceAreaList: [],
      // 专题左右标题
      prefrenceAreaTitles: ['待选择', '已选择'],
      productCategoryOptions: [],
      listLoading: true,
      list: [],
      total: 0,
      showAddModel: false,
      listQuery: Object.assign({}, defaultListQuery),
      value: {
        name: '',
        gameCareinfoVx: '',
        gameCareinfoTime: '',
        pic: '',
        productCategoryId: '',
        publishStatus: '',
        subjectProductRelationList: [],
        productAttributeValueList: [],
        productAttributeCategoryId: ''
      },
      rules: {},
      formLabelWidth: '140px',
      cateList: []
    };
  },
  computed: {
    // 选中的专题
    selectSubject: {
      get: function() {
        const subjects = [];
        if (
          this.value.subjectProductRelationList == null ||
          this.value.subjectProductRelationList.length <= 0
        ) {
          return subjects;
        }
        for (let i = 0; i < this.value.subjectProductRelationList.length; i++) {
          subjects.push(this.value.subjectProductRelationList[i].subjectId);
        }
        return subjects;
      },
      set: function(newValue) {
        this.value.subjectProductRelationList = [];
        for (let i = 0; i < newValue.length; i++) {
          this.value.subjectProductRelationList.push({
            subjectId: newValue[i]
          });
        }
      }
    }
  },
  created() {
    this.metaId = 89;
    this.fetchListCate(this.metaId);
  },
  methods: {
    addIt(item) {
      let data = {
        kferName: item.name,
        productId: item.id,
        toIM: this.toIM
      };
      sendKFerCard(data).then(res => {
        if (res.code == 200) {
          this.$message.success(res.message);
          this.$emit('close');
        }
      });
    },
    fetchListCate(id) {
      const param = { pageNum: 1, pageSize: 999 };
      fetchListCate(id, param).then(res => {
        if (res.code == 200) {
          this.cateList = res.data.list || [];
          if (this.cateList.length) {
            this.cateList = this.cateList.filter(ele => ele.name != '机器人');
            this.cateList.sort((a, b) => {
              return b.sort - a.sort;
            });
            this.activeName = this.cateList[1].name;
          }
          this.getList();
          this.getProductAttrCateList();
        }
      });
    },
    changeTab() {
      this.getList();
    },
    handleProductAttrChange(value) {
      this.extList = this.getExtList();
      this.getProductAttrList(1, value);
      this.getProductAttrList(2, value);
      this.getProductAttrList(3, value);
      this.getProductAttrList(4, value);
    },
    changequfu(value) {
      this.value.gameAccountQufu = value;
    },
    getEditAttrOptions2(list) {
      const result = list.map(item => {
        // 1输入框，2单选，3多选
        let tdtype = 1;
        if (item.inputType === 1) {
          if (item.selectType === 1) {
            tdtype = 2;
          } else if (item.selectType === 2) {
            tdtype = 3;
          } else if (item.selectType === 3) {
            tdtype = 4;
          }
        }
        if (tdtype === 1) {
          return Object.assign({}, item, {
            childList: [],
            is_required: 0,
            field_type: 2,
            default_word: '请输入',
            tdtype,
            iptVal: ''
          });
        } else if (tdtype === 2) {
          return Object.assign({}, item, {
            tdtype,
            value: '',
            is_required: 0,
            field_type: 2,
            inputList: item.inputList.split(',')
          });
        } else if (tdtype === 3) {
          const inputList = item.inputList.split(',');
          const childList = [];
          inputList.forEach(ele => {
            childList.push({
              icon: '',
              name: ele,
              checked: false
            });
          });
          return Object.assign({}, item, {
            childList,
            is_required: 0,
            field_type: 2,
            default_word: '点击可下拉选择物品',
            tdtype,
            iptVal: '',
            showChild: false,
            choosedList: [],
            zidingyiList: [],
            iptSearchVal: '',
            searchList: []
          });
        } else {
          const options = JSON.parse(item.inputList);
          options.forEach(ele => {
            ele.value = ele.parent_name;
            ele.label = ele.parent_name;
            const { childList } = ele;
            const children = childList.map(item => {
              return {
                value: item,
                label: item
              };
            });
            ele.children = children;
          });
          return Object.assign({}, item, {
            tdtype,
            value: [],
            is_required: 0,
            field_type: 2,
            options
          });
        }
      });
      return result;
    },
    handleEditCreatedAttr() {
      // 根据商品属性分类id获取属性和参数
      if (this.value.productAttributeCategoryId != null) {
        this.handleProductAttrChange(this.value.productAttributeCategoryId);
      }
    },
    getProductAttrList(type, cid) {
      const param = { pageNum: 1, pageSize: 200, type: type };
      fetchProductAttrList(cid, param).then(response => {
        const list = response.data.list;
        if (type !== 0) {
          let label = `基础信息扩展`;
          if (type === 2) {
            label = `账号信息扩展`;
          } else if (type === 3) {
            label = `其他扩展`;
          }
          const extParam = {
            index: parseInt(type, 10),
            label,
            needShow: list && list.length > 0
          };
          const opetionDate = this.getEditAttrOptions2(list);
          const detailoptList = [];
          extParam.opetionDate = opetionDate;
          for (let i = 0; i < list.length; i++) {
            let detailopt = null;
            if (this.isEdit) {
              // 编辑模式下获取参数属性
              detailopt = this.getEditParamValue2(list[i]);
              detailopt && detailoptList.push(detailopt);
            }
          }
          extParam.detailOptions = detailoptList;
          util.async2opetionDate(extParam.detailOptions, extParam.opetionDate);
          this.$set(this.extList, `ext${type}`, extParam);
        } else {
          if (!this.isEdit) {
            this.selectProductAttr = [];
          }
          for (let i = 0; i < list.length; i++) {
            const options = [];
            let values = [];
            if (this.isEdit) {
              // 编辑状态下获取选中属性
              values = this.getEditAttrValues(i);
            }
            this.selectProductAttr.push({
              id: list[i].id,
              name: list[i].name,
              handAddStatus: list[i].handAddStatus,
              inputList: list[i].inputList,
              values: values,
              options: options
            });
          }
        }
      });
    },
    getEditParamValue2(item) {
      // 1输入框，2单选，3多选,4级联
      let tdtype = 1;
      if (item.inputType === 1) {
        if (item.selectType === 1) {
          tdtype = 2;
        } else if (item.selectType === 2) {
          tdtype = 3;
        } else if (item.selectType === 3) {
          tdtype = 4;
        }
      }
      for (let i = 0; i < this.value.productAttributeValueList.length; i++) {
        if (
          item.id === this.value.productAttributeValueList[i].productAttributeId
        ) {
          const v = this.value.productAttributeValueList[i];
          if (tdtype === 1) {
            return Object.assign({}, item, {
              childList: [],
              is_required: 0,
              field_type: 2,
              default_word: '请输入',
              tdtype,
              iptVal: v.value
            });
          } else if (tdtype === 2) {
            return Object.assign({}, item, {
              childList: [],
              is_required: 0,
              field_type: 2,
              default_word: '请输入',
              tdtype,
              value: v.value
            });
          } else if (tdtype === 3) {
            let values = [];
            if (v.value !== '') {
              values = v.value.split(',');
            }
            const result = [];
            values.forEach(ele => {
              result.push({
                icon: '',
                name: ele,
                checked: true
              });
            });
            return Object.assign({}, item, {
              title: item.name,
              tdtype,
              value: result
            });
          } else {
            return Object.assign({}, item, {
              title: item.name,
              tdtype,
              value: (v.value || '').split('|'),
              options: JSON.parse(item.inputList)
            });
          }
        }
      }
    },
    getEditAttrValues(index) {
      const values = new Set();
      if (index === 0) {
        for (let i = 0; i < this.value.skuStockList.length; i++) {
          const sku = this.value.skuStockList[i];
          const spData = JSON.parse(sku.spData);
          if (spData != null && spData.length >= 1) {
            values.add(spData[0].value);
          }
        }
      } else if (index === 1) {
        for (let i = 0; i < this.value.skuStockList.length; i++) {
          const sku = this.value.skuStockList[i];
          const spData = JSON.parse(sku.spData);
          if (spData != null && spData.length >= 2) {
            values.add(spData[1].value);
          }
        }
      } else {
        for (let i = 0; i < this.value.skuStockList.length; i++) {
          const sku = this.value.skuStockList[i];
          const spData = JSON.parse(sku.spData);
          if (spData != null && spData.length >= 3) {
            values.add(spData[2].value);
          }
        }
      }
      return Array.from(values);
    },
    getProductAttrCateList() {
      const param = { pageNum: 1, pageSize: 999 };
      fetchProductAttrCateList(param).then(response => {
        this.productAttributeCategoryOptions = [];
        const list = response.data.list;
        for (let i = 0; i < list.length; i++) {
          this.productAttributeCategoryOptions.push({
            label: list[i].name,
            value: list[i].id
          });
        }
      });
    },
    getSubjectList() {
      fetchSubjectList().then(response => {
        this.subjectList = [];
        const list = response.data;
        for (let i = 0; i < list.length; i++) {
          if (list[i].categoryId === 4) {
            this.subjectList.push({
              label: list[i].title,
              key: list[i].id
            });
          }
        }
      });
    },
    filterMethod(query, item) {
      return item.label.indexOf(query) > -1;
    },
    getButtonType(state) {
      return state ? 'danger' : 'success';
    },
    toggleState(index, row) {
      const { id } = row;
      const param = Object.assign({}, row);
      param.publishStatus = row.publishStatus ? 0 : 1;
      productKFUpdate(id, param).then(() => {
        this.getList();
      });
    },
    getGameCate() {
      return new Promise((resolve, reject) => {
        Promise.all([
          fetchListProductCate(74, {
            pageNum: 1,
            pageSize: 999
          }),
          fetchListProductCate(73, {
            pageNum: 1,
            pageSize: 999
          })
        ]).then(response => {
          let childList = [];

          childList.push({
            icon: '',
            name: '手游',
            splitTitle: 1,
            checked: false
          });
          const list0 = response[0].data.list.map(item => {
            return {
              icon: '',
              name: item.name,
              checked: false
            };
          });
          childList = childList.concat(list0);
          childList.push({
            icon: '',
            name: '端游',
            splitTitle: 1,
            checked: false
          });
          const list1 = response[1].data.list.map(item => {
            return {
              icon: '',
              name: item.name,
              checked: false
            };
          });
          childList = childList.concat(list1);
          const obj = {
            childList,
            name: '关联游戏',
            is_required: 0,
            field_type: 2,
            default_word: '点击可下拉选择',
            tdtype: 3,
            iptVal: '',
            showChild: false,
            choosedList: [],
            zidingyiList: [],
            iptSearchVal: '',
            searchList: [],
            id: +new Date()
          };
          this.opetionDate = [obj];
          resolve();
        });
      });
    },
    getList() {
      this.listLoading = true;
      fetchListWithChildren().then(response => {
        const list = response.data;
        this.productCategoryOptions = list.find(
          ele => ele.id === this.metaId
        ).children;
        const findIt = this.cateList.find(ele => ele.name === this.activeName);
        this.listQuery.productCategoryId = findIt.id;
        fetchListKF(this.listQuery).then(response => {
          this.listLoading = false;
          const { list } = response.data;
          this.list = list;
          this.list = this.list.filter(ele => ele.publishStatus);
          this.total = response.data.total;
        });
      });
      // 找到账号列表
      //   const menu = store.getters.menus.find(element => element.id === 31)
      //   const url = menu.url
    },
    handleUpdateProduct(index, row) {
      const { id } = row;
      this.clearValue();
      this.getGameCate().then(e => {
        this.getSubjectList();
        getHelpDetailKF(id).then(response => {
          this.value = Object.assign({}, response.data);
          this.id = id;
          this.isEdit = true;
          this.showAddModel = true;
          // this.startTime = this.value.description.split('~')[0];
          // this.endTime = this.value.description.split('~')[1];
          this.detailOptions = [];
          if (this.value.subTitle) {
            const result = [];
            this.value.subTitle.split(',').forEach(ele => {
              result.push({
                icon: '',
                name: ele,
                checked: true
              });
            });
            this.detailOptions = [
              {
                title: '关联游戏',
                tdtype: 3,
                value: result,
                id: idUUid++
              }
            ];
          } else {
            this.detailOptions = [];
          }
          this.handleEditCreatedAttr();
        });
      });
    },
    createHelp() {
      this.getGameCate().then(e => {
        this.getSubjectList();
        this.clearValue();
        this.id = '';
        this.isEdit = false;
        this.showAddModel = true;
        // this.startTime = STARTTIME;
        // this.endTime = ENDTIME;
      });
    },
    getExtList() {
      return {
        ext1: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext2: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext3: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext4: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        }
      };
    },
    clearValue() {
      this.detailOptions = [];
      this.extList = this.getExtList();
      this.value = {
        name: '',
        gameCareinfoVx: '',
        gameCareinfoTime: '',
        pic: '',
        productCategoryId: '',
        publishStatus: '',
        subjectProductRelationList: [],
        productAttributeValueList: [],
        productAttributeCategoryId: ''
      };
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1;
      this.listQuery.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getList();
    },
    cancel() {
      this.showAddModel = false;
    },
    submitForm() {
      this.value.brandId = 63;
      this.value.productAttributeValueList = [];
      const keys = Object.keys(this.extList);
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const opetionDate = this.extList[key].opetionDate;
        for (let i = 0; i < opetionDate.length; i++) {
          const param = opetionDate[i];
          let value = param.value || '';
          // type1输入框，2单选，3多选，4区服
          if (param.tdtype === 3 && param.choosedList.length) {
            value = param.choosedList.map(c => c.name).join(',');
          } else if (param.tdtype === 1) {
            value = param.iptVal;
          } else if (param.tdtype === 4) {
            value = param.value.join('|');
          }
          this.value.productAttributeValueList.push({
            productAttributeId: param.id,
            value,
            attriName: param.name,
            sort: param.sort,
            filterType: param.filterType,
            searchType: param.searchType,
            type: param.type,
            searchSort: param.searchSort
          });
        }
      }
      this.showAddModel = false;
      const choosedList = this.opetionDate[0].choosedList.map(ele => {
        return ele.name;
      });
      this.value.subTitle = choosedList.join(',');
      const findIt = this.productCategoryOptions.find(ele => {
        return this.value.productCategoryId === ele.id;
      });
      this.value.productCategoryName = findIt.name;
      if (this.isEdit) {
        // this.value.description = `${this.startTime}~${this.endTime}`;
        productKFUpdate(this.id, this.value).then(() => {
          this.getList();
        });
      } else {
        const params = Object.assign({}, this.value);
        // this.value.description = `${this.startTime}~${this.endTime}`;
        productKFCreate(params).then(() => {
          this.getList();
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs {
  width: 800px;
}
.ext1 .tedian-box,
.ext2 .tedian-box,
.ext3 .tedian-box,
.ext4 .tedian-box {
  /deep/ .tedian_box_ext {
    width: 100%;
    .tedian_item {
      .teding_name {
        width: 140px;
        word-wrap: break-word;
        line-height: 20px;
      }
      .right_tedian_box {
        margin-left: 140px;
      }
    }
  }
}

.m-footer {
  text-align: right;
  margin-top: 10px;
}
.add-help {
  float: right;
  margin: 0 30px 30px 0;
}
</style>
