<template>
  <div class="kferTransfer el-reset-clazz">
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :close-on-click-modal="false"
      width="33%"
      :modal="false"
      center
      title="创建合同"
    >
      <el-form
        ref="contract"
        :label-width="formLabelWidth"
        :model="contract"
        class="form-box"
        :rules="rules"
      >
        <el-form-item
          :label="huikuanForm.transType == 0 ? '银行卡号' : '支付宝账号'"
          prop="bankNo"
        >
          <el-input
            disabled
            :value="`***${huikuanForm.bankNo.slice(-4)}`"
            placeholder="请输入银行卡号"
          />
        </el-form-item>
        <el-form-item label="收款人" prop="accountName">
          <el-input
            disabled
            v-model="huikuanForm.accountName"
            placeholder="请输入收款人"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="contract.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="游戏账号" prop="gameAccount">
          <el-input
            v-model="contract.gameAccount"
            placeholder="请输入游戏账号"
          />
        </el-form-item>
        <el-form-item label="汇款金额" prop="payAmount">
          <el-input v-model="contract.payAmount" placeholder="请输入合同金额" />
        </el-form-item>
        <el-form-item label="身份证姓名" prop="userIdName">
          <el-input v-model="contract.userIdName" disabled />
        </el-form-item>
        <el-form-item label="身份证号" prop="userIdNumber">
          <el-input v-model="contract.userIdNumber" disabled />
        </el-form-item>
        <el-form-item label="用户地址" prop="userAddress">
          <el-input v-model="contract.userAddress" />
        </el-form-item>
        <el-form-item label="图片">
          <el-image
            class="userImg"
            :src="contract.attachment.userIdPic1"
            :preview-src-list="[contract.attachment.userIdPic1]"
          ></el-image>
          <el-image
            class="userImg"
            :src="contract.attachment.userIdPic2"
            :preview-src-list="[contract.attachment.userIdPic2]"
          ></el-image>
          <el-image
            class="userImg"
            :src="contract.attachment.userOrderPic"
            :preview-src-list="[contract.attachment.userOrderPic]"
          ></el-image>
        </el-form-item>
      </el-form>
      <div class="m-footer spaceEnd">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="onSubmit('contract')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// attachment: '{"userIdPic1":"","userIdPic2":"","userOrderPic":"https://images2.kkzhw.com/mall/images/********/r464yf_1722108729187.jpg"}';
// memberId: 68;
// orderId: 112257;
// orderSn: '*****************';
// payAmount: -8;
// phone: '***********';
// productCategoryName: '剑灵怀旧服';
// productSn: 'JLHJ04227109';
// subject: '游戏账号交易合同';
// userAddress: '123';
// userIdName: '陈李平';
// userIdNumber: '320481198510032813';
// username: '***********';
import { queryBankCode } from '@/api/product';
import { contractPreview, contractCreate, startSignTask } from '@/api/kf';
const defaultValue = {
  bankNo: '',
  accountName: '',
  price: ''
};
export default {
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {};
      }
    },
    actionObj: {
      type: Object,
      default() {
        return {};
      }
    },
    actionData: {
      type: Object,
      default() {
        return {};
      }
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formLabelWidth: '100px',
      huikuanForm: Object.assign({}, defaultValue),
      errorTxt: '',
      rules: {
        gameAccount: [
          { required: true, message: '请输入游戏账号', trigger: 'blur' }
        ],
        payAmount: [{ required: true, message: '请输入金额', trigger: 'blur' }]
      },
      bankList: [],
      contract: {
        attachment: {}
      }
    };
  },
  mounted() {
    const values = this.actionData;
    this.huikuanForm.accountName = values.transName;
    this.huikuanForm.bankNo = values.transCard;
    this.huikuanForm.transType = values.transType;
    this.init();
    this.getContractPreview();
  },
  methods: {
    getContractPreview() {
      let data = {
        orderId: this.orderDetail.id
      };
      contractPreview(data).then(res => {
        if (res.code == 200) {
          this.contract = res.data;
          if (res.data.attachment) {
            this.contract.attachment = JSON.parse(this.contract.attachment);
          }
        }
      });
    },
    init() {
      queryBankCode().then(res => {
        const { data = '[]' } = res;
        this.bankList = JSON.parse(data);
      });
    },
    onCancel() {
      this.huikuanForm = Object.assign({}, defaultValue);
      this.$emit('close');
    },
    onSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let data = Object.assign({}, this.contract);
          data.attachment = JSON.stringify(data.attachment);
          data.bankNo = this.huikuanForm.bankNo;
          data.accountName = this.huikuanForm.accountName;
          contractCreate(data).then(res => {
            if (res.code == 200) {
              this.$message.success('合同创建成功');
              this.onCancel();
            }
          });
        } else {
          return;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.userImg {
  width: 100px;
  height: 100px;
}
</style>
