<template>
  <div class="kferTransfer el-reset-clazz">
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :close-on-click-modal="false"
      width="33%"
      :modal="false"
      center
      title="修改群名"
    >
      <el-form
        ref="postForm"
        :label-width="formLabelWidth"
        :model="postForm"
        class="form-box"
        :rules="rules"
      >
        <el-form-item label="新群名" prop="newTeamName">
          <el-input v-model="postForm.newTeamName" placeholder="新群名" />
        </el-form-item>
      </el-form>
      <div class="m-footer">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="onSubmit('postForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { changeIMTeamName } from '@/api/kf';
const defaultValue = {
  newTeamName: ''
};
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    teamId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      formLabelWidth: '100px',
      postForm: Object.assign({}, defaultValue),
      rules: {
        price: [{ required: true, message: '请输入新群名', trigger: 'blur' }]
      }
    };
  },
  mounted() {
    const { store } = window.__xkit_store__;
    const session = store.uiStore.selectedSession;
    const teamId = session.replace('team-', '');
    const team = store.teamStore.teams.get(teamId);
    this.postForm.newTeamName = team.name;
  },
  methods: {
    onCancel() {
      this.postForm = Object.assign({}, defaultValue);
      this.$emit('close');
    },
    onSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let data = {
            teamId: this.teamId
          };
          data.newTeamName = this.postForm.newTeamName;
          changeIMTeamName(data, {}).then(res => {
            if (res.code == 200) {
              this.$message.success(res.message);
              this.onCancel();
            }
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
