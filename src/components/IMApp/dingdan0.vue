<template>
  <el-dialog
    :close-on-click-modal="false"
    width="30%"
    :before-close="onCancel"
    :visible.sync="dialogVisible"
    append-to-body
    top="1vh"
  >
    <el-form
      ref="postForm2"
      :model="postForm2"
      :rules="rules"
      label-width="100px"
      :disabled="isNeedVerifyCode && step!==1"
    >
      <!-- <el-form-item label="订单类型">
        <el-select style="width:100%" v-model="postForm2.orderType">
          <el-option
            v-for="item in createDanList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="支付类型">
        <el-select style="width:100%" v-model="postForm2.orderStatus">
          <el-option
            v-for="item in payTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="游戏">
        <el-select
          :filterable="true"
          style="width:100%"
          v-model="postForm2.cateId"
          @change="changeCateId"
        >
          <el-option
            v-for="item in gameList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="postForm2.orderType == 8" label="商品编号">
        <el-input v-model="postForm2.productSn"></el-input>
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input
          @input="onPriceChange"
          placeholder="请输入金额"
          v-model="postForm2.price"
        ></el-input>
      </el-form-item>
      <el-form-item v-if="orderBaopeiList0.length" label="基础包赔">
        <el-select style="width:100%" v-model="baopei0" :clearable="true">
          <el-option
            v-for="item in orderBaopeiList0"
            :key="item.id"
            :label="item.value"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="orderBaopeiList1.length" label="增值包赔">
        <el-checkbox-group v-model="baopeiCheckList">
          <el-checkbox
            :key="item.id"
            v-for="item in orderBaopeiList1"
            :label="item.id"
            >{{ item.value }}</el-checkbox
          >
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="买家账号">
        <el-input v-model="postForm2.buyerUsername"></el-input>
      </el-form-item>

      <el-form-item label="卖家">
        <el-input
          class="input-with-select"
          style="margin-bottom: 10px;"
          v-model="postForm2.usernameOrSn"
          placeholder="请输入"
        >
          <el-select
            v-model="postForm2.usernameOrSnType"
            slot="prepend"
            placeholder="请选择"
            style="width:105px"
            @change="usernameOrSnTypeChange"
          >
            <el-option label="商品编号" value="1"></el-option>
            <el-option label="账号" value="2"></el-option>
          </el-select>
        </el-input>
      </el-form-item>
      <!-- <el-form-item v-if="postForm2.orderType != 8" label="卖家账号">
        <el-input v-model="postForm2.sellerUsername"></el-input>
      </el-form-item> -->
      <el-form-item
        v-if="postForm2.orderType == 8"
        prop="orderStatus"
        :rules="[
          { required: true, message: '请选择是否已支付', trigger: 'blur' }
        ]"
        label="是否已支付"
      >
        <el-radio-group v-model="postForm2.orderStatus">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="baopei0price > 0" label="基础包赔金额">
        <el-input disabled v-model="baopei0price"></el-input>
      </el-form-item>
      <el-form-item v-if="baopei1price > 0" label="增值包赔金额">
        <el-input disabled v-model="baopei1price"></el-input>
      </el-form-item>
      <el-form-item label="总金额">
        <el-input disabled v-model="allprice"></el-input>
      </el-form-item>
      <el-form-item label="卖家到手价" prop="sellerHandAmount">
        <el-input
          placeholder="请输入金额"
          v-model="postForm2.sellerHandAmount"
        ></el-input>
      </el-form-item>
      <el-form-item label="支付金额" prop="payAmount">
        <el-input
          placeholder="请输入金额"
          v-model="postForm2.payAmount"
        ></el-input>
      </el-form-item>
      <el-form-item label="优惠金额">
        <el-input
          placeholder="优惠金额"
          disabled
          v-model="postForm2.promotionAmount"
        ></el-input>
        <!-- <div class="spaceStart">
          <div class="price">优惠金额:-{{ postForm2.promotionAmount }}</div>
        </div> -->
      </el-form-item>
    </el-form>

    <el-form
      ref="postForm3"
      label-width="100px"
      :model="postForm2"
      v-if="isNeedVerifyCode && step!==1"
    >
     <el-form-item
        label="动态码"
        prop="password"
        :rules="[
          {
            required: true,
            message: '请输入动态码',
            trigger: 'blur'
          }
        ]"
      >
        <el-input
          v-model="postForm2.password"
          placeholder="请输入动态码"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <el-button @click="onCancel">取 消</el-button>
    <el-button type="primary" @click="doCreateHuishou">{{isNeedVerifyCode && step==1?'获取动态码':'确 定'}}</el-button>
  </el-dialog>
</template>
<script>
import util from '@/utils/index.js';
import { fetchList } from '@/api/productCate';
import { generateKKOrderByKF2, getProductCategory,generateOrderPassword } from '@/api/kf.js';
const defaultForm = {
  orderStatus: '',
  productSn: '',
  promotionAmount: 0,
  sellerHandAmount: '',
  payAmount: '',
  orderType: '7',
  cateId: '',
  sellerUsername: '',
  buyerUsername: '',
  price: '',
  teamId: '',
  buyType: 0,
  baopeiTypes: undefined,
  usernameOrSn: '',
  usernameOrSnType: ''
};
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      step:1,

      payTypeList: [
        {
          label: '已支付',
          value: 1
        },
        {
          label: '未支付',
          value: 0
        }
      ],
      rules: {
        price: [{ required: true, message: '请输入价格', trigger: 'blue' }],
        sellerHandAmount: [
          { required: true, message: '请输入金额', trigger: 'blue' }
        ],
        payAmount: [{ required: true, message: '请输入金额', trigger: 'blue' }]
      },
      gameList: [],
      baopeiCheckList: [],
      createDanList: [
        { id: '6', name: '回收订单' },
        { id: '7', name: '担保订单' }
      ],
      allprice: 0,
      baopei0: '',
      baopei1price: 0,
      baopei0price: 0,
      orderBaopeiList0: [],
      orderBaopeiList1: [],
      postForm2: Object.assign({}, defaultForm)
    };
  },
  computed:{
    isNeedVerifyCode(){
      return this.postForm2.orderStatus == 1
    }
  },
  watch: {
    'postForm2.payAmount'(nVal, oVal) {
      this.postForm2.promotionAmount = util.minus(
        this.allprice,
        this.postForm2.payAmount
      );
      if (this.postForm2.promotionAmount <= 0) {
        this.postForm2.promotionAmount = 0;
      }
      this.postForm2.promotionAmount = `-${this.postForm2.promotionAmount}`;
    },
    baopei0(nVal, oVal) {
      this.computePirce();
    },
    baopeiCheckList(nVal, oVal) {
      this.computePirce();
    }
  },
  created() {
    Promise.all([
      fetchList(74, {
        pageNum: 1,
        pageSize: 999
      })
    ]).then(values => {
      this.gameList = [];
      values.forEach(item => {
        this.gameList = this.gameList.concat(item.data.list);
      });
    });
  },
  mounted() {
    const buyerUsername = this.$store.getters.bidMember.username;
    const sellerUsername = this.$store.getters.sidMember.username;
    this.postForm2 = Object.assign({}, defaultForm, {
      buyerUsername,
      sellerUsername
    });
  },
  methods: {
    usernameOrSnTypeChange(value) {
      if (value == 2) {
        this.postForm2.usernameOrSn = this.$store.getters.sidMember.username;
      }
    },
    doCreateHuishou() {
      const formName = this.isNeedVerifyCode && this.step===2?'postForm3':'postForm2'

      if (this.postForm2.usernameOrSnType === '') {
        this.$message.error('请选择商品编号或买家账号');
        return;
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          const { store } = window.__xkit_store__;
          const selectedSession = store.uiStore.selectedSession;
          const tempList = selectedSession.split('-');
          tempList.shift();
          const to = tempList.join('-');
          this.postForm2.teamId = to;
          let baopeiCheckListCopy = this.baopeiCheckList.map(ele => ele);

          if (baopeiCheckListCopy.length || this.baopei0) {
            this.postForm2.buyType = 1;
            if (this.baopei0) {
              baopeiCheckListCopy.push(this.baopei0);
            }
            this.postForm2.baopeiTypes = baopeiCheckListCopy;
          } else {
            this.postForm2.buyType = 0;
          }
          this.postForm2.sourceType = 4;
          const data = Object.assign({}, this.postForm2);

          if (data.usernameOrSnType == 1) {
            data.productSn = data.usernameOrSn;
            delete data.sellerUsername;
          } else {
            data.sellerUsername = data.usernameOrSn;
            delete data.productSn;
          }
          delete data.usernameOrSnType;
          delete data.usernameOrSn;
          if (data.orderStatus == 0) {
            delete data.password;
          }
          data.promotionAmount = util.times(data.promotionAmount, -1);

          // 先获取动态码
          if(this.isNeedVerifyCode && this.step===1){
            generateOrderPassword(data).then(res => {
              if (res.code == 200) {
                this.$message.success(res.message+',请输入动态码');
                this.step = this.step+1
              }
            });
          }else{ 
            generateKKOrderByKF2(data).then(res => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.onCancel();
              }
            });
          }
        }
      });
    },
    onCancel() {
      this.$emit('hide');
    },
    onPriceChange() {
      this.computePirce();
    },
    changeCateId(id) {
      getProductCategory(id).then(res => {
        if (res.code == 200) {
          const { data = {} } = res;
          let { custom = '{}' } = data;
          custom = JSON.parse(custom);
          const { baopei = [] } = custom;
          this.orderBaopeiList0 = [];
          this.orderBaopeiList1 = [];
          this.baopei0 = '';
          baopei.forEach(ele => {
            if (ele.type == 'BASIC_COMPENSATION') {
              this.orderBaopeiList0.push(ele);
            }
            if (ele.type == 'VALUE_ADD_COMPENSATION') {
              this.orderBaopeiList1.push(ele);
            }
          });
        }
      });
    },
    computePirce() {
      if (this.baopei0) {
        const baopei0rate = this.orderBaopeiList0.find(
          ele => ele.id == this.baopei0
        );
        this.baopei0price = util.times(
          this.postForm2.price || 0,
          baopei0rate.price
        );
      } else {
        this.baopei0price = 0;
      }
      if (this.baopeiCheckList.length) {
        this.baopei1price = 0;
        this.baopeiCheckList.forEach(ele => {
          const findIt = this.orderBaopeiList1.find(item => item.id == ele);
          this.baopei1price = util.add(
            this.baopei1price,
            util.times(this.postForm2.price || 0, findIt.price)
          );
        });
      } else {
        this.baopei1price = 0;
      }
      this.allprice = util.add(this.postForm2.price || 0, this.baopei0price);
      this.allprice = util.add(this.allprice, this.baopei1price);
      this.postForm2.sellerHandAmount = this.allprice;
      this.postForm2.payAmount = this.allprice;
      this.postForm2.promotionAmount = util.minus(
        this.allprice,
        this.postForm2.payAmount
      );
      if (this.postForm2.promotionAmount <= 0) {
        this.postForm2.promotionAmount = 0;
      }
      this.postForm2.promotionAmount = `-${this.postForm2.promotionAmount}`;
    }
  }
};
</script>
<style scoped>
.price {
  margin-right: 10px;
}
</style>
