<template>
    <div class="baopei_box el-reset-clazz">
      <el-dialog
        :visible.sync="dialogVisible"
        :before-close="onCancel"
        :close-on-click-modal="false"
        :modal="false"
        width="800px"
        top
        title="包赔认证"
      >
        <div v-if="postForm.baopeiStatus == 0" class="realPerson_tip">
          温馨提示：资料审核中
        </div>
        <div v-if="postForm.baopeiStatus == 1" class="realPerson_tip">
          温馨提示：资料审核不通过；<span v-if="postForm.region">{{
            postForm.region
          }}</span>
        </div>
        <div
          v-if="postForm.baopeiStatus == 2"
          class="realPerson_tip realPerson_tip2"
        >
          温馨提示：资料审核已通过
        </div>
        <div>
          <el-form
            v-loading.fullscreen.lock="loading"
            ref="postForm"
            :model="postForm"
            :rules="rules"
            label-width="130px"
            auto-complete="on"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
          >
            <div class="userC_form">
              <!-- <el-form-item prop="username" label="本人电话:">
                <el-input v-model="userInfo.phone" type="text" disabled />
              </el-form-item>
              <el-form-item prop="authCode" label="手机验证码:">
                <el-input
                  v-model="postForm.authCode"
                  type="tel"
                  placeholder="请输入手机验证码"
                  style="width: 70%"
                />
                <div class="code_wrap posionRight" @click="sendCode">
                  {{ codeMsg }}
                </div>
              </el-form-item> -->
              <el-form-item prop="name" label="紧急联系人:">
                <el-input
                  v-model="postForm.name"
                  type="text"
                  placeholder="请输入紧急联系人"
                />
              </el-form-item>
              <el-form-item prop="phoneNumber" label="紧急联系人电话:">
                <el-input
                  v-model="postForm.phoneNumber"
                  type="text"
                  placeholder="请输入紧急联系人电话"
                />
              </el-form-item>
              <el-form-item>
                <div class="rctip_box">
                  <span style="color: rgb(84, 141, 212)">
                    1.请您如实填写以便审核快速、顺利完成。<br />
                    2.不可填写您本人的号码作为紧急联系人电话。<br />
                    3.紧急联系人信息必须真实有效，该紧急联系电话号码的实名需要和紧急联系人姓名一致。<br />
                    4.此处信息仅用于账号交易审核，看看账号网将严格保护您的隐私信息。
                  </span>
                </div>
              </el-form-item>
              <el-form-item label="网购或外卖订单:">
                <el-popover placement="right" width="400" trigger="hover">
                  <img
                    style="width: 380px; margin: 0 auto"
                    src="https://images2.kkzhw.com/mall/images/20240618/waimai.webp"
                  />
                  <el-button
                    slot="reference"
                    style="
                      color: #fe5a1e;
                      padding: 0px 0 10px;
                      border: none;
                      font-size: 16px;
                      font-weight: 600;
                    "
                  >
                    <i class="el-icon-question" style="font-size: 18px"></i
                    >&nbsp;查看实例
                  </el-button>
                </el-popover>
                <uploadSingle
                  :url-pic="postForm.userOrderPic"
                  :need-water="false"
                  class="userOrderPic"
                  name-key="userOrderPic"
                  @upSuccsessSingle="picUpLoadSuc"
                />
              </el-form-item>
              <el-form-item>
                <div class="rctip_box" style="margin-top: -40px">
                  <span style="color: rgb(84, 141, 212)">
                    1.至少提供一张3个月内已交易成功的订单截图，推荐提交外卖订单。<br />
                  </span>
                  <span
                    >2.截图须完整包含姓名、电话、地址、时间。<br />3.订单地址不能是网吧或酒店等公共地址，不能打码。<br
                  /></span>
                  <span style="color: #ff0000">
                    系统提示：为了您能顺利通过审核，请按照上述要求进行提交</span
                  >
                </div>
              </el-form-item>
  
              <!-- <el-form-item prop="" label="备注:">
                <el-input
                  v-model="postForm.detailAddress"
                  type="textarea"
                  placeholder="请输入备注信息"
                />
              </el-form-item> -->
            </div>
          </el-form>
        </div>
        <div class="spaceEnd">
          <div class="elbtn cancel" @click="onCancel">取消</div>
          <div
            v-if="postForm.baopeiStatus != 2"
            class="elbtn"
            @click="submitForm('postForm')"
          >
            提交认证
          </div>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import moment from 'moment';
  import '@/utils/yidun-captcha';
  import { mapState } from 'vuex';
  import uploadSingle from '@/components/uploadSingle/index';
  import { getCertDetail, certAdd } from '@/api/bmc';
//   import isLogin from '@/utils/isLogin';
  
  export default {
    components: {
      uploadSingle,
    },
    props: {
      processFlowResult: {
        type: Object,
        default() {
          return {};
        },
      },
      actionObj: {
        type: Object,
        default() {
          return {};
        },
      },
      actionData: {
        type: Object,
        default() {
          return {};
        },
      },
      dialogVisible: {
        type: Boolean,
        default: false,
      },
      url: {
        type: String,
        default: '',
      },
    },
    data() {
      const validateName = (rule, value, callback) => {
        if (/[^\u4e00-\u9fa5]+/.test(value)) {
          callback(new Error('请输入中文'));
        } else {
          callback();
        }
      };
      return {
        postForm: {
          source: 'imteam',
          name: '',
          phoneNumber: '',
          userOrderPic: '',
          detailAddress: '',
        },
        rules: {
          // code: [
          //   { required: true, message: '请输入手机验证码', trigger: 'blur' },
          // ],
          name: [
            { required: true, message: '请输入紧急联系人', trigger: 'blur' },
            { validator: validateName, trigger: 'blur' },
          ],
          phoneNumber: [
            { required: true, message: '请输入紧急联系人电话', trigger: 'blur' },
          ],
        },
        loading: false,
        codeMsg: '获取验证码',
        code: 60,
        isDis: false,
      };
    },
    computed: {
      ...mapState({
        userInfo: (state) => state.userInfo,
      }),
    },
    mounted() {
        this.initUser();
    //   if (isLogin()) {
    //     this.initUser();
    //     //   this.initCaptcha();
    //   } else {
    //     this.$router.push({
    //       path: '/login',
    //       query: {
    //         redirect: location.href,
    //       },
    //     });
    //   }
    },
    methods: {
      onCancel() {
        this.postForm = {
          source: 'imteam',
          name: '',
          phoneNumber: '',
          userOrderPic: '',
          detailAddress: '',
        };
        this.$emit('close');
      },
      // initCaptcha() {
      //   window.initNECaptchaWithFallback(
      //     {
      //       captchaId: '3455bd8a6484410ea146980a113839aa',
      //       width: '320px',
      //       mode: 'popup',
      //       apiVersion: 2,
      //       onVerify: (err, data) => {
      //         if (err) return;
      //         this.doSendSmsCode(data);
      //       },
      //     },
      //     (instance) => {
      //       this.captchaIns = instance;
      //     },
      //   );
      // },
      picUpLoadSuc(url, key) {
        this.postForm[key] = url;
      },
      initUser() {
        this.$store.dispatch('getUserInfoStore');
        getCertDetail().then((res) => {
          if (res.code == 200) {
            if (res.data.faceStatus != 2) {
              this.$message.error('请先完成人脸认证！');
            }
            // 保证userOrderPic响应式
            this.postForm = Object.assign(this.postForm, res.data || {});
            if (res.data.baopeiUpdateTime) {
              // 使用 moment.js 解析上次包赔时间
              const baopeiUpdateTime = moment(res.data.baopeiUpdateTime);
  
              // 获取当前时间
              const currentTime = moment();
  
              // 计算两个日期之间的差异（天数）
              const diffDays = currentTime.diff(baopeiUpdateTime, 'days');
  
              // 判断是否超过60天
              if (diffDays > 60) {
                this.postForm.baopeiStatus = null;
              }
            }
          }
        });
      },
      // 保存提交身份证
      submitForm(formName) {
        // if (this.postForm.phoneNumber == this.userInfo.phone) {
        //   this.$message.error('不可填写您本人的号码作为紧急联系人电话');
        //   return;
        // }
        this.$refs[formName].validate((valid) => {
          if (!this.postForm.userOrderPic) {
            this.$message.error('请上传图片');
            return;
          }
          if (valid) {
            this.submitAdd();
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      submitAdd() {
        if (this.loading) {
          return;
        }
        this.loading = true;
        const flowImteamId = this.processFlowResult.id;
        const data = {
          flowImteamId,
          source: this.postForm.source,
          name: this.postForm.name,
          phoneNumber: this.postForm.phoneNumber,
          type: 3,
          userOrderPic: this.postForm.userOrderPic,
        };
        certAdd(data)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success('认证已提交请耐心等待审核!');
              this.initUser();
              this.onCancel();
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      // doSendSmsCode(data) {
      //   this.loading = true;
      //   sendPhoneCode({
      //     telephone: this.userInfo.phone,
      //     validate: data.validate,
      //   })
      //     .then((res) => {
      //       if (res.code === 200) {
      //         this.$message.success('验证码发送成功！');
      //         this.countDown();
      //       }
      //       this.loading = false;
      //     })
      //     .finally(() => {
      //       this.captchaIns.refresh();
      //     });
      // },
      /**
       * 获取验证码
       */
      sendCode() {
        if (this.isDis == true) {
          this.$message.error('请稍后重试');
          return;
        }
        this.captchaIns && this.captchaIns.verify();
      },
      /**
       * 倒计时
       */
      countDown() {
        this.code -= 1;
        if (this.code == 0) {
          this.code = 60;
          this.codeMsg = '获取验证码';
          this.isDis = false;
          clearInterval(interval);
          return;
        }
        this.codeMsg = '重新获取' + this.code + 'S';
        this.isDis = true;
        var _this = this;
        var interval = setTimeout(function () {
          _this.countDown();
        }, 1000);
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .error {
    color: #f56c6c;
    font-size: 12px;
  }
  .elbtn {
    display: inline-block;
    text-align: center;
    padding: 12px 20px;
    line-height: 14px;
    cursor: pointer;
    margin-left: 10px;
    color: #409eff;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
  }
  .elbtn:hover {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
  }
  .cancel {
    color: #606266;
    background: #fff;
    border: 1px solid #dcdfe6;
  }
  .cancel:hover {
    background: #fff;
    border-color: #409eff;
    color: #409eff;
  }
  .baopei_box {
    position: absolute;
    top: -88px;
    width: 802px;
    height: 814px;
    border: 1px solid #ccc;
    /deep/ .el-dialog__body {
      padding-top: 0;
    }
  }
  /deep/ .el-dialog__wrapper {
    position: absolute;
  }
  /deep/ .userOrderPic {
    .picUpload_wrap {
      border: 1px dashed #cccc;
    }
  }
  .realPerson_tip {
    top: 10px;
    z-index: 3000;
    color: #e60f0f;
    font-size: 14px;
    font-weight: 400;
    height: 40px;
    line-height: 40px;
    text-align: center;
    display: flex;
    justify-self: center;
    margin: 0 auto;
    justify-content: center;
    margin-top: -48px;
  }
  .realPerson_tip2 {
    color: rgb(103, 194, 58);
  }
  .code_wrap {
    float: right;
    height: 40px;
    width: 120px;
    background: #fff;
    color: #ff6716;
    border: none;
    cursor: pointer;
    text-align: center;
    line-height: 40px;
    border-radius: 20px;
    font-weight: 500;
  }
  .rctip_box {
    background-color: #eff7ff;
    border: 1px solid #0082ff;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;
    line-height: 26px;
  }
  .tip_boxBei {
    color: #999;
    font-size: 12px;
    padding-bottom: 15px;
  }
  .userC_form {
    width: 80%;
    margin: 0 auto;
  }
  .userC_identify {
    width: 300px;
    height: 160px;
    border-radius: 50%;
    position: relative;
  }
  .picUpload_btn {
    position: absolute;
    z-index: 4;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    opacity: 0;
    cursor: pointer;
  }
  .userC_phone {
    font-size: 16px;
    color: #222222;
    font-weight: 400;
  }
  .userC_subBtn {
    padding: 12px 60px;
    border-radius: 20px;
    font-size: 16px;
    color: #ffffff;
    background: #ff7000;
    margin: 40px auto;
    cursor: pointer;
  }
  .userC_subBtn.disabled {
    background: #f5f5f5;
    color: #222222;
  }
  .cutDt_pushWrap_i {
    height: 40px;
    margin-bottom: 20px;
  }
  .cutDt_pushWrap_left {
    width: 80px;
  }
  .cutDt_push_ipt_i {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    border: none;
    text-indent: 14px;
    font-weight: 500;
    background: transparent;
    width: 70%;
    outline: none;
    border: 1px solid #ff6716;
    border-radius: 4px;
  }
  .cutDt_push_ipt:focus {
    border: none;
  }
  .cutDt_push_btn {
    width: 80px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #ff6716;
    color: #fff;
    cursor: pointer;
  }
  .plDt_btn_i {
    margin-left: 20px;
    width: 148px;
    background: linear-gradient(90deg, #ff9600, #ff6700);
    border-radius: 21px;
    padding: 11px 0;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    margin-top: 20px;
  }
  .plDt_btn i {
    margin-right: 4px;
  }
  </style>
  