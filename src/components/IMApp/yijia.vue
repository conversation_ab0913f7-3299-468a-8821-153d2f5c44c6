<template>
  <el-dialog
    title="发送议价"
    :close-on-click-modal="false"
    width="30%"
    :before-close="onCancel"
    :visible.sync="dialogVisible"
    append-to-body
    top="20vh"
  >
    <el-form
      ref="postForm"
      :model="postForm"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="商品编号" prop="productSn">
        <el-input v-model="postForm.productSn"></el-input>
      </el-form-item>
    </el-form>
    <div class="spaceEnd">
      <el-button @click="onCancel">取 消</el-button>
      <el-button type="primary" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
const defaultForm = {
  productSn: ''
};
import { sendYijiaCard } from '@/api/kf';
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        productSn: [
          { required: true, message: '请输入商品编号', trigger: 'blur' }
        ]
      },
      postForm: Object.assign({}, defaultForm)
    };
  },
  methods: {
    onCancel() {
      this.$emit('hide');
    },
    onSubmit() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          const { store } = window.__xkit_store__;
          const selectedSession = store.uiStore.selectedSession;
          const tempList = selectedSession.split('-');
          tempList.shift();
          const buerIM = tempList.join('-');
          let data = {
            buerIM
          };
          data = Object.assign(data, this.postForm);
          sendYijiaCard(data).then(res => {
            if (res.code == 200) {
              this.$message.success(res.message);
              this.onCancel();
            }
          });
        }
      });
    }
  }
};
</script>

<style></style>
