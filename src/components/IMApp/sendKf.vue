<template>
  <el-dialog
    :close-on-click-modal="false"
    width="40%"
    :before-close="onCancel"
    :visible.sync="dialogVisible"
    append-to-body
    class="kflist"
    top="5vh"
  >
    <kflist2 :toIM="toIM" @close="onCancel"></kflist2>
  </el-dialog>
</template>

<script>
import kflist2 from './kflist2.vue';
export default {
  components: {
    kflist2
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      toIM: ''
    };
  },
  mounted() {
    const { store } = window.__xkit_store__;
    const selectedSession = store.uiStore.selectedSession;
    const tempList = selectedSession.split('-');
    tempList.shift();
    const to = tempList.join('-');
    this.toIM = to;
  },
  methods: {
    onCancel() {
      this.$emit('hide');
    }
  }
};
</script>

<style scoped>
.imDiv {
  cursor: pointer;
  color: #3995fb;
}
.kflist {
  .el-dialog__body {
    height: 500px;
    overflow: auto;
  }
}
</style>
