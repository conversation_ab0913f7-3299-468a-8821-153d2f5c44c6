<template>
  <div class="kferTransfer el-reset-clazz">
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :close-on-click-modal="false"
      width="33%"
      :modal="false"
      center
      title="汇款申请"
    >
      <el-form
        ref="huikuanForm"
        :label-width="formLabelWidth"
        :model="huikuanForm"
        class="form-box"
        :rules="rules"
      >
        <el-form-item
          :label="huikuanForm.transType == 0 ? '银行卡号' : '支付宝账号'"
          prop="bankNo"
        >
          <el-input
            disabled
            :value="`***${huikuanForm.bankNo.slice(-4)}`"
            placeholder="请输入银行卡号"
          />
        </el-form-item>
        <el-form-item label="收款人" prop="accountName">
          <el-input
            disabled
            v-model="huikuanForm.accountName"
            placeholder="请输入收款人"
          />
        </el-form-item>
        <el-form-item style="display: none;" label="汇款银行" prop="bankCode">
          <el-select
            :filterable="true"
            style="width:100%;"
            v-model="huikuanForm.bankCode"
          >
            <el-option
              v-for="(item, index) in bankList"
              :key="index"
              :value="`${item.node.bank_code}`"
              :label="item.node.bank_name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="汇款金额" prop="price">
          <el-input v-model="huikuanForm.price" placeholder="请输入汇款金额" />
        </el-form-item>
        <el-form-item label="申请备注" prop="applyNote">
          <el-input
            v-model="huikuanForm.applyNote"
            placeholder="请输入申请备注"
          />
        </el-form-item>
      </el-form>
      <div class="m-footer">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="onSubmit('huikuanForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryBankCode } from '@/api/product';
import { kkTransactionsCreateByOrder } from '@/api/product';
const defaultValue = {
  bankNo: '',
  accountName: '',
  // payeeType: 1,
  price: '',
  applyNote: ''
  // bankCode: ''
};
export default {
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {};
      }
    },
    actionObj: {
      type: Object,
      default() {
        return {};
      }
    },
    actionData: {
      type: Object,
      default() {
        return {};
      }
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formLabelWidth: '100px',
      huikuanForm: Object.assign({}, defaultValue),
      errorTxt: '',
      rules: {
        // bankNo: [
        //   { required: true, message: '请输入银行卡号', trigger: 'blur' },
        //   { pattern: /^\d+$/, message: '银行卡号只能输入数字', trigger: 'blur' }
        // ],
        // accountName: [
        //   { required: true, message: '请输入收款人', trigger: 'blur' }
        // ],
        // bankCode: [
        //   { required: true, message: '请输入汇款银行', trigger: 'blur' }
        // ],
        price: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        applyNote: [{ required: true, message: '请输入备注', trigger: 'blur' }]
      },
      bankList: []
    };
  },
  mounted() {
    const values = this.actionData;
    this.huikuanForm.accountName = values.transName;
    this.huikuanForm.bankNo = values.transCard;
    this.huikuanForm.transType = values.transType;
    this.init();
  },
  methods: {
    init() {
      queryBankCode().then(res => {
        const { data = '[]' } = res;
        this.bankList = JSON.parse(data);
      });
    },
    onCancel() {
      this.huikuanForm = Object.assign({}, defaultValue);
      this.$emit('close');
    },
    onSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let data = {};
          // let findIt = this.bankList.find(
          //   ele => ele.node.bank_code == this.huikuanForm.bankCode
          // );
          // const bankName = findIt.node.bank_name;
          data = Object.assign(data, this.huikuanForm);
          const { id, orderSn } = this.orderDetail;
          data.orderId = id;
          data.orderSn = orderSn;
          kkTransactionsCreateByOrder(data).then(res => {
            if (res.code == 200) {
              this.$message.success(res.message);
              this.onCancel();
            }
          });
        } else {
          return;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
