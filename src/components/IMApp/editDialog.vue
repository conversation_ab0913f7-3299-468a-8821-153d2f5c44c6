<template>
  <el-dialog
    :close-on-click-modal="false"
    width="20%"
    :visible.sync="dialogVisible"
    append-to-body
    top="4vh"
    title="备注"
    :before-close="onCancel"
  >
    <el-form>
      <el-form-item label="">
        <el-input
          v-model="teamPnote"
          type="textarea"
          placeholder="请输入备注"
          maxlength="60"
          :autosize="{ minRows: 4, maxRows: 6 }"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onSave">保存</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { personChatAddNote } from '@/api/kf';
import util from '@/utils/index';
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    imteamId: {
      type: String,
      default: ''
    },
    teamPnote: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      util
    };
  },
  created() {},
  methods: {
    onCancel() {
      this.$emit('hide');
    },
    onSave() {
      let data = {};
      data.note = this.teamPnote;
      data.imteamId = this.imteamId;
      personChatAddNote(data).then(res => {
        if (res.code == 200) {
          this.$message.success(res.message);
          this.$emit('hide');
          this.$emit('refreshList', res.data.list);
        }
      });
    }
  }
};
</script>

<style scoped>
.price {
  margin-right: 10px;
}
</style>
