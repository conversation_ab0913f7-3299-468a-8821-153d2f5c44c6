// indexedDBMixin.js
export default {
  data() {
    return {
      // 注意：这里我们不再在 mixin 的 data 中声明 db，因为每个组件实例应该有自己的数据库连接逻辑
      // 但实际上，你可能想要将数据库连接管理放在 Vuex store 或其他地方
      dbName: 'kfDatabase',
      version: 1,
      objectStoreName: 'kfObjectStore'
    };
  },
  methods: {
    async openDB() {
      if (!('indexedDB' in window)) {
        console.error('IndexedDB is not supported by this browser.');
        return;
      }

      return new Promise((resolve, reject) => {
        const request = window.indexedDB.open(this.dbName, this.version);

        request.onerror = event => {
          console.error('Database error:', event.target.errorCode);
          reject(event.target.errorCode);
        };

        request.onsuccess = event => {
          this.$db = event.target.result; // 这里我们将数据库连接保存到组件的实例属性上（尽管这通常不是最佳实践）
          resolve(this.$db);
        };

        request.onupgradeneeded = event => {
          const db = event.target.result;
          if (!db.objectStoreNames.contains(this.objectStoreName)) {
            db.createObjectStore(this.objectStoreName, {
              keyPath: 'key'
            });
          }
        };
      });
    },
    async getByKeyDb(key) {
      if (!this.$db) {
        console.error('Database is not open.');
        return;
      }

      return new Promise((resolve, reject) => {
        const transaction = this.$db.transaction(
          [this.objectStoreName],
          'readonly'
        );
        const objectStore = transaction.objectStore(this.objectStoreName);

        const request = objectStore.get(key);

        request.onsuccess = event => {
          if (request.result) {
            resolve(request.result);
          } else {
            resolve(null); // 或者你可以抛出一个错误，表示没有找到记录
          }
        };

        request.onerror = event => {
          console.error('Unable to retrieve data:', event.target.errorCode);
          reject(event.target.errorCode);
        };
      });
    },
    async updateByKeyDb(key, newValue) {
      if (!this.$db) {
        console.error('Database is not open.');
        return Promise.reject(new Error('Database is not open.'));
      }

      return new Promise((resolve, reject) => {
        const transaction = this.$db.transaction(
          [this.objectStoreName],
          'readwrite'
        );
        const objectStore = transaction.objectStore(this.objectStoreName);

        // 第一步：尝试根据键检索记录
        const getRequest = objectStore.get(key);

        getRequest.onsuccess = event => {
          const record = event.target.result;
          if (record) {
            // 如果记录存在，则进行更新
            const updatedRecord = Object.assign({}, record, newValue); // 或者使用其他合并逻辑

            // 第二步：使用 put 方法更新记录
            const putRequest = objectStore.put(updatedRecord);

            putRequest.onsuccess = () => {
              resolve(updatedRecord); // 解析更新后的记录
            };

            putRequest.onerror = errorEvent => {
              console.error(
                'Unable to update data:',
                errorEvent.target.errorCode
              );
              reject(
                new Error(
                  `Unable to update data: ${errorEvent.target.errorCode}`
                )
              );
            };
          } else {
            // 如果记录不存在，则进行添加
            // 注意：newValue 应该是一个完整的对象，包含所有需要的字段
            const addRequest = objectStore.add(newValue);

            addRequest.onsuccess = () => {
              resolve(newValue); // 解析添加后的记录（或任何你需要的值）
            };

            addRequest.onerror = errorEvent => {
              console.error('Unable to add data:', errorEvent.target.errorCode);
              reject(
                new Error(`Unable to add data: ${errorEvent.target.errorCode}`)
              );
            };
          }
        };

        getRequest.onerror = errorEvent => {
          console.error(
            'Unable to retrieve data for update or add:',
            errorEvent.target.errorCode
          );
          reject(
            new Error(
              `Unable to retrieve data for update or add: ${errorEvent.target.errorCode}`
            )
          );
        };
      });
    }
  }
};
