<template>
  <el-dialog
    :close-on-click-modal="false"
    width="30%"
    :before-close="onCancel"
    :visible.sync="dialogVisible"
    append-to-body
    top="5vh"
  >
    <el-form
      ref="postForm"
      :model="postForm"
      :rules="rules"
      label-width="200px"
    >
      <el-form-item label="IM号或商品编号或手机号" prop="keyword">
        <el-input v-model="postForm.keyword"></el-input>
      </el-form-item>
    </el-form>
    <div v-if="getUserInfoOk">
      <el-row :gutter="20">
        <el-col :span="10">
          <div class="spaceStart">
            <div>手机号:</div>
            <div>{{ userInfo.phone }}</div>
          </div>
        </el-col>
        <el-col :span="10"
          ><div class="spaceStart">
            <div>IM号:</div>
            <div class="imDiv" @click="goChat(userInfo)">
              {{ userInfo.imaccount }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="spaceEnd">
      <el-button @click="onCancel">取 消</el-button>
      <el-button type="primary" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
const defaultForm = {
  keyword: ''
};
import { getMemberInfo2 } from '@/api/kf';
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      getUserInfoOk: false,
      userInfo: {},
      postForm: Object.assign({}, defaultForm),
      rules: {
        keyword: [
          {
            required: true,
            message: '请输入IM号或商品编号或手机号',
            trigger: 'blur'
          }
        ]
      }
    };
  },
  methods: {
    goChat(userInfo) {
      const { imaccount } = userInfo;
      const { store } = window.__xkit_store__;
      const sessionId = `p2p-${imaccount}`;
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', imaccount);
      }
      this.onCancel();
    },
    onCancel() {
      this.$emit('hide');
    },
    onSubmit() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          getMemberInfo2(this.postForm).then(res => {
            if (res.code == 200) {
              this.getUserInfoOk = true;
              this.userInfo = res.data;
            }
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.imDiv {
  cursor: pointer;
  color: #3995fb;
}
</style>
