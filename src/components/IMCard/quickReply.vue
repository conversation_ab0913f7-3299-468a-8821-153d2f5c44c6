<template>
  <div style="padding: 20px">
    <div v-if="isXYShop == true">
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10px;
        "
      >
        <div>
          商品ID：<span style="color: #3995fb">{{
            productObj.outProductId || ''
          }}</span>

          <i
            :size="14"
            color="#FF720C"
            @click="copyVal(productObj.outProductId)"
            class="el-icon-copy-document"
          ></i>
        </div>
        <div>
          {{
            goodsStatusType[productObj.status]
          }}
        </div>
      </div>
      <div class="collect_item_pic">
        <el-image
          :src="
            productObj.pic
          "
          class="productPic"
        />
      </div>
      <div @click="goPage" style="cursor: pointer;" class="collect_item_right">
        <!-- <div class="spaceBetween">
              <div class="collect_tit">
                <div :title="item.orderItemList[0].productName" class="collect_tit_two">{{ item.orderItemList[0].productName }}</div>
                <div :title="item.productCategoryName" class="collect_tit_two">
                  {{ item.productCategoryName }}
                </div>
              </div>
            </div> -->
            <div class="collect_price spaceBetween">
          <div :title="productObj.subTitle" class="textOneLine">
            商品名称：<span class="contentText">{{
              productObj.subTitle
            }}</span>
          </div>
        </div>
        <div style="margin-top: 8px;" class="collect_price spaceBetween">
          <div>
            金额：<span class="list-price-new contentText"
              >¥ {{ productObj.price }}</span
            >
          </div>
          <!-- <a class="sendGoods" @click="sendToIm(item)">发送商品</a> -->
        </div>
        <div  style="margin-top: 8px;" class="collect_price spaceBetween">
          <div>
            商品：<span class="contentText">{{
              productObj.description
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isXYShop == false">
      <div v-for="(item, index) in recordList" :key="index">
        <div class="assureitem">
          <div class="spaceBetween">
            <div>{{ util.getAppellation(item.consultFrom) }}</div>
            <div>{{ util.timeFormat(item.createTime) }}</div>
          </div>
          <div class="spaceBetween">
            <pre class="precnt">{{ item.consultContent }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { loadByIM, memberConsultList, getIdlefishGoodsList } from '@/api/bmc';
import util from '@/utils/index';
export default {
  data() {
    return {
      util,
      recordList: [],
      isXYShop: false,
      productObj: {},
      pageUrl:'',
      goodsStatusType: {
        'NEW': '新建',
        'DRAFT': '草稿',
        'PENDING': '待发布',
        'ON_SALE': '销售中',
        'DOWN_SHELF': '已下架',
        // 'DELETED': '已删除',
      },
    };
  },
  methods: {
    initData(e) {
      this.isXYShop = e;
      const { store } = window.__xkit_store__;
      const im = store.uiStore.selectedSession.replace('p2p-', '');
      loadByIM({ im }).then((res) => {
        if (res.code == 200) {
          const username = res.data.username;
          memberConsultList({
            username,
          }).then((response) => {
            if (response.code == 200) {
              this.recordList = response.data;
              if (this.isXYShop) {
                let dataJson = JSON.parse(response.data[0].consultContent);
                this.pageUrl=dataJson.url
                let params = {
                  platform: 'XY',
                  pageNum: 1,
                  pageSize: 20,
                  outProductId: 969461889893701,
                  // dataJson.id
                };
                getIdlefishGoodsList(params).then((res) => {
                  if (res.code == 200) {
                    if (res.data.list.length > 0) {
                      this.productObj = res.data.list[0];
                    }
                  }
                });
              }
            }
          });
        }
      });
    },
    goPage(){
      window.open(this.pageUrl, '_blank');
    },
    // inputMsg(command) {
    //   const txt = this.dropList[command];
    //   console.log(this.dropList,command,8989)
    //   const { nim, store } = window.__xkit_store__;
    //   let sessionId = store.uiStore.selectedSession;
    //   console.log(sessionId.indexOf('team-'),2222222)
    //   let scene = 'team';
    //   if (sessionId.indexOf('team-') === 0) {
    //     sessionId = sessionId.replace('team-', '');
    //     sessionId = parseInt(sessionId);
    //   } else {
    //     scene = 'p2p';
    //     sessionId = sessionId.replace('p2p-', '');
    //   }
    //   console.log(txt,2222222)
    //   if (txt.indexOf('@买家老板') != -1) {
    //     const name = store.uiStore.getAppellation({
    //       account: this.bidim,
    //     });
    //     const txtMsg = txt.replace('@买家老板', '');
    //     let newTxt = `@${name} ${txtMsg}`;
    //     const selectedAtMembers = {
    //       account: this.bidim,
    //       appellation: name,
    //     };
    //     const ext = util.onAtMembersExtHandler(newTxt, [selectedAtMembers]);
    //     store.msgStore
    //       .sendTextMsgActive({
    //         scene,
    //         to: sessionId,
    //         body: newTxt,
    //         ext,
    //       })
    //       .then((res) => {
    //         document.getElementById(`${res.idClient}`).scrollIntoView();
    //       });
    //   } else {
    //     store.msgStore
    //       .sendTextMsgActive({
    //         scene,
    //         to: sessionId,
    //         body: txt,
    //       })
    //       .then((res) => {
    //         document.getElementById(`${res.idClient}`).scrollIntoView();
    //       });
    //   }
    // },
  },
};
</script>
<style lang="scss" scoped>
.oneline {
  max-width: 340px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  line-height: 2;
  cursor: pointer;
}
.oneline:hover {
  background: #eef5fe;
}
.assureitem {
  margin: 10px 0;
  font-size: 12px;
  border-bottom: 1px solid #ccc;
  .spaceBetween {
    margin: 5px;
  }
  .price_red {
    color: #fe5a1e;
  }
}
.precnt {
  word-break: break-all;
  white-space: pre-wrap;
}
.collect_item_pic {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 6px;
  float: left;
  .productPic {
    object-fit: cover;
    height: 100%;
    width: 100%;
  }
}
.collect_item_pic > image {
  width: 100%;
  height: 100%;
}
.collect_item_right {
  width: 100%;
  height: 80px;
  box-sizing: border-box;
  padding-left: 90px;
}
.list-price-new {
  color: #fe5a1e;
}
</style>
