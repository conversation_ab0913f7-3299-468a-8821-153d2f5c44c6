<template>
  <div class="recordCard">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick" stretch>
      <el-tab-pane
        v-for="(item, index) in tablist"
        :key="index"
        :label="item.label"
        :name="item.name"
      ></el-tab-pane>
    </el-tabs>
    <div v-if="activeName == 'goods'">
      <div
        style="
          padding-bottom: 10px;
          box-shadow: rgba(0, 0, 0, 0.05) 1px 2px 3px 0px;
        "
      >
        <el-input
          placeholder="商品编号/ID，回车确认"
          prefix-icon="el-icon-search"
          v-model="goodsParams.productSn"
          @keyup.enter.native="goodsSearch"
        >
        </el-input>
      </div>
      <!-- v-infinite-scroll="loadMoreScroll"
        infinite-scroll-delay="200"
        :infinite-scroll-disabled="goodsBusy" -->
      <div
        style="max-height: 660px; overflow: auto"
        v-infinite-scroll="loadMoreScroll"
        infinite-scroll-delay="200"
        :infinite-scroll-disabled="goodsBusy"
      >
        <el-empty v-if="footerList.length < 1" :image-size="200"></el-empty>
        <div
          class="collect_item"
          v-for="(item, index) in footerList"
          :key="index"
        >
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 10px;
            "
          >
            <div>
              {{ item.outProductId ? '商品ID' : '商品编号' }}：<span
                style="color: #3995fb"
                >{{
                  item.outProductId ? item.outProductId : item.productSn || ''
                }}</span
              >

              <i
                :size="14"
                color="#FF720C"
                @click="
                  copyVal(
                    item.outProductId ? item.outProductId : item.productSn
                  )
                "
                class="el-icon-copy-document"
              ></i>
            </div>
            <div></div>
          </div>
          <div class="collect_item_pic">
            <el-image :src="item.pic" class="productPic" />
          </div>
          <div class="collect_item_right">
            <div class="spaceBetween">
              <!-- @click="goProduct(item)" -->
              <div class="collect_tit">
                <div :title="item.subTitle" class="collect_tit_two">
                  {{ item.subTitle }}
                </div>
                <div :title="item.productCategoryName" class="collect_tit_two">
                  {{ item.productCategoryName }}
                </div>
              </div>
            </div>
            <div class="collect_price spaceBetween">
              <div class="list-price-new">¥ {{ item.price }}</div>
              <a class="sendGoods" @click="sendToIm(item)">发送商品</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="activeName == 'product'">
      <productCard ref="productCardChild" :isXYShop="isXYShop" />
    </div>
    <div v-if="activeName == 'quickReply'">
      <quickReply ref="quickReplyChild" />
    </div>
  </div>
</template>
<script>
import { getProductList, mySellerList, getIdlefishGoodsList } from '@/api/bmc';
import productCard from './productCard.vue';
import quickReply from './quickReply.vue';
export default {
  components: {
    productCard,
    quickReply,
    // orderCard,
    // SingleUpload
  },
  props: {
    orderImAccount: {
      type: [String, Number],
      default: () => '',
    },
  },
  data() {
    return {
      footerList: [],
      isXYShop: false,
      activeName: 'goods',
      loadMorePageNum: '',
      goodsBusy: true,
      goodsParams: {
        pageNum: 1,
        pageSize: 20,
        productSn: '',
        productStatus: 'ON_SHELF',
        // ON_SHELF
      },

      tablist: [
        {
          label: '用户',
          name: 'quickReply',
        },
        {
          label: '商品',
          name: 'goods',
        },
        {
          label: '订单',
          name: 'product',
        },
      ],
    };
  },
  mounted() {},
  methods: {
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    initData(e, isTeam) {
      if (isTeam) {
        this.tablist = [
          {
            label: '商品',
            name: 'goods',
          },
          {
            label: '订单',
            name: 'product',
          },
        ];
        if (this.activeName == 'quickReply') {
          this.activeName = 'goods';
        }
      } else {
        this.tablist = [
          {
            label: '用户',
            name: 'quickReply',
          },
          {
            label: '商品',
            name: 'goods',
          },
          {
            label: '订单',
            name: 'product',
          },
        ];
      }
      this.isXYShop = e.includes('kug');
      if (this.activeName == 'quickReply') {
        setTimeout(() => {
          this.$refs.quickReplyChild.initData();
        });
      }

      this.goodsParams = {
        pageNum: 1,
        pageSize: 20,
        productSn: '',
        productStatus: 'ON_SHELF',
        // ON_SHELF
      };
      this.getGoodsList();
      if (this.activeName == 'product') {
        setTimeout(() => {
          this.$refs.productCardChild.initData(this.isXYShop);
        });
        // this.orderParams.orderStatus=''
      }
    },
    getGoodsList(load) {
      let params = JSON.parse(JSON.stringify(this.goodsParams));
      params.pageNum = this.loadMorePageNum;
      if (this.isXYShop) {
        let params = {
          status: 'ON_SALE',
          platform: 'XY',
          pageNum: this.goodsParams.pageNum,
          pageSize: this.goodsParams.pageSize,
          outProductId: this.goodsParams.productSn,
        };
        getIdlefishGoodsList(params).then((res) => {
          if (res.data.list.length < 1 && load == 'load') {
            this.goodsParams.pageNum = this.loadMorePageNum;
            this.$message.warning('暂无更多');
            return;
          }
          if (!load) {
            this.footerList = [];
          }
          this.footerList = this.footerList.concat(res.data.list);
          this.goodsParams.pageNum = res.data.pageNum;
          this.goodsBusy = this.footerList.length > 19 ? false : true;
        });
      } else {
        getProductList(this.goodsParams).then((res) => {
          if (res.data.list.length < 1 && load == 'load') {
            this.goodsParams.pageNum = this.loadMorePageNum;
            this.$message.warning('暂无更多');
            return;
          }
          if (!load) {
            this.footerList = [];
          }
          this.footerList = this.footerList.concat(res.data.list);
          this.goodsParams.pageNum = res.data.pageNum;
          this.goodsBusy = this.footerList.length > 19 ? false : true;
        });
      }
    },
    loadMoreScroll() {
      this.loadMorePageNum = this.goodsParams.pageNum++;
      this.getGoodsList('load');
    },

    goodsSearch() {
      this.goodsParams.pageNum = 1;
      this.getGoodsList();
    },

    handleClick() {
      if (this.activeName == 'goods') {
        this.getGoodsList();
      }
      if (this.activeName == 'quickReply') {
        setTimeout(() => {
          this.$refs.quickReplyChild.initData(this.isXYShop);
        });
      }
      if (this.activeName == 'product') {
        setTimeout(() => {
          this.$refs.productCardChild.initData(this.isXYShop);
        });
        // this.orderParams.orderStatus=''
      }
    },

    sendToIm(item) {
      const { store, nim } = window.__xkit_store__;
      let sessionId = store.uiStore.selectedSession;
      const splitList = sessionId.split('-');
      const scene = splitList[0];
      splitList.shift();
      const to = splitList.join('-');
      const myAccount = store.userStore.myUserInfo.account;
      const productDetail = item;
      const { productSn, productCategoryId } = productDetail;
      console.log(item,22222)
      
      if(to.includes('kug')){
        store.msgStore
          .sendTextMsgActive({
            scene,
            to: to,
            body: `https://www.goofish.com/item?id=${item.outProductId}`,
          })
          .then((res) => {
            document.getElementById(`${res.idClient}`).scrollIntoView();
          });
        return
      }
     
      const content = `<div class="spaceBetween msg-flexstart">
      <img src="${productDetail.pic}" class="msg-productImg" />
      <div>
        <div class="twoLine">${productDetail.subTitle}</div>
        <div class="msg-red">￥${productDetail.price || ''}</div>
      </div>
    </div>`;
      const attach = {
        data: {
          type: 'product',
          productSn,
          productId: productDetail.id,
          productCategoryId,
        },
        body: {
          title: productDetail.subTitle,
          content,
        },
        type: 'kk_product_msg_fed',
      };
      store.msgStore
        .sendCustomMsgActive({
          scene: scene,
          from: myAccount,
          to: to,
          attach: JSON.stringify(attach),
        })
        .then((res) => {
          // 让消息滚动到可视区域
          document.getElementById(`${res.idClient}`).scrollIntoView();
        })
        .catch((err) => {
          console.log('发送失败', err);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.userFooter-top {
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
}
.member_box {
  flex-wrap: wrap;
  font-size: 12px;
}
.refresh {
  color: #3995fb;
  cursor: pointer;
}
.member {
  width: 48%;
  line-height: 30px;
  .red_txt {
    color: #fe5a1e;
  }
  .blue_txt {
    color: #3995fb;
    cursor: pointer;
  }
  .label {
    color: #666;
  }
}
.userFooter {
  /deep/ .el-tabs__item {
    padding: 0 5px 0 0;
  }
}
.teamitem {
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
.imspan {
  color: #409eff;
  cursor: pointer;
  display: inline-block;
}
.assureitem {
  margin: 10px 0;
  font-size: 12px;
  border-bottom: 1px solid #ccc;
  .spaceBetween {
    margin: 5px;
  }
  .price_red {
    color: #fe5a1e;
  }
}
.precnt {
  word-break: break-all;
  white-space: pre-wrap;
}
.userFooter {
  margin: 10px 0;
  text-align: center;
}
.collect_item {
  overflow: hidden;
  margin: 10px 0;
  border-bottom: 1px solid #ccc;
  padding: 15px;
  cursor: pointer;
}
.collect_item_pic {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 6px;
  float: left;
  .productPic {
    object-fit: cover;
    height: 100%;
    width: 100%;
  }
}
.collect_item_pic > image {
  width: 100%;
  height: 100%;
}
.collect_item_right {
  width: 100%;
  height: 80px;
  box-sizing: border-box;
  padding-left: 90px;
}
.collect_item_top {
  font-size: 12px;
  color: #000;
  font-weight: 600;
}
.collect_tit {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  padding-top: 4px;
}
.collect_tit_two {
  font-size: 12px;
  color: #666;
  padding-top: 2px;
  width: 150px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  word-break: break-all;
}
.collect_price {
  font-weight: 700;
  font-size: 14px;
}
.list-price-new {
  color: #fe5a1e;
}
.empty {
  text-align: center;
  line-height: 40px;
}
.back_box {
  margin: 10px 0;
}
.backbtn {
  margin-left: 10px;
}
/deep/ .el-select .el-input {
  width: 100px;
}
/deep/ .input-with-select .el-input-group__prepend {
  background-color: #fff;
}
/deep/ .back_box .el-input-group__prepend {
  background-color: #fff;
}
.recordCard {
  padding: 10px;
  .searchIpt {
    margin-right: 20px;
  }
  .search_box {
    margin-top: 10px;
  }
  .order_box {
    position: relative;
    .spaceBetween {
      justify-content: space-evenly;
    }
  }
  .record_box {
    font-size: 12px;
    flex-wrap: wrap;
    align-items: center;
    .record {
      width: 100%;
      border: 1px solid #ccc;
      padding: 10px;
      margin-top: 10px;
      .label {
        width: 120px;
      }
      .spaceBetween {
        align-items: flex-start;
        line-height: 30px;
      }
      .content {
        color: #666;
        max-width: 400px;
        // 强制换行
        word-break: break-all;
        word-wrap: break-word;
      }
    }
  }
}
.sendGoods {
  min-width: 10px;
  padding: 0 10px;
  border: 1px solid #9a9a9a;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}
.contentText {
  font-weight: 400;
}
</style>
