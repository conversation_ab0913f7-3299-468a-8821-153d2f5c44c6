<template>
  <div class="detailCard">
    <div class="card_title spaceBetween">
      <div>的好的好的</div>
      <div class="red_txt">场景FAQ</div>
    </div>
    <div class="card_content">
      都是更好的生活
    </div>
  </div>
</template>

<script>
export default {
  name: "detailCard",
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
.detailCard {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  .card_title {
  }
  .red_txt {
    color: #fe5a1e;
    cursor: pointer;
  }
  .card_content {
    margin-top: 15px;
  }
}
</style>
