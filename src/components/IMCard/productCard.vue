<template>
  <div
    style="
      padding-bottom: 10px;
      box-shadow: rgba(0, 0, 0, 0.05) 1px 2px 3px 0px;
    "
  >
    <el-input
      placeholder="订单，回车确认"
      prefix-icon="el-icon-search"
      v-model="orderParams.orderSn"
      @keyup.enter.native="productSnSearch"
    >
    </el-input>

    <el-tabs
      class="userFooter"
      v-model="orderParams.orderStatus"
      stretch
      @tab-click="changeActiveProductStatus"
    >
      <el-tab-pane
        v-for="(item, index) in (isXYShop ? productStatusXYType:productStatusType )"
        :label="item.label"
        :name="item.name"
      ></el-tab-pane>
      <!-- <el-tab-pane label="待付款" name="WAIT_PAY"></el-tab-pane>
    <el-tab-pane label="已预定" name="BOOKED"></el-tab-pane>
    <el-tab-pane label="已退款" name="REFUND"></el-tab-pane>
    <el-tab-pane label="已完成" name="COMPLETED"></el-tab-pane>
    <el-tab-pane label="已取消" name="CANCELED"></el-tab-pane> -->
    </el-tabs>
    <div
      style="max-height: 580px; overflow: auto"
      v-infinite-scroll="loadProductMore"
      infinite-scroll-delay="200"
      :infinite-scroll-disabled="productBusy"
    >
      <el-empty v-if="productList.length < 1" :image-size="200"></el-empty>
      <div
        class="collect_item"
        v-for="(item, index) in productList"
        :key="index"
      >
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
          "
        >
          <div>
            订单编号：<span style="color: #3995fb">{{
              item.orderSn?item.orderSn:item.outProductId || ''
            }}</span>

            <i
              :size="14"
              color="#FF720C"
              @click="copyVal(item.orderSn?item.orderSn:item.outProductId )"
              class="el-icon-copy-document"
            ></i>
          </div>
          <div>{{ item.orderStatusName?item.orderStatusName:goodsStatusType[item.status] }}</div>
        </div>
        <div class="collect_item_pic">
          <el-image
            :src="item.orderItemList?item.orderItemList[0].productPic:item.pic"
            class="productPic"
          />
        </div>
        <div class="collect_item_right">
          <!-- <div class="spaceBetween">
              <div class="collect_tit">
                <div :title="item.orderItemList[0].productName" class="collect_tit_two">{{ item.orderItemList[0].productName }}</div>
                <div :title="item.productCategoryName" class="collect_tit_two">
                  {{ item.productCategoryName }}
                </div>
              </div>
            </div> -->
          <div class="collect_price spaceBetween">
            <div>
              金额：<span class="list-price-new contentText"
                >¥ {{ item.totalAmount?item.totalAmount:item.price }}</span
              >
            </div>
            <!-- <a class="sendGoods" @click="sendToIm(item)">发送商品</a> -->
          </div>
          <div class="collect_price spaceBetween">
            <div>数量：<span class="contentText">1</span></div>
          </div>
          <div class="collect_price spaceBetween">
            <div>
              商品：<span class="contentText">{{ item.orderSn?item.orderSn:item.outProductId }}</span>
            </div>
          </div>
          <!-- <div class="collect_price spaceBetween">
              <div>
                状态：<span class="contentText">{{ item.orderStatusName}}</span>
              </div>
            </div> -->
        </div>
        <!-- <div style="display: flex; justify-content: right; margin-top: 10px">
          <a class="sendGoods" @click="sendToIm(item)">提醒付款</a>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
import { getProductList, mySellerList,getIdlefishGoodsList } from '@/api/bmc';
export default {
  components: {
    // orderCard,
    // SingleUpload
  },
  data() {
    return {
      isXYShop: false,
      productList: [],
      productBusy: true,
      loadProductMorePageNum:1,
      orderParams: {
        pageNum: 1,
        pageSize: 20,
        orderSn: '',
        orderStatus: '',
      },
      goodsStatusType: {
        'NEW': '新建',
        'DRAFT': '草稿',
        'PENDING': '待发布',
        'ON_SALE': '销售中',
        'DOWN_SHELF': '已下架',
        // 'DELETED': '已删除',
      },
      productStatusType: [
        {
          label: '全部',
          name: '0',
        },
        {
          label: '待付款',
          name: 'WAIT_PAY',
        },
        {
          label: '已预定',
          name: 'BOOKED',
        },
        {
          label: '已退款',
          name: 'REFUND',
        },
        {
          label: '已完成',
          name: 'COMPLETED',
        },
        {
          label: '已取消',
          name: 'CANCELED',
        },
      ],
      productStatusXYType: [
        {
          name: '0',
          label: '查看全部',
        },
        {
          name: 'NEW',
          label: '新建',
        },
        {
          name: 'DRAFT',
          label: '草稿',
        },
        {
          name: 'PENDING',
          label: '待发布',
        },
        {
          name: 'ON_SALE',
          label: '销售中',
        },
        {
          name: 'DOWN_SHELF',
          label: '已下架',
        },
        // {
        //   name: 'DELETED',
        //   label: '已删除',
        // },
      ],
    };
  },
  mounted() {
    // this.getProductListLoad();
  },
  methods: {
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    initData(e) {
      this.isXYShop = e;
      this.orderParams={
        pageNum: 1,
        pageSize: 20,
        orderSn: '',
        orderStatus: '0',
      }
      this.loadProductMorePageNum=1
      this.getProductListLoad();
    },
    loadProductMore() {

      this.loadProductMorePageNum = this.orderParams.pageNum++;
      this.getProductListLoad('load');
    },
    changeActiveProductStatus() {
      
      this.orderParams.pageNum = 1;
      this.getProductListLoad();
    },
    productSnSearch() {
      this.orderParams.pageNum = 1;
      this.getProductListLoad();
    },
    getProductListLoad(load) {
      let params = JSON.parse(JSON.stringify(this.orderParams));
      params.pageNum = this.loadProductMorePageNum;
      if (this.isXYShop) {
        
        let searchParams={
          status:params.orderStatus&&params.orderStatus != '0' ? params.orderStatus : null,
          platform:'XY',
          pageNum:params.pageNum,
          pageSize:params.pageSize,
          outProductId:params.orderSn
        }
        getIdlefishGoodsList(searchParams).then((res) => {
          if (res.data.list && res.data.list.length < 1 && load == 'load') {
            this.orderParams.pageNum = this.loadMorePageNum;
            this.$message.warning('暂无更多');
            return;
          }
          if (!load) {
            this.productList = [];
          }
          if (!res.data.list) {
            this.productList = [];
            return;
          }
          console.log(params,222222)
          this.productList = this.productList.concat(res.data.list);
          this.orderParams.pageNum = res.data.pageNum;
          this.productBusy = this.productList.length > 19 ? false : true;
        });
      } else {
        params.orderStatus =
          params.orderStatus != '0' ? params.orderStatus : '';
        mySellerList(params).then((res) => {
          if (res.data.list && res.data.list.length < 1 && load == 'load') {
            this.orderParams.pageNum = this.loadMorePageNum;
            this.$message.warning('暂无更多');
            return;
          }
          if (!load) {
            this.productList = [];
          }
          if (!res.data.list) {
            this.productList = [];
            return;
          }
          this.productList = this.productList.concat(res.data.list);
          this.orderParams.pageNum = res.data.pageNum;
          this.productBusy = this.productList.length > 19 ? false : true;
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.userFooter-top {
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
}
.member_box {
  flex-wrap: wrap;
  font-size: 12px;
}
.refresh {
  color: #3995fb;
  cursor: pointer;
}
.member {
  width: 48%;
  line-height: 30px;
  .red_txt {
    color: #fe5a1e;
  }
  .blue_txt {
    color: #3995fb;
    cursor: pointer;
  }
  .label {
    color: #666;
  }
}
.userFooter {
  /deep/ .el-tabs__item {
    padding: 0 5px 0 0;
  }
}
.teamitem {
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
.imspan {
  color: #409eff;
  cursor: pointer;
  display: inline-block;
}
.assureitem {
  margin: 10px 0;
  font-size: 12px;
  border-bottom: 1px solid #ccc;
  .spaceBetween {
    margin: 5px;
  }
  .price_red {
    color: #fe5a1e;
  }
}
.precnt {
  word-break: break-all;
  white-space: pre-wrap;
}
.userFooter {
  margin: 10px 0;
  text-align: center;
}
.collect_item {
  overflow: hidden;
  margin: 10px 0;
  border-bottom: 1px solid #ccc;
  padding: 15px;
  cursor: pointer;
}
.collect_item_pic {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 6px;
  float: left;
  .productPic {
    object-fit: cover;
    height: 100%;
    width: 100%;
  }
}
.collect_item_pic > image {
  width: 100%;
  height: 100%;
}
.collect_item_right {
  width: 100%;
  height: 80px;
  box-sizing: border-box;
  padding-left: 90px;
}
.collect_item_top {
  font-size: 12px;
  color: #000;
  font-weight: 600;
}
.collect_tit {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  padding-top: 4px;
}
.collect_tit_two {
  font-size: 12px;
  color: #666;
  padding-top: 2px;
  width: 150px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  word-break: break-all;
}
.collect_price {
  font-weight: 700;
  font-size: 14px;
}
.list-price-new {
  color: #fe5a1e;
}
.empty {
  text-align: center;
  line-height: 40px;
}
.back_box {
  margin: 10px 0;
}
.backbtn {
  margin-left: 10px;
}
/deep/ .el-select .el-input {
  width: 100px;
}
/deep/ .input-with-select .el-input-group__prepend {
  background-color: #fff;
}
/deep/ .back_box .el-input-group__prepend {
  background-color: #fff;
}
.recordCard {
  padding: 10px;
  .searchIpt {
    margin-right: 20px;
  }
  .search_box {
    margin-top: 10px;
  }
  .order_box {
    position: relative;
    .spaceBetween {
      justify-content: space-evenly;
    }
  }
  .record_box {
    font-size: 12px;
    flex-wrap: wrap;
    align-items: center;
    .record {
      width: 100%;
      border: 1px solid #ccc;
      padding: 10px;
      margin-top: 10px;
      .label {
        width: 120px;
      }
      .spaceBetween {
        align-items: flex-start;
        line-height: 30px;
      }
      .content {
        color: #666;
        max-width: 400px;
        // 强制换行
        word-break: break-all;
        word-wrap: break-word;
      }
    }
  }
}
.sendGoods {
  min-width: 10px;
  padding: 0 10px;
  border: 1px solid #9a9a9a;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}
.contentText {
  font-weight: 400;
}
</style>
