<template>
  <div class="stepCard">
    <div
      class="content_item spaceBetween"
      v-for="(item, index) in stepList"
      :key="index"
    >
      <div class="cricle" :class="item.current === 1 ? 'active' : ''"></div>
      <div class="item_txt">
        <div>{{ item.stepName }}</div>
        <div
          v-for="(value, key, index) in item.subSteps"
          :key="index"
          class="spaceBetween sub_item"
        >
          <div>{{ value.name }}</div>
          <div>{{ value.startTime }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'stepCard',
  props: {
    flow: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    stepList() {
      let tempList = [];
      if (this.flow.mainSteps) {
        Object.keys(this.flow.mainSteps).forEach(key => {
          // let index = parseInt(key) - 1;
          let item = this.flow.mainSteps[key];
          // list[index] = item;
          tempList.push(item);
        });
        tempList.sort((a, b) => {
          return a - b;
        });
      }
      // list.reverse();
      return tempList;
    }
  },
  data() {
    return {};
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
.stepCard {
  padding: 15px;
  .content_item {
    line-height: 28px;
    align-items: flex-end;
    border-left: 1px solid #e5e5e5;
    padding: 0 0 5px 15px;
    position: relative;
    .item_txt {
      width: 100%;
    }
    .sub_item {
      font-size: 12px;
      color: #ccc;
      line-height: 20px;
    }
    .cricle {
      background: #ccc;
      border-radius: 50%;
      height: 10px;
      width: 10px;
      position: absolute;
      left: -6px;
      top: 10px;
    }
    .active {
      background: red;
    }
    .red_txt {
      color: #fe5a1e;
    }
  }
}
</style>
