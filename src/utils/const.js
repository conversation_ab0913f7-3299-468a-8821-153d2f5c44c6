const transformObj = (data) => {
  const obj = {};
  data.forEach((element) => {
    obj[element.value] = element;
  });
  return obj;
};

// 录号管理-设备状态
export const LH_DEVICE_STATUS = [
  { value: 'IN_PROGRESS', label: '录号中', color: 'primary' },
  { value: 'IDLE', label: '空闲', color: 'success' },
  { value: 'NEED_MANUAL', label: '需人工', color: 'danger' },
  { value: 'OFFLINE', label: '离线', color: 'info' },
  { value: 'RESTART', label: '重启中', color: 'warning' },
];

// 录号管理-设备状态
export const LH_DEVICE_STATUS_OBJ = transformObj(LH_DEVICE_STATUS);

// 录号管理-登录方式
export const LH_LOGIN_TYPE = [
  { value: 'QR_CODE', label: '二维码', alias: '扫码' },
  { value: 'SMS_CODE', label: '短信验证码', alias: '短信' },
  { value: 'PASSWORD', label: '账密登录', alias: '账密' },
];
// 录号管理-登录方式
export const LH_LOGIN_TYPE_OBJ = transformObj(LH_LOGIN_TYPE);

// 录号管理-录号任务状态
export const LH_TASK_STATUS = [
  { value: 'PENDING', label: '待录号', color: '' },
  { value: 'SMS_SENT', label: '短信已发送', color: '' },
  { value: 'MANUAL_REQUIRED', label: '需人工', color: 'warning' },
  { value: 'IN_PROGRESS', label: '录号中', color: '' },
  { value: 'COMPLETED', label: '已完成', color: 'success' },
  { value: 'FAILED', label: '失败', color: 'danger' },
  { value: 'CANCELLED', label: '已取消', color: 'info' },
];

// 录号管理-录号任务状态
export const LH_TASK_STATUS_OBJ = transformObj(LH_TASK_STATUS);

// 账号审核-账号审核状态
export const ACCOUNT_VERTIFY_STATUS = [
  {
    value: '0-1',
    label: '待截图',
    params: { verifyStatus: 0,pushType: 1},
    color:'info'
  },
  {
    value: '0-1-1',
    label: '截图中',
    color:'info',
    params: { verifyStatus: 0, pushType: 1, pushStatus: 1 },
  },
  {
    value: '0-1-2',
    label: '官方录号待审核',
    color:'info',
    params: { verifyStatus: 0, pushType: 1, pushStatus: 2 },
  },
  {
    value: '0-1-3',
    label: '待截图',
    params: { verifyStatus: 0, pushType: 1, pushStatus: 3 },
    color:'info',
    hide:true // 不显示在列表
  },
  {
    value: '0-2',
    label: '待审核',
    params: { verifyStatus: 0, pushType: 2 },
  },
  { value: '1', label: '审核通过',color:'success' },
  {
    value: '2',
    label: '审核不通过',
    color:'danger' ,
    params: { deleteStatus: 2 },
  },
  { value: '-1', label: '用户下架',color:'warning'  },
  { value: '-2', label: '用户删除',color:'danger'  },
];
// 账号审核-账号审核状态
export const ACCOUNT_VERTIFY_STATUS_OBJ = transformObj(ACCOUNT_VERTIFY_STATUS);

// 操作日志动作标识
export const OPERATE_TYPES = [
  {
    label: '用户添加',
    value: 'USER_ADD'
  },
  {
    label: '用户编辑',
    value: 'USER_EDIT'
  },
  {
    label: '用户改价',
    value: 'USER_CHANGE_PRICE'
  },
  {
    label: '用户删除',
    value: 'USER_DELETE'
  },
  {
    label: '用户下架',
    value: 'USER_PUBLISH_DOWN'
  },
  {
    label: '后台添加',
    value: 'ADMIN_ADD'
  },
  {
    label: '后台编辑',
    value: 'ADMIN_EDIT'
  },
  {
    label: '后台删除',
    value: 'ADMIN_DELETE'
  },
  {
    label: '后台审核',
    value: 'ADMIN_VERIFY'
  },
  {
    label: '后台换绑用户',
    value: 'ADMIN_BIND'
  },
  {
    label: '后台上架',
    value: 'ADMIN_PUBLISH_UP'
  },
  {
    label: '后台下架',
    value: 'ADMIN_PUBLISH_DOWN'
  },
  {
    label: '系统上架',
    value: 'SYS_PUBLISH_UP'
  }
];
// 商品详情标题列表
export const GOOD_TITLE_OTHER = [
  {
    label: '珍品传说',
    index: 4,
    children: {
      label: '珍品',
      value: 0,
    },
  },
  {
    label: '无双皮肤',
    index: 5,
    children: {
      label: '无双',
      value: 0,
    },
  },
  {
    label: '荣耀典藏皮肤',
    index: 6,
    children: {
      label: '典藏',
      value: 0,
    },
  },
];
// 商品详情标题列表
export const GOOD_TITLE = [
  {
    label: '大国标数量',
    index: 0,
    children: {
      label: '大国标',
      value: 0,
    },
  },
  {
    label: '小国标数量',
    index: 1,
    children: {
      label: '小国标',
      value: 0,
    },
  },
  {
    label: '省标数量',
    index: 2,
    children: {
      label: '省标',
      value: 0,
    },
  },
  {
    label: '贵族等级',
    index: 3,
    children: {
      label: '',
      value: 0,
    },
  },
  {
    label: '传说皮肤数量',
    index: 7,
    children: {
      label: '传说',
      value: 0,
    },
  },
  {
    label: '史诗皮肤数量',
    index: 8,
    children: {
      label: '史诗',
      value: 0,
    },
  },
  {
    label: '皮肤数量',
    index: 9,
    children: {
      label: '皮肤',
      value: 0,
    },
  },
  {
    label: '英雄数量',
    index: 10,
    children: {
      label: '英雄',
      value: 0,
    },
  },
];
export const OPERATE_TYPES_OBJ = transformObj(OPERATE_TYPES);

