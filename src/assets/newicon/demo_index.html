<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4524340" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe73e;</span>
                <div class="name">免打扰</div>
                <div class="code-name">&amp;#xe73e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">站内消息</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">转发</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">记录</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">勾选</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe762;</span>
                <div class="name">立即确认20x20</div>
                <div class="code-name">&amp;#xe762;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">复制</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">撤回</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c6;</span>
                <div class="name">叹号</div>
                <div class="code-name">&amp;#xe8c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">钱盾-32</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66b;</span>
                <div class="name">问号</div>
                <div class="code-name">&amp;#xe66b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">查看订单</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe673;</span>
                <div class="name">客户群组</div>
                <div class="code-name">&amp;#xe673;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">企业文化</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">法律保护</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">验证 验证码</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">咨询</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">估价</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80f;</span>
                <div class="name">积分</div>
                <div class="code-name">&amp;#xe80f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe810;</span>
                <div class="name">支付</div>
                <div class="code-name">&amp;#xe810;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe811;</span>
                <div class="name">身份</div>
                <div class="code-name">&amp;#xe811;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe812;</span>
                <div class="name">分类</div>
                <div class="code-name">&amp;#xe812;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe813;</span>
                <div class="name">付费</div>
                <div class="code-name">&amp;#xe813;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe814;</span>
                <div class="name">购买</div>
                <div class="code-name">&amp;#xe814;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe816;</span>
                <div class="name">说明</div>
                <div class="code-name">&amp;#xe816;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe817;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xe817;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe818;</span>
                <div class="name">订单</div>
                <div class="code-name">&amp;#xe818;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1722959695392') format('woff2'),
       url('iconfont.woff?t=1722959695392') format('woff'),
       url('iconfont.ttf?t=1722959695392') format('truetype'),
       url('iconfont.svg?t=1722959695392#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-miandarao"></span>
            <div class="name">
              免打扰
            </div>
            <div class="code-name">.icon-miandarao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhanneixiaoxi"></span>
            <div class="name">
              站内消息
            </div>
            <div class="code-name">.icon-zhanneixiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuanfa"></span>
            <div class="name">
              转发
            </div>
            <div class="code-name">.icon-zhuanfa
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jilu"></span>
            <div class="name">
              记录
            </div>
            <div class="code-name">.icon-jilu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huiyishiqueren_huabanfuben"></span>
            <div class="name">
              勾选
            </div>
            <div class="code-name">.icon-huiyishiqueren_huabanfuben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lijiqueren"></span>
            <div class="name">
              立即确认20x20
            </div>
            <div class="code-name">.icon-lijiqueren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhi"></span>
            <div class="name">
              复制
            </div>
            <div class="code-name">.icon-fuzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chehui"></span>
            <div class="name">
              撤回
            </div>
            <div class="code-name">.icon-chehui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tanhao"></span>
            <div class="name">
              叹号
            </div>
            <div class="code-name">.icon-tanhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiandun-32"></span>
            <div class="name">
              钱盾-32
            </div>
            <div class="code-name">.icon-qiandun-32
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon-question"></span>
            <div class="name">
              问号
            </div>
            <div class="code-name">.icon-icon-question
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-065chakandingdan"></span>
            <div class="name">
              查看订单
            </div>
            <div class="code-name">.icon-065chakandingdan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaxun"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-chaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.icon-guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kehuqunzu"></span>
            <div class="name">
              客户群组
            </div>
            <div class="code-name">.icon-kehuqunzu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiyewenhua"></span>
            <div class="name">
              企业文化
            </div>
            <div class="code-name">.icon-qiyewenhua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-falvbaohu"></span>
            <div class="name">
              法律保护
            </div>
            <div class="code-name">.icon-falvbaohu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yanzhengyanzhengma"></span>
            <div class="name">
              验证 验证码
            </div>
            <div class="code-name">.icon-yanzhengyanzhengma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zixun"></span>
            <div class="name">
              咨询
            </div>
            <div class="code-name">.icon-zixun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gujia"></span>
            <div class="name">
              估价
            </div>
            <div class="code-name">.icon-gujia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jifen"></span>
            <div class="name">
              积分
            </div>
            <div class="code-name">.icon-jifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhifu"></span>
            <div class="name">
              支付
            </div>
            <div class="code-name">.icon-zhifu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenfen"></span>
            <div class="name">
              身份
            </div>
            <div class="code-name">.icon-shenfen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenlei"></span>
            <div class="name">
              分类
            </div>
            <div class="code-name">.icon-fenlei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fufei"></span>
            <div class="name">
              付费
            </div>
            <div class="code-name">.icon-fufei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-goumai"></span>
            <div class="name">
              购买
            </div>
            <div class="code-name">.icon-goumai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuoming"></span>
            <div class="name">
              说明
            </div>
            <div class="code-name">.icon-shuoming
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxiang"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.icon-fenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingdan"></span>
            <div class="name">
              订单
            </div>
            <div class="code-name">.icon-dingdan
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-miandarao"></use>
                </svg>
                <div class="name">免打扰</div>
                <div class="code-name">#icon-miandarao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhanneixiaoxi"></use>
                </svg>
                <div class="name">站内消息</div>
                <div class="code-name">#icon-zhanneixiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuanfa"></use>
                </svg>
                <div class="name">转发</div>
                <div class="code-name">#icon-zhuanfa</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jilu"></use>
                </svg>
                <div class="name">记录</div>
                <div class="code-name">#icon-jilu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huiyishiqueren_huabanfuben"></use>
                </svg>
                <div class="name">勾选</div>
                <div class="code-name">#icon-huiyishiqueren_huabanfuben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lijiqueren"></use>
                </svg>
                <div class="name">立即确认20x20</div>
                <div class="code-name">#icon-lijiqueren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhi"></use>
                </svg>
                <div class="name">复制</div>
                <div class="code-name">#icon-fuzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chehui"></use>
                </svg>
                <div class="name">撤回</div>
                <div class="code-name">#icon-chehui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tanhao"></use>
                </svg>
                <div class="name">叹号</div>
                <div class="code-name">#icon-tanhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiandun-32"></use>
                </svg>
                <div class="name">钱盾-32</div>
                <div class="code-name">#icon-qiandun-32</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon-question"></use>
                </svg>
                <div class="name">问号</div>
                <div class="code-name">#icon-icon-question</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-065chakandingdan"></use>
                </svg>
                <div class="name">查看订单</div>
                <div class="code-name">#icon-065chakandingdan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaxun"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-chaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#icon-guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kehuqunzu"></use>
                </svg>
                <div class="name">客户群组</div>
                <div class="code-name">#icon-kehuqunzu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiyewenhua"></use>
                </svg>
                <div class="name">企业文化</div>
                <div class="code-name">#icon-qiyewenhua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-falvbaohu"></use>
                </svg>
                <div class="name">法律保护</div>
                <div class="code-name">#icon-falvbaohu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yanzhengyanzhengma"></use>
                </svg>
                <div class="name">验证 验证码</div>
                <div class="code-name">#icon-yanzhengyanzhengma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zixun"></use>
                </svg>
                <div class="name">咨询</div>
                <div class="code-name">#icon-zixun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gujia"></use>
                </svg>
                <div class="name">估价</div>
                <div class="code-name">#icon-gujia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jifen"></use>
                </svg>
                <div class="name">积分</div>
                <div class="code-name">#icon-jifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhifu"></use>
                </svg>
                <div class="name">支付</div>
                <div class="code-name">#icon-zhifu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenfen"></use>
                </svg>
                <div class="name">身份</div>
                <div class="code-name">#icon-shenfen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenlei"></use>
                </svg>
                <div class="name">分类</div>
                <div class="code-name">#icon-fenlei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fufei"></use>
                </svg>
                <div class="name">付费</div>
                <div class="code-name">#icon-fufei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-goumai"></use>
                </svg>
                <div class="name">购买</div>
                <div class="code-name">#icon-goumai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuoming"></use>
                </svg>
                <div class="name">说明</div>
                <div class="code-name">#icon-shuoming</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#icon-fenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingdan"></use>
                </svg>
                <div class="name">订单</div>
                <div class="code-name">#icon-dingdan</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
