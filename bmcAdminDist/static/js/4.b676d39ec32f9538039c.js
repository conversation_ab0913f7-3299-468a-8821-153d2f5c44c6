webpackJsonp([4],{E5we:function(t,e){},SJ0n:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("woOf"),i=n.n(a),l=n("tUEa"),r=n("mRsl"),o=n("xT6B"),s={pageNum:1,pageSize:20},u={name:"Help",components:{Tinymce:n("5aCZ").a},filters:{formatCreateTime:function(t){var e=new Date(t);return Object(o.a)(e,"YYYY-MM-DD HH:mm:ss")},verifyStatusFilter:function(t){return t?"启用":"禁用"},verifyStatusFilter2:function(t){return t?"禁用":"启用"}},data:function(){return{listLoading:!0,list:[],total:0,showAddModel:!1,listQuery:i()({},s),productCategoryOptions:[],value:{name:"",content:"",publishStatus:"",detailHtml:"",productCategoryId:""},rules:{},formLabelWidth:"140px"}},watch:{"$route.meta":{handler:function(t,e){this.doInit()},immediate:!0}},methods:{doInit:function(){var t=this.$route.meta.brandId;this.brandId=t,this.listQuery.brandId=this.brandId,this.getList()},getButtonType:function(t){return t?"danger":"success"},toggleState:function(t,e){var n=this,a=e.id,r=i()({},e);r.publishStatus=e.publishStatus?0:1,Object(l.i)(a,r).then(function(){n.getList()})},handleDelete:function(t,e){var n=this;this.$confirm("是否要删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(l.b)({ids:e.id,deleteStatus:1}).then(function(t){n.$message({type:"success",message:"删除成功!"}),n.getList()})})},getList:function(){var t=this;this.listLoading=!0,Object(l.c)(this.listQuery).then(function(e){t.listLoading=!1;var n=e.data.list;t.list=n,t.total=e.data.total})},handleUpdateProduct:function(t,e){var n=this,a=e.id;Object(r.e)().then(function(t){var e=t.data;62==n.brandId?n.productCategoryOptions=e.find(function(t){return"帮助中心"==t.name}).children:n.productCategoryOptions=e.find(function(t){return"活动中心"===t.name}).children,Object(l.f)(a).then(function(t){console.log(t),n.value=i()({},t.data),n.id=a,n.isEdit=!0,n.showAddModel=!0})})},createHelp:function(){var t=this;this.clearValue(),Object(r.e)().then(function(e){var n=e.data;62==t.brandId?t.productCategoryOptions=n.find(function(t){return"帮助中心"==t.name}).children:t.productCategoryOptions=n.find(function(t){return"活动中心"===t.name}).children,t.id="",t.isEdit=!1,t.showAddModel=!0})},clearValue:function(){this.value={name:"",content:"",publishStatus:"",detailHtml:"",productCategoryId:""}},handleCreateProduct:function(){this.clearValue(),this.isEdit=!1,this.showAddModel=!0},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},cancel:function(){this.showAddModel=!1},submitForm:function(){var t=this;this.showAddModel=!1;var e=this.productCategoryOptions.find(function(e){return e.id===t.value.productCategoryId});if(this.value.productCategoryName=e.name,this.isEdit)Object(l.i)(this.id,this.value).then(function(){t.getList()});else{var n=i()({brandId:this.brandId},this.value);Object(l.a)(n).then(function(){t.getList()})}}}},d={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"table-container"},[n("el-button",{staticClass:"add-help",attrs:{type:"primary"},on:{click:t.createHelp}},[t._v("新建")]),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[n("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"标题",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.name))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"分类",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.productCategoryName))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"帮助状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(t._f("verifyStatusFilter")(e.row.publishStatus)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"阅读数量",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.gameSysinfoReadcount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"更新时间",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatCreateTime")(e.row.updateTime)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"创建时间",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatCreateTime")(e.row.createTime)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"260",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.handleUpdateProduct(e.$index,e.row)}}},[t._v("编辑\n            ")]),t._v(" "),n("el-button",{attrs:{type:t.getButtonType(e.row.publishStatus),size:"mini"},on:{click:function(n){return t.toggleState(e.$index,e.row)}}},[t._v("\n              "+t._s(t._f("verifyStatusFilter2")(e.row.publishStatus))+"\n            ")]),t._v(" "),n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.handleDelete(e.$index,e.row)}}},[t._v("\n              删除\n            ")])],1)]}}])})],1),t._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"current-page":t.listQuery.pageNum,"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),n("el-dialog",{attrs:{visible:t.showAddModel,width:"80%",top:"1vh"},on:{"update:visible":function(e){t.showAddModel=e}}},[n("el-card",[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[t._v("编辑帮助")])]),t._v(" "),n("el-form",{ref:"helpForm",staticClass:"form-box",attrs:{model:t.value,rules:t.rules,"label-width":t.formLabelWidth}},[n("el-form-item",{attrs:{label:"标题"}},[n("el-input",{model:{value:t.value.name,callback:function(e){t.$set(t.value,"name",e)},expression:"value.name"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"分类"}},[n("el-select",{model:{value:t.value.productCategoryId,callback:function(e){t.$set(t.value,"productCategoryId",e)},expression:"value.productCategoryId"}},t._l(t.productCategoryOptions,function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"是否显示"}},[n("el-radio-group",{model:{value:t.value.publishStatus,callback:function(e){t.$set(t.value,"publishStatus",e)},expression:"value.publishStatus"}},[n("el-radio",{attrs:{label:1}},[t._v("是")]),t._v(" "),n("el-radio",{attrs:{label:0}},[t._v("否")])],1)],1),t._v(" "),n("el-form-item",{attrs:{label:""}},[t.showAddModel?n("tinymce",{attrs:{height:400,width:"100%"},model:{value:t.value.detailHtml,callback:function(e){t.$set(t.value,"detailHtml",e)},expression:"value.detailHtml"}}):t._e()],1)],1),t._v(" "),n("div",{staticClass:"m-footer"},[n("el-button",{on:{click:t.cancel}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("helpForm")}}},[t._v("确 定")])],1)],1)],1)],1)])},staticRenderFns:[]};var c=n("VU/8")(u,d,!1,function(t){n("E5we")},"data-v-60d742d0",null);e.default=c.exports}});