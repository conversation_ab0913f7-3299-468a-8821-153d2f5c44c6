webpackJsonp([87],{WbiX:function(t,e){},z7op:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n("woOf"),a=n.n(i),l=(n("PJh5"),n("IcnI"),n("0xDb")),s=n("QPAx"),r={keyword:null,productSn:null,pageNum:1,pageSize:20},o={components:{},data:function(){return{util:l.b,listQuery:a()({},r),list:null,total:null,listLoading:!0,showPriceModal:!1,newPrice:"",newPriceId:""}},created:function(){this.initList()},methods:{cancelPrice:function(){this.showPriceModal=!1},submitPrice:function(){Object(s.d)(this.newPriceId,this.modifyItem)},handleSearchList:function(){this.listQuery.pageNum=1,this.initList()},handleResetSearch:function(){this.listQuery=a()({},r)},goProduct:function(t){var e=t.id;window.open("https://www.kkzhw.com/playDetail?productId="+e)},changePrice:function(t,e){this.newPrice=e.price,this.newPriceId=e.id,this.showPriceModal=!0},handleUp:function(t,e){var n=this,i=e.publishStatus?0:1;Object(s.e)({},{ids:e.id,publishStatus:i}).then(function(e){n.$set(n.list[t],"publishStatus",i)})},handleDown:function(t,e){Object(s.e)({},{ids:e.id,publishStatus:0})},handleSelectionChange:function(){},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.initList()},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},initList:function(){var t=this;Object(s.f)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})}}},c={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[n("div",{staticStyle:{"margin-top":"15px"}},[n("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[n("el-form-item",{attrs:{label:"输入搜索："}},[n("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品信息"},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"商品编号："}},[n("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品编号"},model:{value:t.listQuery.productSn,callback:function(e){t.$set(t.listQuery,"productSn",e)},expression:"listQuery.productSn"}})],1),t._v(" "),n("el-form-item",[n("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询结果\n          ")]),t._v(" "),n("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{type:"index",width:"60",label:"序号"}}),t._v(" "),n("el-table-column",{attrs:{label:"商品编号",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.productSn))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"售价",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.price))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"商品信息",width:"460",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticClass:"goodsHome_shopItem_box",on:{click:function(n){return t.goProduct(e.row)}}},[n("div",{staticClass:"goods_shopItem_top"},[n("div",{staticClass:"goods_shopItem_pic"},[n("img",{attrs:{src:e.row.pic}})]),t._v(" "),n("div",{staticClass:"goods_showItem_right"},[n("div",{staticClass:"goodsHome_Item_header"},[e.row.subTitle?n("div",{staticClass:"subTitleMp",attrs:{"prediv-img":!1,"show-img-menu":!1,"lazy-load":!1},domProps:{innerHTML:t._s(e.row.subTitle)}}):t._e()])])]),t._v(" "),n("div",{staticClass:"goods_shopItem_bottom spaceBetween"},[n("div",[e.row.brandName?n("span",{staticClass:"ext_item"},[t._v("\n                  "+t._s(e.row.brandName)+"\n                ")]):t._e(),t._v(" "),e.row.gameAccountQufu?n("span",{staticClass:"ext_item"},[t._v("\n                  "+t._s(e.row.gameAccountQufu)+"\n                ")]):t._e()])])])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"浏览量",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.gameSysinfoReadcount))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"下架时间",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[n("p")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"上架时间",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v("\n            "+t._s(t.util.timeFormat(e.row.createTime,"YYYY-MM-DD HH:mm:ss"))+"\n          ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"出售时间",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[n("p")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"状态",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.publishStatus?"已上架":"已下架"))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(n){return t.handleUp(e.$index,e.row)}}},[t._v(t._s(1===e.row.publishStatus?"下架":"上架")+"\n          ")]),t._v(" "),n("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(n){return t.changePrice(e.$index,e.row)}}},[t._v("改价\n          ")])]}}])})],1)],1),t._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),n("el-dialog",{attrs:{visible:t.showPriceModal,title:"改价",width:"30%",center:"",top:"1vh"},on:{"update:visible":function(e){t.showPriceModal=e}}},[n("el-form",{ref:"verifyForm"},[n("el-form-item",{attrs:{label:"新价格"}},[n("el-input",{model:{value:t.newPrice,callback:function(e){t.newPrice=e},expression:"newPrice"}})],1)],1),t._v(" "),n("div",{staticClass:"m-footer"},[n("el-button",{on:{click:t.cancelPrice}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:t.submitPrice}},[t._v("确定")])],1)],1)],1)},staticRenderFns:[]};var u=n("VU/8")(o,c,!1,function(t){n("WbiX")},"data-v-42f4ae14",null);e.default=u.exports}});