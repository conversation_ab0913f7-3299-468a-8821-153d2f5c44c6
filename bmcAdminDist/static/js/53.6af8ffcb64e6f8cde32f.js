webpackJsonp([53],{MKYo:function(e,t){},lfyr:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n("c/Tr"),a=n.n(r),i=n("lHA8"),s=n.n(i),l=n("bUp0"),o=n("STSY"),c={name:"allocMenu",data:function(){return{menuTreeList:[],defaultProps:{children:"children",label:"title"},roleId:null}},created:function(){this.roleId=this.$route.query.roleId,this.treeList(),this.getRoleMenu(this.roleId)},methods:{treeList:function(){var e=this;Object(l.d)().then(function(t){e.menuTreeList=t.data})},getRoleMenu:function(e){var t=this;Object(o.g)(e).then(function(e){var n=e.data,r=[];if(null!=n&&n.length>0)for(var a=0;a<n.length;a++){var i=n[a];0!==i.parentId&&r.push(i.id)}t.$refs.tree.setCheckedKeys(r)})},handleSave:function(){var e=this,t=this.$refs.tree.getCheckedNodes(),n=new s.a;if(null!=t&&t.length>0)for(var r=0;r<t.length;r++){var i=t[r];n.add(i.id),0!==i.parentId&&n.add(i.parentId)}this.$confirm("是否分配菜单？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=new URLSearchParams;t.append("roleId",e.roleId),t.append("menuIds",a()(n)),Object(o.a)(t).then(function(t){e.$message({message:"分配成功",type:"success",duration:1e3}),e.$router.back()})})},handleClear:function(){this.$refs.tree.setCheckedKeys([])}}},d={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{staticClass:"form-container",attrs:{shadow:"never"}},[n("el-tree",{ref:"tree",attrs:{data:e.menuTreeList,"show-checkbox":"","default-expand-all":"","node-key":"id","highlight-current":"",props:e.defaultProps}}),e._v(" "),n("div",{staticStyle:{"margin-top":"20px"},attrs:{align:"center"}},[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSave()}}},[e._v("保存")]),e._v(" "),n("el-button",{on:{click:function(t){return e.handleClear()}}},[e._v("清空")])],1)],1)},staticRenderFns:[]};var u=n("VU/8")(c,d,!1,function(e){n("MKYo")},"data-v-b6c4e48c",null);t.default=u.exports}});