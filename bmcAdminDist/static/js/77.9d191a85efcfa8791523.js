webpackJsonp([77],{GkIT:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=i("mvHQ"),n=i.n(a),s=i("Lfj9"),o=i("/ekJ"),l=i.n(o),r=(i("mERs"),{name:"ProductCateList",data:function(){return{upStatus:"",loading:!1,upImVisible:!1,qrcode:"",list:null,times:null,dialogFormVisibleTitle:"",dialogFormVisible:!1,total:null,productCategoryList:[],listLoading:!0,imStatusType:{STARTING:"启动中",ONLINE:"在线",OFFLINE:"离线",STOPPING:"下线中"},productUnitList:[{id:"ACTIVE",name:"正常营业"},{id:"INACTIVE",name:"未营业（暂时关闭）"},{id:"CLOSED",name:"永久关闭"}],platformType:{XY:"闲鱼",TB:"淘宝",JD:"京东",PDD:"拼多多"},productUnitType:{ACTIVE:"正常营业",INACTIVE:"未营业（暂时关闭）",CLOSED:"永久关闭"},platformListType:[{label:"闲鱼",value:"XY"}],formValue:{platform:"XY",userIdentity:"",userName:"",userNick:"",shopName:"",shopStatus:"",configInfo:""},listQuery:{pageNum:1,pageSize:20},parentId:0,isFlag:!1}},watch:{dialogFormVisible:function(t){var e=this;t&&0==this.isFlag&&setTimeout(function(){e.isFlag=!0,e.initJsonEdit2()})}},created:function(){this.getList()},mounted:function(){},methods:{handleUp:function(t,e){var i=this;this.$confirm("您确定要上线？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){i.onThirdShopCommand(e.id,"ONLINE")})},handleDown:function(t,e){var i=this;this.$confirm("您确定要下线？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){i.onThirdShopCommand(e.id,"OFFLINE")})},onThirdShopCommand:function(t,e){var i=this,a={thirdShopId:t,command:e};Object(s._24)(a).then(function(a){200==a.code&&("OFFLINE"==e&&(i.$message.success("操作成功"),i.getList()),"ONLINE"==e&&(i.loading=!0,i.upImVisible=!0,i.times=setInterval(function(){i.getDetailStatus(t)},2e3)),i.getList())})},upClose:function(){console.log("点击了"),this.times&&clearInterval(this.times),this.upImVisible=!1,this.loading=!1},getDetailStatus:function(t){var e=this;Object(s._25)(t).then(function(t){if(200==t.code){var i=JSON.parse(t.data.propertyBag);"login_success"!=i.state&&"login_timeout"!=i.state||(clearInterval(e.times),e.loading=!1),"login_success"==i.state&&(e.getList(),e.upImVisible=!1,e.$message.success("上线成功")),e.qrcode=t.data.qrcode,e.upStatus=i.state}console.log(t)})},initJsonEdit2:function(){this.editor2={};var t=document.getElementById("jsoneditor2");console.log(1111111),this.editor2=new l.a(t,{mode:"text",indentation:2,enableSort:!1,enableTransform:!1})},cloneBtn:function(){this.dialogFormVisible=!1},submitBtn:function(){var t=this;this.$refs.form.validate(function(e){if(e)if("新增"==t.dialogFormVisibleTitle){var i=t.editor2.get();t.formValue.configInfo=n()(i),Object(s.e)(t.formValue).then(function(e){200===e.code&&(t.$message.success("添加成功"),t.dialogFormVisible=!1,t.getList())})}else{var a=t.editor2.get();t.formValue.configInfo=n()(a),Object(s._27)(t.formValue).then(function(e){t.$message.success("编辑成功"),t.dialogFormVisible=!1,t.getList()})}})},handleAddProductCate:function(){var t=this;this.dialogFormVisibleTitle="新增",this.formValue={platform:"XY",userIdentity:"",userName:"",userNick:"",shopName:"",shopStatus:"",configInfo:""},this.dialogFormVisible=!0,setTimeout(function(){t.editor2.set({})},100)},getList:function(){var t=this;this.listLoading=!0,Object(s.v)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleShowStatusChange:function(t,e){var i=this,a=new URLSearchParams,n=[];n.push(e.id),a.append("ids",n),a.append("showStatus",e.showStatus),Object(s._27)(a).then(function(t){i.$message({message:"修改成功",type:"success",duration:1e3})})},handleUpdate:function(t,e){var i=this;this.dialogFormVisibleTitle="编辑",this.formValue=JSON.parse(n()(e)),console.log(e),this.dialogFormVisible=!0,setTimeout(function(){i.editor2.set(JSON.parse(e.configInfo))},200)},handleDelete:function(t,e){var i=this;this.$confirm("是否要删除该分类","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(s.g)(e.id).then(function(t){i.$message({message:"删除成功",type:"success",duration:1e3}),i.getList()})})}}}),u={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[i("i",{staticClass:"el-icon-tickets",staticStyle:{"margin-top":"5px"}}),t._v(" "),i("span",{staticStyle:{"margin-top":"5px"}},[t._v("店铺管理")]),t._v(" "),i("el-button",{staticClass:"btn-add",attrs:{size:"mini"},on:{click:function(e){return t.handleAddProductCate()}}},[t._v("\n      添加\n    ")])],1),t._v(" "),i("div",{staticClass:"table-container"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productCateTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[i("el-table-column",{attrs:{label:"平台",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.platformType[e.row.platform]))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"店铺会员ID",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.userIdentity))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"店铺会员名",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.userName))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"会员昵称",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.userNick))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"店铺名称",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.shopName))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"IM状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.imStatusType[e.row.imStatus]))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"店铺状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.productUnitType[e.row.shopStatus]))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"操作",width:"230",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["OFFLINE"==e.row.imStatus&&"STARTING"!=e.row.imStatus?i("el-button",{attrs:{size:"mini"},on:{click:function(i){return t.handleUp(e.$index,e.row)}}},[t._v("上线\n          ")]):t._e(),t._v(" "),"ONLINE"==e.row.imStatus&&"STOPPING"!=e.row.imStatus?i("el-button",{attrs:{size:"mini"},on:{click:function(i){return t.handleDown(e.$index,e.row)}}},[t._v("下线\n          ")]):t._e(),t._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:function(i){return t.handleUpdate(e.$index,e.row)}}},[t._v("编辑\n          ")]),t._v(" "),i("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(i){return t.handleDelete(e.$index,e.row)}}},[t._v("删除\n          ")])]}}])})],1)],1),t._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),i("el-dialog",{attrs:{title:t.dialogFormVisibleTitle,visible:t.dialogFormVisible,width:"900px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[i("el-form",{ref:"form",attrs:{"label-width":"100px",model:t.formValue}},[i("el-form-item",{attrs:{label:"平台："}},[i("el-select",{staticStyle:{width:"700px"},attrs:{placeholder:"请选择平台"},model:{value:t.formValue.platform,callback:function(e){t.$set(t.formValue,"platform",e)},expression:"formValue.platform"}},t._l(t.platformListType,function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),i("el-form-item",{attrs:{label:"店铺会员ID："}},[i("el-input",{staticClass:"editNum",staticStyle:{width:"700px"},model:{value:t.formValue.userIdentity,callback:function(e){t.$set(t.formValue,"userIdentity",e)},expression:"formValue.userIdentity"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"店铺会员名："}},[i("el-input",{staticClass:"editNum",staticStyle:{width:"700px"},model:{value:t.formValue.userName,callback:function(e){t.$set(t.formValue,"userName",e)},expression:"formValue.userName"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"会员昵称："}},[i("el-input",{staticClass:"editNum",staticStyle:{width:"700px"},model:{value:t.formValue.userNick,callback:function(e){t.$set(t.formValue,"userNick",e)},expression:"formValue.userNick"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"店铺名称："}},[i("el-input",{staticClass:"editNum",staticStyle:{width:"700px"},model:{value:t.formValue.shopName,callback:function(e){t.$set(t.formValue,"shopName",e)},expression:"formValue.shopName"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"店铺状态："}},[i("el-select",{staticStyle:{width:"700px"},attrs:{placeholder:"请选择"},model:{value:t.formValue.shopStatus,callback:function(e){t.$set(t.formValue,"shopStatus",e)},expression:"formValue.shopStatus"}},t._l(t.productUnitList,function(t){return i("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),i("el-form-item",{attrs:{label:"配置信息："}},[i("div",{staticStyle:{width:"700px",height:"500px"},attrs:{id:"jsoneditor2"}})])],1),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:t.cloneBtn}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:t.submitBtn}},[t._v("确 定")])],1)],1),t._v(" "),i("el-dialog",{attrs:{title:"上线",visible:t.upImVisible,width:"700px"},on:{close:t.upClose,"update:visible":function(e){t.upImVisible=e}}},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"imUpStatus_box"},[t.qrcode?i("div",{staticClass:"imUpStatus_box"},[i("img",{attrs:{src:t.qrcode,alt:""}}),t._v(" "),"login_timeout"==t.upStatus?i("div",{staticStyle:{color:"red"}},[t._v("登陆超时，请稍后重试")]):t._e(),t._v(" "),"login_wait"==t.upStatus?i("p",[t._v("请扫描二维码登陆")]):t._e(),t._v(" "),"login_success"==t.upStatus?i("p",{staticStyle:{color:"#90EE90"}},[t._v("登陆成功")]):t._e()]):t._e(),t._v(" "),t.qrcode?t._e():i("p",[t._v("等待登录二维码")])])])],1)},staticRenderFns:[]};var c=i("VU/8")(r,u,!1,function(t){i("gMX4")},"data-v-4e6c6a5f",null);e.default=c.exports},gMX4:function(t,e){}});