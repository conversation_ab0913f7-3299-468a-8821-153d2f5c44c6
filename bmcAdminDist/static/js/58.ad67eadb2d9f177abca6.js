webpackJsonp([58],{Zl8l:function(e,t){},ZxUv:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=l("woOf"),i=l.n(a),s=l("TZVV"),n=l("NS8l"),r=l("STSY"),o=l("xT6B"),u=l("Cmve"),c={pageNum:1,pageSize:20},d={name:"",phoneNumber:"",type:3},v={components:{SingleUpload:s.a},filters:{formatDateTime:function(e){if(null==e||""===e)return"N/A";var t=new Date(e);return Object(o.a)(t,"YYYY-MM-DD HH:mm:ss")}},data:function(){return{statusRow:{},areas:u.a,listQuery:i()({},c),list:null,total:null,listLoading:!1,dialogVisible:!1,value:i()({},d),isEdit:!1,allocDialogVisible:!1,allocRoleIds:[],allRoleList:[],allocAdminId:null}},created:function(){this.getList()},methods:{getState:function(e){switch(e){case 0:return"审核中";case 1:return"拒绝";case 2:return"通过";default:return"待提交"}},handleAllocDialogConfirm:function(){var e=this,t={status:this.statusRow.status,type:3,desc:this.statusRow.desc||void 0};Object(n.b)(this.statusRow.id,t).then(function(t){e.$message({message:"提交成功！",type:"success"}),e.allocDialogVisible=!1,e.getList()})},handleSelectRole:function(e,t){var l=this;Object(n.d)(t.id).then(function(e){l.value=i()({},e.data),l.allocDialogVisible=!0})},handleResetSearch:function(){this.listQuery=i()({},c)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},handleAdd:function(){this.dialogVisible=!0,this.isEdit=!1,this.value=i()({},d)},handleUpdate:function(e,t){var l=this;Object(n.d)(t.id).then(function(e){l.dialogVisible=!0,l.isEdit=!0,l.value=i()({},e.data)})},handleDialogConfirm2:function(){var e=this;this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(n.c)(e.value.id,e.value).then(function(t){e.$message({message:"修改成功！",type:"success"}),e.allocDialogVisible=!1,e.getList()})})},handleDialogConfirm:function(){var e=this;this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.isEdit?Object(n.c)(e.value.id,e.value).then(function(t){e.$message({message:"修改成功！",type:"success"}),e.dialogVisible=!1,e.getList()}):Object(n.a)(e.value).then(function(t){e.$message({message:"添加成功！",type:"success"}),e.dialogVisible=!1,e.getList()})})},getList:function(){var e=this;this.listLoading=!0,Object(n.e)(this.listQuery).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})},getAllRoleList:function(){var e=this;Object(r.e)().then(function(t){e.allRoleList=t.data})}}},m={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[l("div",{staticStyle:{"margin-top":"15px"}},[l("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[l("el-form-item",{attrs:{label:"输入搜索："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"帐号/姓名",clearable:""},model:{value:e.listQuery.keyword,callback:function(t){e.$set(e.listQuery,"keyword",t)},expression:"listQuery.keyword"}})],1),e._v(" "),l("el-form-item",[l("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询搜索\n          ")]),e._v(" "),l("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),l("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[l("i",{staticClass:"el-icon-tickets"}),e._v(" "),l("span",[e._v("数据列表")]),e._v(" "),l("el-button",{staticClass:"btn-add",staticStyle:{"margin-left":"20px"},attrs:{size:"mini"},on:{click:function(t){return e.handleAdd()}}},[e._v("添加")])],1),e._v(" "),l("div",{staticClass:"table-container"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"valueTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[l("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"用户名",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.memberUserName))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"姓名",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.userIdName))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"身份证号",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.userIdNumber))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"紧急联系人",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"紧急联系人手机号",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phoneNumber))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"实名认证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.getState(t.row.defaultStatus)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"人脸认证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.getState(t.row.faceStatus)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"包赔审核状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.getState(t.row.baopeiStatus)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(l){return e.handleSelectRole(t.$index,t.row)}}},[e._v("\n            审核\n          ")]),e._v(" "),l("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(l){return e.handleUpdate(t.$index,t.row)}}},[e._v("\n            编辑\n          ")])]}}])})],1)],1),e._v(" "),l("div",{staticClass:"pagination-container"},[l("el-pagination",{attrs:{"current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),l("el-dialog",{attrs:{title:e.isEdit?"编辑":"添加",visible:e.dialogVisible,width:"60%",top:"1vh"},on:{"update:visible":function(t){e.dialogVisible=t}}},[l("el-form",{ref:"valueForm",staticStyle:{display:"flex","flex-wrap":"wrap"},attrs:{model:e.value,"label-width":"150px",size:"small"}},[l("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"用户名："}},[l("el-input",{staticStyle:{width:"200px"},attrs:{disabled:""},model:{value:e.value.memberUserName,callback:function(t){e.$set(e.value,"memberUserName",t)},expression:"value.memberUserName"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"真实姓名："}},[l("el-input",{staticStyle:{width:"200px"},model:{value:e.value.userIdName,callback:function(t){e.$set(e.value,"userIdName",t)},expression:"value.userIdName"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"身份证号："}},[l("el-input",{staticStyle:{width:"200px"},model:{value:e.value.userIdNumber,callback:function(t){e.$set(e.value,"userIdNumber",t)},expression:"value.userIdNumber"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"身份证正面："}},[l("single-upload",{model:{value:e.value.userIdPic1,callback:function(t){e.$set(e.value,"userIdPic1",t)},expression:"value.userIdPic1"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"身份证反面："}},[l("single-upload",{model:{value:e.value.userIdPic2,callback:function(t){e.$set(e.value,"userIdPic2",t)},expression:"value.userIdPic2"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"紧急联系人姓名："}},[l("el-input",{staticStyle:{width:"200px"},model:{value:e.value.name,callback:function(t){e.$set(e.value,"name",t)},expression:"value.name"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"紧急联系人电话："}},[l("el-input",{staticStyle:{width:"200px"},model:{value:e.value.phoneNumber,callback:function(t){e.$set(e.value,"phoneNumber",t)},expression:"value.phoneNumber"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"订单图片："}},[l("single-upload",{model:{value:e.value.userOrderPic,callback:function(t){e.$set(e.value,"userOrderPic",t)},expression:"value.userOrderPic"}})],1)],1),e._v(" "),l("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),l("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleDialogConfirm()}}},[e._v("确 定")])],1)],1),e._v(" "),l("el-dialog",{attrs:{visible:e.allocDialogVisible,title:"审核",width:"60%",top:"1vh"},on:{"update:visible":function(t){e.allocDialogVisible=t}}},[l("el-form",{ref:"valueForm",staticStyle:{display:"flex","flex-wrap":"wrap"},attrs:{model:e.value,"label-width":"150px",size:"small"}},[l("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"实名认证："}},[l("el-radio-group",{model:{value:e.value.defaultStatus,callback:function(t){e.$set(e.value,"defaultStatus",t)},expression:"value.defaultStatus"}},[l("el-radio",{attrs:{label:0}},[e._v("待审核")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("无效")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("有效")])],1)],1),e._v(" "),l("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"人脸认证："}},[l("el-radio-group",{model:{value:e.value.faceStatus,callback:function(t){e.$set(e.value,"faceStatus",t)},expression:"value.faceStatus"}},[l("el-radio",{attrs:{label:0}},[e._v("待审核")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("无效")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("有效")])],1)],1),e._v(" "),l("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"包赔认证："}},[l("el-radio-group",{model:{value:e.value.baopeiStatus,callback:function(t){e.$set(e.value,"baopeiStatus",t)},expression:"value.baopeiStatus"}},[l("el-radio",{attrs:{label:0}},[e._v("待审核")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("无效")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("有效")])],1)],1),e._v(" "),l("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"用户名："}},[l("el-input",{staticStyle:{width:"200px"},attrs:{disabled:""},model:{value:e.value.memberUserName,callback:function(t){e.$set(e.value,"memberUserName",t)},expression:"value.memberUserName"}})],1),e._v(" "),1===e.value.baopeiStatus?l("el-form-item",{staticStyle:{width:"90%"},attrs:{label:"包赔拒绝原因:"}},[l("el-input",{model:{value:e.value.region,callback:function(t){e.$set(e.value,"region",t)},expression:"value.region"}})],1):e._e(),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"真实姓名："}},[l("el-input",{staticStyle:{width:"200px"},attrs:{disabled:""},model:{value:e.value.userIdName,callback:function(t){e.$set(e.value,"userIdName",t)},expression:"value.userIdName"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"身份证号："}},[l("el-input",{staticStyle:{width:"200px"},attrs:{disabled:""},model:{value:e.value.userIdNumber,callback:function(t){e.$set(e.value,"userIdNumber",t)},expression:"value.userIdNumber"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"身份证正面："}},[l("el-image",{attrs:{alt:"暂无图片",src:e.value.userIdPic1,fit:"fill"}},[l("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[l("i",{staticClass:"el-icon-picture-outline"})])])],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"身份证反面："}},[l("el-image",{attrs:{alt:"暂无图片",src:e.value.userIdPic2,fit:"fill"}},[l("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[l("i",{staticClass:"el-icon-picture-outline"})])])],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"紧急联系人姓名："}},[l("el-input",{staticStyle:{width:"200px"},attrs:{disabled:""},model:{value:e.value.name,callback:function(t){e.$set(e.value,"name",t)},expression:"value.name"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"紧急联系人电话："}},[l("el-input",{staticStyle:{width:"200px"},attrs:{disabled:""},model:{value:e.value.phoneNumber,callback:function(t){e.$set(e.value,"phoneNumber",t)},expression:"value.phoneNumber"}})],1),e._v(" "),l("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"订单图片："}},[l("el-image",{attrs:{alt:"暂无图片",src:e.value.userOrderPic,fit:"fill"}},[l("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[l("i",{staticClass:"el-icon-picture-outline"})])])],1)],1),e._v(" "),l("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{size:"small"},on:{click:function(t){e.allocDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),l("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleDialogConfirm2()}}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var f=l("VU/8")(v,m,!1,function(e){l("Zl8l")},null,null);t.default=f.exports}});