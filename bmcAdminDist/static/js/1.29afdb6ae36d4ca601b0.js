webpackJsonp([1],{"2nSJ":function(e,t){},pAhd:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:this.loading,expression:"loading"}]},[t("iframe",{ref:"myIframe",staticStyle:{"min-height":"calc(100vh - 60px)"},attrs:{src:this.iframeUrl,width:"100%",frameborder:"0"},on:{load:this.onIframeLoad}},[t("p",[this._v("您的浏览器不支持iframe标签。")])])])},staticRenderFns:[]};var n=r("VU/8")({data:function(){return{iframeUrl:"",loading:!0}},mounted:function(){this.iframeUrl=this.$route.query.url},watch:{$route:function(e,t){var r=this.$route.query.url;r&&(this.iframeUrl=r)}},methods:{onIframeLoad:function(){this.loading=!1}}},i,!1,function(e){r("2nSJ")},null,null);t.default=n.exports}});