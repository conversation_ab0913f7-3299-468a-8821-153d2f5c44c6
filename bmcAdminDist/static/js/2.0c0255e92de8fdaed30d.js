webpackJsonp([2],{h1b1:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("woOf"),i=a.n(n),s=a("so1O"),r=a("Lfj9"),l=a("0xDb"),c={components:{MyTable:s.a},data:function(){return{activeName:"NEGOTIATING",util:l.b,dialogVisibleInden:!1,counteroffer_price:"",dicker_id:"",options:[{value:"",label:"查看全部"},{value:"NEGOTIATING",label:"议价中"},{value:"NEGOTIATION_SUCCESS",label:"议价成功"},{value:"NEGOTIATION_FAILED_OR_CANCELED",label:"议价失败/取消"}],searchData:{negoStatus:"NEGOTIATING"},defaultListQuery:{productSn:null,negoStatus:""},operationList:[{name:"商品编号",width:"150",value:"productSn"},{name:"分类名称",width:"150",value:"productCategoryName"},{name:"商品图",value:"name",slotName:"goodsPic",width:"120"},{name:"单价",width:"100",value:"originPrice"},{name:"议价",width:"100",value:"offerPrice"},{name:"状态",width:"100",value:"status",slotName:"status"},{name:"商品标题",value:"productName",slotName:"productName"},{name:"提交时间",value:"createTime",width:"150",slotName:"createTime"}]}},methods:{handleSearchList:function(){var t={pageNum:1,pageSize:20,negoStatus:"0"!=this.activeName?this.activeName:""};this.searchData=i()({},this.defaultListQuery,t)},handleResetSearch:function(){this.defaultListQuery={negoStatus:"",productSn:null},this.searchData={pageNum:1,pageSize:20,negoStatus:"0"!=this.activeName?this.activeName:""}},canRefoundBack:function(t){return-1==t.status||0==t.status||2==t.status||5==t.status},canDel:function(t){return-1==t.status||3==t.status||6==t.status},canCancel:function(t){return 1==t.status||2==t.status&&t.sellerOfferPrice>0},getSellerList:r.G,deleteGoods:function(t){var e=this;this.$confirm("您确定要删除吗？删除后不可恢复?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(r.T)(t.id).then(function(t){200==t.code&&(e.$message.success("删除成功"),e.searchData=i()({},e.searchData))})})},doCancel:function(t){var e=this;this.$confirm("您确定要取消吗？取消后不可恢复","二次确认",{closeOnClickModal:!1,confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(function(){e.replayFun(t,2,2)})},disAgreeAssessFun:function(t){var e=this;this.$confirm("您确定拒绝当前报价吗？拒绝后不可撤销","温馨提示",{closeOnClickModal:!1,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.replayFun(t,2,0)})},agreeAssessFun:function(t){var e=this;this.$confirm("您确定同意当前报价吗？确定后不可撤销","温馨提示",{closeOnClickModal:!1,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.replayFun(t,1,1)})},replayFun:function(t,e,a){var n=this;this.listLoading=!0;var s={negoId:t.id,status:e};(a||0==a)&&(s.rejectedState=a),Object(r._14)(s).then(function(t){200==t.code&&(n.searchData=i()({},n.searchData))}).finally(function(){})},handleClick:function(t){this.defaultListQuery.negoStatus=t.name;this.searchData=i()({},this.defaultListQuery,{pageNum:1,pageSize:20})},huanjiaFun:function(t){this.dicker_id=t.id,this.dialogVisibleInden=!0},counterPriceSure:function(){var t=this;this.counteroffer_price?this.counteroffer_price.length>9?this.$message.error("最多输入9位数字"):Object(r._15)({negoId:this.dicker_id,price:this.counteroffer_price}).then(function(e){t.dialogVisibleInden=!1,200==e.code&&(t.$message.success("议价回复成功！"),t.searchData=i()({},t.searchData))}):this.$message.error("请输入还价金额")},handleClose:function(){this.dialogVisibleInden=!1}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"20px","margin-bottom":"100px"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"查看全部",name:""}}),t._v(" "),a("el-tab-pane",{attrs:{label:"议价中",name:"NEGOTIATING"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"议价成功",name:"NEGOTIATION_SUCCESS"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"议价失败/取消",name:"NEGOTIATION_FAILED_OR_CANCELED"}})],1),t._v(" "),a("el-form",{attrs:{inline:!0,model:t.defaultListQuery,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入商品编号",clearable:""},model:{value:t.defaultListQuery.productSn,callback:function(e){t.$set(t.defaultListQuery,"productSn",e)},expression:"defaultListQuery.productSn"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n                    重置\n                ")]),t._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n                    查询搜索\n                ")])],1)],1)],1),t._v(" "),a("MyTable",{ref:"childRef",attrs:{listApi:t.getSellerList,operationList:t.operationList,searchObj:t.searchData},scopedSlots:t._u([{key:"goodsPic",fn:function(t){var e=t.row;return[a("img",{staticStyle:{width:"100px"},attrs:{src:e.productPic,alt:""}})]}},{key:"status",fn:function(e){var n=e.row;return[a("span",[0==n.status?a("div",[t._v("待回复")]):t._e(),t._v(" "),1==n.status?a("div",[t._v("已接受,待付款")]):t._e(),t._v(" "),8==n.status?a("div",[t._v("议价成功")]):t._e(),t._v(" "),2!=n.status||n.sellerOfferPrice?t._e():a("div",[t._v("已拒绝")]),t._v(" "),2==n.status&&n.sellerOfferPrice>0?a("div",[t._v("\n                    已还价\n                ")]):t._e(),t._v(" "),3==n.status?a("div",[t._v("已取消")]):t._e(),t._v(" "),4==n.status?a("div",[t._v("买家已支付全款")]):t._e(),t._v(" "),5==n.status?a("div",[t._v("已拉黑")]):t._e(),t._v(" "),6==n.status&&n.endOrderId?a("div",[t._v("议价成功")]):6==n.status?a("div",[t._v("已退款")]):t._e(),t._v(" "),7==n.status?a("div",[t._v("退款中")]):t._e()])]}},{key:"productName",fn:function(e){var n=e.row;return[a("div",{staticClass:"text_linTwo",attrs:{title:n.productName}},[t._v("\n                "+t._s(n.productName)+"\n            ")])]}},{key:"createTime",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(t.util.timeFormat(n.createTime)))])]}},{key:"btns",fn:function(e){var n=e.row;return[a("div",[t.canDel(n)?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.deleteGoods(n)}}},[t._v("删除")]):t._e(),t._v(" "),t.canCancel(n)?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.doCancel(n)}}},[t._v("取消")]):t._e(),t._v(" "),0==n.status?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.agreeAssessFun(n)}}},[t._v("同意")]):t._e(),t._v(" "),0==n.status?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.huanjiaFun(n)}}},[t._v("还价")]):t._e(),t._v(" "),0==n.status?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.disAgreeAssessFun(n)}}},[t._v("拒绝")]):t._e()],1)]}}])}),t._v(" "),a("el-dialog",{attrs:{title:"还价",visible:t.dialogVisibleInden,width:"500px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisibleInden=e}}},[a("div",[a("el-input",{staticClass:"editNum",attrs:{type:"number",placeholder:"请输入还价金额",clearable:""},model:{value:t.counteroffer_price,callback:function(e){t.counteroffer_price=e},expression:"counteroffer_price"}}),t._v(" "),a("div",[t._v(" 如卖家同意还价金额，不能无责取消")])],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisibleInden=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.counterPriceSure}},[t._v("确 定")])],1)])],1)},staticRenderFns:[]},u=a("VU/8")(c,o,!1,null,null,null);e.default=u.exports}});