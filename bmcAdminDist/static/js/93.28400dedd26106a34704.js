webpackJsonp([93],{ShF9:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("//Fk"),i=n.n(a),l=n("woOf"),s=n.n(l),r=n("0xDb"),u=n("mRsl"),o=n("CTAa"),c={keyword:null,pageNum:1,pageSize:20},d={components:{},data:function(){return{util:r.b,listQuery:s()({},c),list:null,total:null,listLoading:!0,gameCateList:[]}},created:function(){var t=this;i.a.all([Object(u.d)(74,{pageNum:1,pageSize:999}),Object(u.d)(73,{pageNum:1,pageSize:999})]).then(function(e){t.gameCateList=t.gameCateList.concat(e[0].data.list||[]).concat(e[1].data.list||[]),t.initList()})},methods:{onDel:function(t,e){var n=this;this.$confirm("是否要删除","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o.i)(e.id).then(function(t){n.$message({type:"success",message:"删除成功!"}),n.initList()})})},getType:function(t){return this.gameCateList.find(function(e){return e.id==t}).name},handleSearchList:function(){this.listQuery.pageNum=1,this.initList()},handleResetSearch:function(){this.listQuery=s()({},c)},handleSelectionChange:function(){},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.initList()},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},initList:function(){var t=this;Object(o.d)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})}}},g={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{type:"index",width:"60",label:"序号"}}),t._v(" "),n("el-table-column",{attrs:{label:"游戏",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(t.getType(e.row.categoryId)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"客服IM",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.csIm))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"商品编号",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.productSn))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"状态",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s("0"===e.row.status?"无效":"有效"))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"用户IM",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.userIm))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"创建时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v("\n            "+t._s(t.util.timeFormat(e.row.createTime,"YYYY-MM-DD HH:mm:ss"))+"\n          ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"最后更新时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v("\n            "+t._s(t.util.timeFormat(e.row.updateTime,"YYYY-MM-DD HH:mm:ss"))+"\n          ")])]}}])})],1)],1),t._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])},staticRenderFns:[]};var p=n("VU/8")(d,g,!1,function(t){n("xHBM")},"data-v-24228e81",null);e.default=p.exports},xHBM:function(t,e){}});