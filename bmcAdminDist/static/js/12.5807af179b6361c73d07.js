webpackJsonp([12],{"6R4F":function(t,e){},PhaL:function(t,e){},VjTl:function(t,e){},"Yga/":function(t,e){},di7P:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=a("Dd8w"),i=a.n(s),n=a("woOf"),l=a.n(n),r=a("0Dnf"),o=a("rtXg"),c={props:{data:{type:Object,default:function(){}}},data:function(){return{}},methods:{}},u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-descriptions",{attrs:{title:"任务详情"}},[a("el-descriptions-item",{attrs:{label:"任务ID"}},[t._v(t._s(t.data.id))]),t._v(" "),a("el-descriptions-item",{attrs:{label:"设备名称"}},[t._v("\n    "+t._s(t.data.deviceName))]),t._v(" "),a("el-descriptions-item",{attrs:{label:"游戏名称"}},[t._v(t._s(t.data.productCategoryName))])],1)},staticRenderFns:[]};var d=a("VU/8")(c,u,!1,function(t){a("VjTl")},"data-v-aec12150",null).exports,m={props:{data:{type:Array,default:function(){return[]}}},data:function(){return{}},computed:{srcList:function(){return this.data.slice().reverse().map(function(t){return t.snapshot})}}},h={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.data&&t.data.length?a("el-timeline",{attrs:{reverse:!0}},t._l(t.data,function(e,s){return a("el-timeline-item",{key:s,attrs:{timestamp:e.time}},[a("p",[t._v(t._s(e.msg||"-"))]),t._v(" "),a("el-image",{staticStyle:{height:"60px"},attrs:{src:e.snapshot,"preview-src-list":t.srcList}})],1)}),1):a("div",[t._v("暂无信息")])},staticRenderFns:[]};var f=a("VU/8")(m,h,!1,function(t){a("PhaL")},null,null).exports,p=a("nYtf"),v={components:{IntervalTask:p.a},props:{taskId:{type:Number},stageList:{type:Array,default:function(){return[]}}},data:function(){var t={beginStage:"",needLogin:!0};return{startTask:!1,formLabelWidth:"200px",emptyFormData:t,value:t,rules:{beginStage:[{required:!0,message:"请选择",trigger:"blur"}]}}},methods:{cancel:function(){this.$emit("cancel")},handelFinish:function(){this.startTask=!1,this.value=this.emptyFormData,this.$message.success("录号流程结束"),this.$emit("cancel")},submitForm:function(t){var e=this;this.$refs[t].validate(function(t){if(t){var a=i()({taskId:e.taskId},e.value);e.value.smsCode="",Object(o.j)(a).then(function(t){200==t.code&&(e.$message.success("重新发起成功"),e.startTask=!0)})}})}}},g={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{ref:"resumForm",staticClass:"form-box",attrs:{model:t.value,rules:t.rules,"label-width":t.formLabelWidth}},[a("el-form-item",{attrs:{label:"开始阶段",prop:"beginStage"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.value.beginStage,callback:function(e){t.$set(t.value,"beginStage",e)},expression:"value.beginStage"}},t._l(t.stageList,function(t){return a("el-option",{key:t.name,attrs:{label:t.desc,value:t.name}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"是否需要登录",prop:"needLogin"}},[a("el-radio-group",{model:{value:t.value.needLogin,callback:function(e){t.$set(t.value,"needLogin",e)},expression:"value.needLogin"}},[a("el-radio",{attrs:{label:!0}},[t._v("是")]),t._v(" "),a("el-radio",{attrs:{label:!1}},[t._v("否")])],1)],1),t._v(" "),a("div",{staticClass:"m-footer"},[a("div",{staticClass:"spaceEnd"},[a("div",[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("resumForm")}}},[t._v("确认")])],1)])])],1),t._v(" "),t.startTask?a("IntervalTask",{attrs:{taskId:t.taskId},on:{finish:t.handelFinish}}):t._e()],1)},staticRenderFns:[]};var _={pageNum:1,pageSize:20},b={components:{ProgressInfo:f,Detail:d,ResumForm:a("VU/8")(v,g,!1,function(t){a("phx6")},"data-v-8d1e5ae6",null).exports,IntervalTask:p.a},data:function(){return{listQuery:l()({},_),list:null,total:null,listLoading:!0,multipleSelection:[],LH_TASK_STATUS:r.h,LH_LOGIN_TYPE_OBJ:r.g,LH_TASK_STATUS_OBJ:r.i,showProgressInfo:!1,taskLogList:[],showDetailModal:!1,showResumModal:!1,detailData:{},stageList:[],statisticTotal:{}}},created:function(){this.getList(),this.getStatsTotal()},methods:{getStatsTotal:function(){var t=this;Object(o.g)().then(function(e){200==e.code&&(console.log(e.data),t.statisticTotal=e.data)})},getList:function(){var t=this;this.listLoading=!0;var e=i()({},this.listQuery);Object(o.l)(e).then(function(e){200==e.code&&(t.list=e.data.list,t.total=e.data.total)}).finally(function(){t.listLoading=!1})},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleSelectionChange:function(t){this.multipleSelection=t},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleResetSearch:function(){this.listQuery=l()({},_),this.getList()},handleRestart:function(t){this.getStageListAction(t.id),this.detailData=t,this.showResumModal=!0},handleShowProgressInfo:function(t){var e=this,a=t.id;this.taskLogList=[],Object(o.h)(a).then(function(t){200==t.code&&t.data&&t.data.recordLog&&(e.taskLogList=JSON.parse(t.data.recordLog)||[],e.showProgressInfo=!0)})},handelOpenDetail:function(t){this.detailData=t,this.showDetailModal=!0},handelFinish:function(){this.detailData={},this.showDetailModal=!1,this.getList()},handleEnd:function(t){var e=this,a=t.id;this.$confirm("是否确认终止？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o.k)({id:a}).then(function(t){200===t.code&&(e.$message.success("终止任务成功"),e.getList())})})},getStageListAction:function(t){var e=this;Object(o.f)(t).then(function(t){200===t.code&&(e.stageList=t.data)})}}},y={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticClass:"total-layout"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card",attrs:{header:"今日发起"}},[a("div",{staticStyle:{display:"flex"}},[a("el-statistic",{attrs:{title:"录号总量",value:t.statisticTotal.todayStartTotal}}),t._v(" "),a("el-statistic",{attrs:{title:"机器录号总量",value:t.statisticTotal.todayStartRobot}}),t._v(" "),a("el-statistic",{attrs:{title:"人工录号总量",value:t.statisticTotal.todayStartManual}})],1)])],1),t._v(" "),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card",attrs:{header:"今日完成"}},[a("div",{staticStyle:{display:"flex"}},[a("el-statistic",{attrs:{title:"录号总量",value:t.statisticTotal.todayFinishTotal}}),t._v(" "),a("el-statistic",{attrs:{title:"录号机器总量",value:t.statisticTotal.todayFinishRobot}}),t._v(" "),a("el-statistic",{attrs:{title:"录号人工总量",value:t.statisticTotal.todayFinishManual}})],1)])],1),t._v(" "),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card",attrs:{header:"当前发起"}},[a("div",{staticStyle:{display:"flex"}},[a("el-statistic",{attrs:{title:"录号总量",value:t.statisticTotal.totalInProgressTotal}}),t._v(" "),a("el-statistic",{attrs:{title:"录号机器总量",value:t.statisticTotal.totalInProgressRobot}}),t._v(" "),a("el-statistic",{attrs:{title:"录号人工总量",value:t.statisticTotal.totalInProgressManual}})],1)])],1),t._v(" "),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card",attrs:{header:"当月完成"}},[a("div",{staticStyle:{display:"flex"}},[a("el-statistic",{attrs:{title:"录号总量",value:t.statisticTotal.monthFinishTotal}}),t._v(" "),a("el-statistic",{attrs:{title:"机器录号总量",value:t.statisticTotal.monthFinishRobot}}),t._v(" "),a("el-statistic",{attrs:{title:"人工录号总量",value:t.statisticTotal.monthFinishManual}})],1)])],1)],1)],1)]),t._v(" "),a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"设备ID："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入",clearable:""},model:{value:t.listQuery.deviceId,callback:function(e){t.$set(t.listQuery,"deviceId",e)},expression:"listQuery.deviceId"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"设备名称："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入",clearable:""},model:{value:t.listQuery.deviceName,callback:function(e){t.$set(t.listQuery,"deviceName",e)},expression:"listQuery.deviceName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入",clearable:""},model:{value:t.listQuery.productSn,callback:function(e){t.$set(t.listQuery,"productSn",e)},expression:"listQuery.productSn"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"状态："}},[a("el-select",{staticStyle:{width:"248px"},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.LH_TASK_STATUS,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:t.handleSearchList}},[t._v("\n            查询搜索\n          ")]),t._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:t.handleResetSearch}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{label:"任务ID",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.id))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"设备ID",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.deviceId))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"来源",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.source))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"设备名称",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.deviceName))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品ID",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.productId))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品编号",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.productSn))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"游戏名称",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.productCategoryName))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"截图",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("el-image",{staticClass:"snapshot",attrs:{src:t.row.snapshot,"preview-src-list":[t.row.snapshot]}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"录号信息",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{display:"flex","align-items":"center",cursor:"pointer"},on:{click:function(a){return t.handleShowProgressInfo(e.row)}}},[a("i",{staticClass:"el-icon-info"}),t._v(" "),a("p",{staticClass:"text_linThree",staticStyle:{"text-align":"left","margin-left":"5px"}},[t._v("\n              "+t._s(e.row.msg||"-")+" \n            ")])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:(t.LH_TASK_STATUS_OBJ[e.row.status]||{}).color}},[t._v(t._s((t.LH_TASK_STATUS_OBJ[e.row.status]||{}).label))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"登录方式",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s((t.LH_LOGIN_TYPE_OBJ[e.row.loginType]||{}).label))]),t._v(" "),a("p",[t._v(t._s(e.row.gameAccount))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(t._f("timeFormat")(e.row.createTime)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"更新时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(t._f("timeFormat")(e.row.updateTime)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"280",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(a){return t.handleRestart(e.row)}}},[t._v("重新发起")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return t.handleEnd(e.row)}}},[t._v("终止")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.handelOpenDetail(e.row)}}},[t._v("详情")])]}}])})],1)],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("el-dialog",{attrs:{width:"40%",visible:t.showResumModal},on:{"update:visible":function(e){t.showResumModal=e}}},[a("ResumForm",{attrs:{taskId:t.detailData.id,stageList:t.stageList},on:{cancel:function(e){t.showResumModal=!1}}})],1),t._v(" "),a("el-dialog",{attrs:{title:"任务阶段",width:"50%",visible:t.showProgressInfo,noBeforeClose:""},on:{"update:visible":function(e){t.showProgressInfo=e}}},[a("div",{staticStyle:{"max-height":"600px","overflow-y":"auto"}},[a("ProgressInfo",{attrs:{data:t.taskLogList}})],1)]),t._v(" "),a("el-dialog",{attrs:{width:"60%",visible:t.showDetailModal,noBeforeClose:"",close:!1},on:{"update:visible":function(e){t.showDetailModal=e}}},["PENDING"===t.detailData.status?a("IntervalTask",{attrs:{taskId:t.detailData.id},on:{finish:t.handelFinish}}):t._e(),t._v(" "),a("Detail",{attrs:{data:t.detailData}})],1)],1)},staticRenderFns:[]},S=a("VU/8")(b,y,!1,null,null,null);e.default=S.exports},"f9/G":function(t,e,a){"use strict";var s={data:function(){return{progressLoading:!1}},mounted:function(){this.start()},methods:{start:function(){var t=this;this.$nextTick(function(){t.progressLoading=!0})},end:function(){var t=this;this.percentage=100,clearInterval(this.timeStart),setTimeout(function(){t.progressLoading=!1,t.$emit("progressEnd")},300)}}},i={render:function(){var t=this.$createElement,e=this._self._c||t;return this.progressLoading?e("div",{staticClass:"loadingModal"},[this._m(0)]):this._e()},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"loadingBox"},[e("div",{staticClass:"tit"},[this._v("\n      正在上号中，请勿关闭此页面\n    ")]),this._v(" "),e("div",[e("i",{staticClass:"el-icon-loading",staticStyle:{"font-size":"30px"}})])])}]};var n=a("VU/8")(s,i,!1,function(t){a("lkmL")},"data-v-b1cde2da",null);e.a=n.exports},lkmL:function(t,e){},nYtf:function(t,e,a){"use strict";var s=a("mvHQ"),i=a.n(s),n=a("rtXg"),l=a("f9/G"),r={props:{id:{type:[String,Number],default:""},smcCodeInputTime:{type:Number}},data:function(){return{formLabelWidth:"200px",value:{smsCode:""},rules:{smsCode:[{required:!0,message:"请输入短信验证码",trigger:"blur"}]}}},methods:{cancel:function(){this.value.smsCode="",this.$emit("cancel")},submitForm:function(t){var e=this;this.$refs[t].validate(function(t){if(t){var a={id:e.id,smsCode:e.value.smsCode};e.value.smsCode="",Object(n.i)(a).then(function(t){200==t.code&&(e.$message.success("开始录号"),e.$emit("startTask"))})}})}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.smcCodeInputTime>0?a("el-tag",{staticStyle:{margin:"0 auto",display:"block","margin-bottom":"10px"},attrs:{type:"danger"}},[t._v("\n    验证码超时或输入错误，请重输\n  ")]):t._e(),t._v(" "),a("el-form",{ref:"smsForm",staticClass:"form-box",attrs:{model:t.value,rules:t.rules,"label-width":t.formLabelWidth}},[a("el-form-item",{attrs:{label:"录号短信验证码",prop:"smsCode"}},[a("el-input",{attrs:{placeholder:"请输入短信验证码"},model:{value:t.value.smsCode,callback:function(e){t.$set(t.value,"smsCode",e)},expression:"value.smsCode"}})],1),t._v(" "),a("div",{staticClass:"m-footer"},[a("div",{staticClass:"spaceEnd"},[a("div",[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("smsForm")}}},[t._v("确认")])],1)])])],1)],1)},staticRenderFns:[]};var c=a("VU/8")(r,o,!1,function(t){a("Yga/")},"data-v-521d5f79",null).exports,u={components:{myProgress:l.a,SMS:c},props:{taskId:{type:Number}},data:function(){return{showSmsModal:!1,smcCodeInputTime:0,stateImg:"",showProgress:!1}},methods:{handleNext:function(){var t=this;Object(n.m)(this.upSmsObj).then(function(e){200==e.code&&(t.stateImg="")})},showStateImg:function(t){this.stateImg=t},startTask:function(){this.showSmsModal=!1,this.smcCodeInputTime=this.smcCodeInputTime+1},startCheckState:function(t){var e=this;this.showProgress=!0,this.stl=setInterval(function(){Object(n.h)(t).then(function(t){if(200==t.code&&t.data&&t.data.propertyBag){var a=JSON.parse(t.data.propertyBag);a.state.includes("need_show_")?(a.state=a.state+"_done",e.upSmsObj=t.data,e.upSmsObj.propertyBag=i()(a),e.showStateImg(a.state_img)):"need_sms_code"===a.state&&e.smcCodeInputTime<1?e.showSmsModal=!0:"need_sms_code2"===a.state&&e.smcCodeInputTime<2?e.showSmsModal=!0:"login_success"!==a.state&&"login_timeout"!==a.state&&"login_fail"!==a.state||(e.showProgress=!1,e.stl=clearInterval(e.stl),e.$message.success("开始录号"),e.value=e.emptyFormData,e.smcCodeInputTime=0,e.$emit("finish"))}})},5e3)}},mounted:function(){this.startCheckState(this.taskId)}},d={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.showProgress?a("my-progress",{on:{progressEnd:function(e){t.showProgress=!1}}}):t._e(),t._v(" "),t.stateImg?a("div",{staticClass:"stateImgBox"},[a("div",{staticClass:"tit"},[t._v("请根据图片引导用户进行操作")]),t._v(" "),a("el-image",{staticClass:"stateImg",attrs:{src:t.stateImg,fit:"contain"}}),t._v(" "),a("div",{staticClass:"spaceEnd"},[a("el-button",{staticClass:"btn",attrs:{type:"primary"},on:{click:t.handleNext}},[t._v("用户已完成操作")])],1)],1):t._e(),t._v(" "),t.showSmsModal?a("div",{staticClass:"stateImgBox"},[a("SMS",{attrs:{id:t.taskId,smcCodeInputTime:t.smcCodeInputTime},on:{cancel:function(e){t.showSmsModal=!1},startTask:t.startTask}})],1):t._e()],1)},staticRenderFns:[]};var m=a("VU/8")(u,d,!1,function(t){a("6R4F")},"data-v-02137a5b",null);e.a=m.exports},phx6:function(t,e){},rtXg:function(t,e,a){"use strict";e.c=function(t){return Object(s.a)({url:"/record/device/list",method:"get",params:t})},e.b=function(t){return Object(s.a)({url:"/record/device/add",method:"post",data:t})},e.e=function(t){return Object(s.a)({url:"/record/device/stop/"+t,method:"post"})},e.d=function(t){return Object(s.a)({url:"/record/device/restart/"+t,method:"post"})},e.a=function(t){return Object(s.a)({url:"/product/createLuhaoV2",method:"post",data:t})},e.h=function(t){return Object(s.a)({url:i+"/record/full_task/"+t,method:"get"})},e.m=function(t){return Object(s.a)({url:i+"/record/task/update",method:"post",data:t})},e.k=function(t){return Object(s.a)({url:i+"/record/task/cancel",method:"post",data:t})},e.i=function(t){return Object(s.a)({url:"/product/recordTask/smsCode",method:"post",data:t})},e.l=function(t){return Object(s.a)({url:"/record/task/list",method:"get",params:t})},e.j=function(t){return Object(s.a)({url:"/record/resumeTask",method:"post",data:t})},e.f=function(t){return Object(s.a)({url:"/record/task/stageList?taskId="+t,method:"get"})},e.g=function(){return Object(s.a)({url:"/record/task/stats",method:"get"})};var s=a("vLgD"),i="https://api2.kkzhw.com/mall-portal"}});