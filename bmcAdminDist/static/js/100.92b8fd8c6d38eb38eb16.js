webpackJsonp([100],{"0p0L":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=i("woOf"),a=i.n(r),n=i("RRo+"),o=i.n(n),s=i("so1O"),l=i("Lfj9"),c=i("0xDb"),u={components:{MyTable:s.a},data:function(){return{priceForm:{price:"",originPrice:"",id:""},util:c.b,priceFormDialog:!1,options:[{value:"",label:"查看全部"},{value:"ON_SHELF",label:"在售"},{value:"BOOKED",label:"已预定"},{value:"OFF_SHELF",label:"已下架"},{value:"TO_REVIEW",label:"待审核"}],searchData:{},defaultListQuery:{productSn:null,productStatus:""},operationList:[{name:"商品编号",width:"150",value:"productSn"},{name:"分类名称",width:"150",value:"productCategoryName"},{name:"商品图",value:"name",slotName:"goodsPic",width:"120"},{name:"单价",width:"100",value:"price"},{name:"底价",width:"100",value:"originalPrice"},{name:"商品状态",width:"150",value:"status",slotName:"status"},{name:"商品标题",value:"detailTitle"},{name:"发布时间",value:"createTime",width:"150",slotName:"createTime"}]}},methods:{validatePositiveInteger:function(t,e,i){if(!e)return i(new Error("请输入账号价格"));var r=Number(e);o()(r)&&r>0?i():i(new Error("请输入一个有效的正整数"))},handleSearchList:function(){this.searchData=a()({},this.defaultListQuery,{pageNum:1,pageSize:20})},handleResetSearch:function(){this.defaultListQuery={productStatus:"",productSn:null},this.searchData={pageNum:1,pageSize:20}},canRefoundBack:function(t){return-1==t.status||0==t.status||2==t.status||5==t.status},canEdit:function(t){return 0==t.deleteStatus&&9==t.stock&&-2!=t.publishStatus},canDown:function(t){return 0==t.deleteStatus&&1==t.publishStatus&&9==t.stock},canUp:function(t){return 0==t.deleteStatus&&-2==t.publishStatus&&-1==t.verifyStatus&&9==t.stock},uploadIng:function(t){return 1==t.pushType&&1==t.pushStatus},canDel:function(t){return 0==t.deleteStatus&&9==t.stock},canCancel:function(t){return 1==t.status||2==t.status&&t.sellerOfferPrice>0},getState:function(t){return 2===t.deleteStatus?"已删除":1===t.deleteStatus?"管理员删除":0===t.stock?"已出售":1===t.stock?"已预订":1===t.publishStatus?"在售":0===t.publishStatus?"已下架":0===t.verifyStatus?"待审核":2===t.verifyStatus?"审核失败":void 0},getProductList:l.E,deleteGoods:function(t){var e=this;this.$confirm("您确定要删除吗？删除后不可恢复?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(l.j)(t.id).then(function(t){200==t.code&&(e.$message.success("删除成功"),e.searchData=a()({},e.searchData))})})},delePushFun:function(t){var e=this;this.$confirm("您确定下架当前商品？下架后不可恢复","提示",{closeOnClickModal:!1,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.listLoading=!0,Object(l._7)({id:t.id}).then(function(t){e.listLoading=!1,200==t.code&&(e.$message.success("下架成功"),e.searchData=a()({},e.searchData))})})},priceFormSubmit:function(){var t=this;this.$refs.priceForm.validate(function(e){if(e){var i=t.priceForm.price,r=t.priceForm.originPrice;console.log(i,r,t.priceForm.id),Object(l._28)(t.priceForm.id,{newPrice:i,newOriginPrice:r}).then(function(e){200==e.code&&(t.$message.success("修改成功"),t.searchData=a()({},t.searchData),t.priceFormDialog=!1,t.$refs.priceForm.resetFields())})}})},updatePrice:function(t){this.priceForm.id=t.id,this.priceFormDialog=!0},handleClose:function(){this.priceFormDialog=!1},editAccUpload:function(t){var e=this;Object(l._9)({id:t.id}).then(function(t){e.listLoading=!1,200==t.code&&(e.$message.success("上架成功"),e.searchData=a()({},e.searchData))})}}},d={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{padding:"20px","margin-bottom":"100px"}},[i("div",{staticStyle:{"margin-top":"15px"}},[i("el-form",{attrs:{inline:!0,model:t.defaultListQuery,size:"small","label-width":"100px"}},[i("el-form-item",{attrs:{label:"商品编号："}},[i("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入商品编号",clearable:""},model:{value:t.defaultListQuery.productSn,callback:function(e){t.$set(t.defaultListQuery,"productSn",e)},expression:"defaultListQuery.productSn"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"商品状态："}},[i("el-select",{attrs:{placeholder:"请选择状态"},model:{value:t.defaultListQuery.productStatus,callback:function(e){t.$set(t.defaultListQuery,"productStatus",e)},expression:"defaultListQuery.productStatus"}},t._l(t.options,function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),i("el-form-item",[i("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n                    重置\n                ")]),t._v(" "),i("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n                    查询搜索\n                ")])],1)],1)],1),t._v(" "),i("MyTable",{ref:"childRef",attrs:{listApi:t.getProductList,operationList:t.operationList,searchObj:t.searchData},scopedSlots:t._u([{key:"goodsPic",fn:function(t){var e=t.row;return[i("img",{staticStyle:{width:"100px"},attrs:{src:e.pic,alt:""}})]}},{key:"status",fn:function(e){var r=e.row;return[i("span",[t._v("\n                "+t._s(t.getState(r))+"\n            ")])]}},{key:"createTime",fn:function(e){var r=e.row;return[i("span",[t._v(t._s(t.util.timeFormatDD(r.createTime)))])]}},{key:"btns",fn:function(e){var r=e.row;return[i("div",[t.canDel(r)?i("el-button",{attrs:{type:"text"},on:{click:function(e){return t.deleteGoods(r)}}},[t._v("删除商品")]):t._e(),t._v(" "),t.canEdit(r)&&!t.uploadIng(r)?i("el-button",{attrs:{type:"text"},on:{click:function(e){return t.updatePrice(r)}}},[t._v("改价")]):t._e(),t._v(" "),t.canDown(r)&&!t.uploadIng(r)?i("el-button",{attrs:{type:"text"},on:{click:function(e){return t.delePushFun(r)}}},[t._v("下架")]):t._e(),t._v(" "),t.canUp(r)&&!t.uploadIng(r)?i("el-button",{attrs:{type:"text"},on:{click:function(e){return t.editAccUpload(r)}}},[t._v("上架")]):t._e(),t._v(" "),1!==r.pushType&&(["OFF_SHELF"].includes(r.productStatus)||"ON_SHELF"===r.productStatus&&3===r.gameGoodsSaletype)?i("el-button",{attrs:{type:"text"},on:{click:function(e){return t.deleteGoods(r)}}},[t._v("编辑")]):t._e()],1)]}}])}),t._v(" "),i("el-dialog",{attrs:{title:"改价",visible:t.priceFormDialog,width:"600px","before-close":t.handleClose},on:{"update:visible":function(e){t.priceFormDialog=e}}},[i("div",[i("div",{staticStyle:{"text-align":"center","margin-bottom":"20px"}},[t._v("改价比例不能超过50%，如超过50%将重新审核")]),t._v(" "),i("el-form",{ref:"priceForm",staticClass:"demo-ruleForm",attrs:{model:t.priceForm,"label-width":"100px"}},[i("el-form-item",{attrs:{rules:[{required:!0,message:"请输入账号价格",trigger:"blur"},{validator:t.validatePositiveInteger,trigger:"blur"}],label:"账号价格",prop:"price"}},[i("el-input",{staticClass:"editNum",attrs:{type:"number",placeholder:"请输入账号价格",clearable:""},model:{value:t.priceForm.price,callback:function(e){t.$set(t.priceForm,"price",e)},expression:"priceForm.price"}})],1),t._v(" "),i("el-form-item",{attrs:{rules:[{required:!0,message:"请输入心理底价",trigger:"blur"},{validator:t.validatePositiveInteger,trigger:"blur"}],label:"心理底价",prop:"originPrice"}},[i("el-input",{staticClass:"editNum",attrs:{type:"number",placeholder:"请输入心理底价",clearable:""},model:{value:t.priceForm.originPrice,callback:function(e){t.$set(t.priceForm,"originPrice",e)},expression:"priceForm.originPrice"}})],1)],1),t._v(" "),i("div",{staticStyle:{"text-align":"center","font-size":"13px"}},[t._v("当有买家咨询最低价时系统将自动发送此价格，请谨慎填写。")])],1),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.priceFormDialog=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:t.priceFormSubmit}},[t._v("确 定")])],1)])],1)},staticRenderFns:[]},p=i("VU/8")(u,d,!1,null,null,null);e.default=p.exports}});