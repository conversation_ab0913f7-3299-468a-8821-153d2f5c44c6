webpackJsonp([82],{E4I8:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=i("//Fk"),n=i.n(a),l=i("woOf"),s=i.n(l),o=i("M9A7"),r=i("STSY"),c=i("xT6B"),d=i("TZVV"),u=i("mRsl"),m=i("ocgh"),p=0,f={pageNum:1,pageSize:20,keyword:null,type:10},h={gender:"",baopeiConfirm:"",icon:"",nickname:"",password:"",password2:"",phone:"",realNameConfirm:"",realPersionConfirm:"",status:"",username:"",vxname:""},g={name:"AdminList",components:{SingleUpload:d.a},filters:{formatDateTime:function(t){if(null==t||""===t)return"N/A";var e=new Date(t);return Object(c.a)(e,"YYYY-MM-DD HH:mm:ss")}},data:function(){return{detailOptions:[],opetionDate:[],pwd0:"",pwd1:"",listQuery:s()({},f),list:null,total:null,listLoading:!1,dialogVisible:!1,dialogVisiblePwd:!1,admin:s()({},h),isEdit:!1,allocDialogVisible:!1,allocRoleIds:[],allRoleList:[],allocAdminId:null}},watch:{$route:function(t,e){console.log(1),this.beforeInit()}},created:function(){console.log(2),this.beforeInit()},methods:{beforeInit:function(){this.admin.type=10,h.type=10,this.doInit()},clearValue:function(){this.detailOptions=[],this.admin=s()({},h)},changepwd0:function(t){this.pwd0=t},changepwd1:function(t){this.pwd1=t},doInit:function(){this.getList(),this.getAllRoleList()},handleResetSearch:function(){this.listQuery=s()({},f)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleAdd:function(){var t=this;this.getGameCate().then(function(e){t.clearValue(),t.dialogVisible=!0,t.isEdit=!1,t.admin=s()({},h)})},handleStatusChange:function(t,e){var i=this;this.$confirm("是否要修改该状态?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(m.g)(e.id,{status:e.status,id:e.id,type:e.type}).then(function(t){i.$message({type:"success",message:"修改成功!"})})}).catch(function(){i.$message({type:"info",message:"取消修改"}),i.getList()})},handleDelete:function(t,e){var i=this;this.$confirm("是否要删除该用户?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(m.d)(e.id).then(function(t){i.$message({type:"success",message:"删除成功!"}),i.getList()})})},getGameCate:function(){var t=this;return new n.a(function(e,i){n.a.all([Object(u.d)(74,{pageNum:1,pageSize:999}),Object(u.d)(73,{pageNum:1,pageSize:999})]).then(function(i){var a=[];a.push({icon:"",name:"手游",splitTitle:1,checked:!1});var n=i[0].data.list.map(function(t){return{icon:"",name:t.name,checked:!1,id:t.id}});(a=a.concat(n)).push({icon:"",name:"端游",splitTitle:1,checked:!1});var l=i[1].data.list.map(function(t){return{icon:"",name:t.name,checked:!1,id:t.id}}),s={childList:a=a.concat(l),name:"关联游戏",is_required:0,field_type:2,default_word:"点击可下拉选择",tdtype:3,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[],id:+new Date};t.opetionDate=[s],e()})})},handleUpdate:function(t,e){var i=this;this.getGameCate().then(function(t){Object(m.a)(e.id).then(function(t){i.clearValue(),i.admin=t.data;var e=i.admin.categoryList.map(function(t){return{icon:"",id:t.id,name:t.name,checked:!0}});i.detailOptions=[{title:"关联游戏",tdtype:3,value:e,id:p++}],i.dialogVisible=!0,i.isEdit=!0})})},handleUpdatePwd:function(t,e){this.dialogVisiblePwd=!0,this.pwd0="",this.pwd1="",this.admin=s()({},e)},handleDialogConfirmPwd:function(){var t=this;this.pwd0?this.pwd0===this.pwd1?this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(m.h)({},{id:t.admin.id,password:t.pwd0,pass2:t.pwd1}).then(function(e){t.$message({message:"密码修改成功",type:"success"}),t.dialogVisiblePwd=!1})}):this.$message({message:"两次密码不一致，请重新输入",type:"error"}):this.$message({message:"请输入密码",type:"error"})},handleDialogConfirm:function(){var t=this;if(!this.isEdit){if(!this.admin.password)return void this.$message({message:"请输入密码",type:"error"});if(this.admin.password!==this.admin.password2)return void this.$message({message:"两次密码不一致，请重新输入",type:"error"})}this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=t.opetionDate[0].choosedList;t.admin.categoryList=e,t.isEdit?Object(m.f)(t.admin.id,t.admin).then(function(e){t.$message({message:"修改成功！",type:"success"}),t.dialogVisible=!1,t.getList()}):Object(m.c)(t.admin).then(function(e){t.$message({message:"添加成功！",type:"success"}),t.dialogVisible=!1,t.getList()})})},handleAllocDialogConfirm:function(){var t=this;this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=new URLSearchParams;e.append("adminId",t.allocAdminId),e.append("roleIds",t.allocRoleIds),Object(o.a)(e).then(function(e){t.$message({message:"分配成功！",type:"success"}),t.allocDialogVisible=!1})})},handleSelectRole:function(t,e){this.allocAdminId=e.id,this.allocDialogVisible=!0,this.getRoleListByAdmin(e.id)},getList:function(){var t=this;this.listLoading=!0,Object(m.e)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})},getAllRoleList:function(){var t=this;Object(r.e)().then(function(e){t.allRoleList=e.data})},getRoleListByAdmin:function(t){var e=this;Object(o.f)(t).then(function(t){var i=t.data;if(e.allocRoleIds=[],null!=i&&i.length>0)for(var a=0;a<i.length;a++)e.allocRoleIds.push(i[a].id)})}}},v={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[i("div",{staticStyle:{"margin-top":"15px"}},[i("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[i("el-form-item",{attrs:{label:"输入搜索："}},[i("el-input",{staticClass:"input-width",attrs:{placeholder:"帐号/姓名",clearable:""},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}})],1),t._v(" "),i("el-form-item",[i("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询搜索\n          ")]),t._v(" "),i("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),i("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[i("i",{staticClass:"el-icon-tickets"}),t._v(" "),i("span",[t._v("数据列表")]),t._v(" "),i("el-button",{staticClass:"btn-add",staticStyle:{"margin-left":"20px"},attrs:{size:"mini"},on:{click:function(e){return t.handleAdd()}}},[t._v("添加")])],1),t._v(" "),i("div",{staticClass:"table-container"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"adminTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[i("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"帐号",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.username))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"昵称",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.nickname))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"手机号",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.phone))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"IM号",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.imaccount))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"添加时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatDateTime")(e.row.createTime)))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"是否启用",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(i){return t.handleStatusChange(e.$index,e.row)}},model:{value:e.row.status,callback:function(i){t.$set(e.row,"status",i)},expression:"scope.row.status"}})]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return t.handleUpdatePwd(e.$index,e.row)}}},[t._v("\n            修改密码\n          ")]),t._v(" "),i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return t.handleUpdate(e.$index,e.row)}}},[t._v("\n            编辑\n          ")]),t._v(" "),i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return t.handleDelete(e.$index,e.row)}}},[t._v("删除\n          ")])]}}])})],1)],1),t._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":t.listQuery.pageNum,"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),t.dialogVisible?i("el-dialog",{attrs:{title:t.isEdit?"编辑用户":"添加用户",visible:t.dialogVisible,width:"80%",top:"1vh"},on:{"update:visible":function(e){t.dialogVisible=e}}},[i("el-form",{ref:"adminForm",attrs:{model:t.admin,"label-width":"150px",size:"small"}},[i("el-form-item",{attrs:{label:"性别："}},[i("el-radio-group",{model:{value:t.admin.gender,callback:function(e){t.$set(t.admin,"gender",e)},expression:"admin.gender"}},[i("el-radio",{attrs:{label:1}},[t._v("男")]),t._v(" "),i("el-radio",{attrs:{label:0}},[t._v("女")])],1)],1),t._v(" "),i("el-form-item",{attrs:{label:"账号："}},[i("el-input",{model:{value:t.admin.username,callback:function(e){t.$set(t.admin,"username",e)},expression:"admin.username"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"昵称："}},[i("el-input",{model:{value:t.admin.nickname,callback:function(e){t.$set(t.admin,"nickname",e)},expression:"admin.nickname"}})],1),t._v(" "),t.isEdit?t._e():i("el-form-item",{attrs:{label:"密码："}},[i("el-input",{attrs:{type:"password"},model:{value:t.admin.password,callback:function(e){t.$set(t.admin,"password",e)},expression:"admin.password"}})],1),t._v(" "),t.isEdit?t._e():i("el-form-item",{attrs:{label:"重复密码："}},[i("el-input",{attrs:{type:"password"},model:{value:t.admin.password2,callback:function(e){t.$set(t.admin,"password2",e)},expression:"admin.password2"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"手机号："}},[i("el-input",{model:{value:t.admin.phone,callback:function(e){t.$set(t.admin,"phone",e)},expression:"admin.phone"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"IM号："}},[i("el-input",{model:{value:t.admin.imaccount,callback:function(e){t.$set(t.admin,"imaccount",e)},expression:"admin.imaccount"}})],1),t._v(" "),t.isEdit?t._e():i("el-form-item",{attrs:{label:"是否启用："}},[i("el-radio-group",{model:{value:t.admin.status,callback:function(e){t.$set(t.admin,"status",e)},expression:"admin.status"}},[i("el-radio",{attrs:{label:1}},[t._v("是")]),t._v(" "),i("el-radio",{attrs:{label:0}},[t._v("否")])],1)],1)],1),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleDialogConfirm()}}},[t._v("确 定")])],1)],1):t._e(),t._v(" "),i("el-dialog",{attrs:{visible:t.allocDialogVisible,title:"分配角色",width:"30%",top:"1vh"},on:{"update:visible":function(e){t.allocDialogVisible=e}}},[i("el-select",{staticStyle:{width:"80%"},attrs:{multiple:"",placeholder:"请选择",size:"small"},model:{value:t.allocRoleIds,callback:function(e){t.allocRoleIds=e},expression:"allocRoleIds"}},t._l(t.allRoleList,function(t){return i("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(e){t.allocDialogVisible=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleAllocDialogConfirm()}}},[t._v("确 定")])],1)],1),t._v(" "),i("el-dialog",{attrs:{visible:t.dialogVisiblePwd,title:"修改密码",width:"50%",top:"1vh"},on:{"update:visible":function(e){t.dialogVisiblePwd=e}}},[i("el-form",{ref:"adminForm",attrs:{model:t.admin,"label-width":"150px",size:"small"}},[i("el-form-item",{attrs:{label:"密码："}},[i("el-input",{staticStyle:{width:"250px"},attrs:{value:t.pwd0,type:"password"},on:{input:t.changepwd0}})],1),t._v(" "),i("el-form-item",{attrs:{label:"确认密码："}},[i("el-input",{staticStyle:{width:"250px"},attrs:{value:t.pwd1,type:"password"},on:{input:t.changepwd1}})],1)],1),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogVisiblePwd=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleDialogConfirmPwd()}}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var b=i("VU/8")(g,v,!1,function(t){i("VVxv")},null,null);e.default=b.exports},VVxv:function(t,e){}});