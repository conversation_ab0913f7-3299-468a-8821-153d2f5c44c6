webpackJsonp([72],{"/HFZ":function(e,t){},EkXM:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("woOf"),r=a.n(l),n=a("5rT4"),i=a("0xDb"),s=[{label:"体验问题",value:0},{label:"功能问题",value:1},{label:"BUG",value:2},{label:"游戏问题",value:3},{label:"交易流程",value:4},{label:"账号资料",value:5},{label:"订单投诉",value:6}],o={pageNum:1,pageSize:20,reportType:void 0,createTime:void 0},u={0:"体验问题",1:"功能问题",2:"BUG",3:"游戏问题",4:"交易流程",5:"账号资料",6:"订单投诉"},c={name:"",data:function(){return{util:i.b,reportTypeOptions:s,listQuery:r()({},o),list:null,total:null,listLoading:!1}},created:function(){this.getList()},filters:{formatReportType:function(e){return u[e]},formatReportType2:function(e){return 6==e?"订单投诉":"网站建议"},formatDateTime:function(e){if(null==e||""===e)return"N/A";var t=new Date(e);return formatDate(t,"YYYY-MM-DD HH:mm:ss")}},methods:{handleResetSearch:function(){this.listQuery=r()({},o)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},getList:function(){var e=this;this.listLoading=!0,Object(n.S)(this.listQuery).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})}}},p={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.listQuery.createTime,callback:function(t){e.$set(e.listQuery,"createTime",t)},expression:"listQuery.createTime"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"分类"}},[a("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.reportType,callback:function(t){e.$set(e.listQuery,"reportType",t)},expression:"listQuery.reportType"}},e._l(e.reportTypeOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询搜索\n          ")]),e._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),a("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "),a("span",[e._v("数据列表")])]),e._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"adminTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[a("el-table-column",{attrs:{label:"分类",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatReportType2")(t.row.reportType)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"反馈用户",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.username))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"内容",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.note))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"订单号",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.reportObject))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"建议类别",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatReportType")(t.row.reportType)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"反馈时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.util.timeFormat(t.row.createTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"图片",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l((t.row.pics||"").split(","),function(l,r){return t.row.pics?a("el-image",{key:r,staticStyle:{width:"50px",height:"50px"},attrs:{src:l,"preview-src-list":[l]}}):e._e()})}}])})],1)],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)}}})],1)],1)},staticRenderFns:[]};var d=a("VU/8")(c,p,!1,function(e){a("/HFZ")},null,null);t.default=d.exports}});