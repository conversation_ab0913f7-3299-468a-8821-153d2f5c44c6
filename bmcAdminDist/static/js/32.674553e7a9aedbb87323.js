webpackJsonp([32],{ATaD:function(t,e){},"h+Aa":function(t,e){},oHd1:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o={name:"addProductAttr",components:{ProductAttrDetail:r("qYud").a}},a={render:function(){var t=this.$createElement;return(this._self._c||t)("product-attr-detail",{attrs:{"is-edit":!1}})},staticRenderFns:[]};var l=r("VU/8")(o,a,!1,function(t){r("h+Aa")},"data-v-2a513176",null);e.default=l.exports},qYud:function(t,e,r){"use strict";var o=r("mvHQ"),a=r.n(o),l=r("woOf"),s=r.n(l),i=r("/ekJ"),n=r.n(i),u=(r("mERs"),r("KhLR")),c=r("3idm"),d={nameGroup:"",filterType:0,handAddStatus:0,inputList:"",inputType:0,name:"",productAttributeCategoryId:0,relatedStatus:0,searchType:0,searchSort:0,selectType:0,sort:0,type:0},p={name:"ProductAttrDetail",props:{isEdit:{type:Boolean,default:!1}},data:function(){var t=this;return{type:"",typeList:[{label:"扩展属性1",value:1},{label:"扩展属性2",value:2},{label:"扩展属性3",value:3},{label:"扩展属性4",value:4},{label:"扩展属性5",value:5},{label:"扩展属性6",value:6},{label:"扩展属性7",value:7}],productAttr:s()({},d),rules:{name:[{required:!0,message:"请输入属性名称",trigger:"blur"},{min:2,max:140,message:"长度在 2 到 140 个字符",trigger:"blur"}],ename:[{validator:function(e,r,o){t.validatorEname(e,r,o)}}]},productAttrCateList:null,inputListFormat:null}},computed:{searchSortList:function(){for(var t=[{id:0,name:"不排序"}],e=1;e<10;e++)t.push({id:e,name:"排序"+e});return t}},watch:{inputListFormat:function(t,e){3!==this.productAttr.selectType&&(t=t.replace(/\n/g,",")),this.productAttr.inputList=t},"productAttr.selectType":function(t,e){this.productAttr.inputType=0===t?0:1}},created:function(){},mounted:function(){var t=this;this.initJsonEdit(),this.initJsonEdit2(),this.type=Number(this.$route.query.type),this.isEdit?Object(c.d)(this.$route.query.id).then(function(e){t.productAttr=e.data,t.productAttr.custom&&t.setCustom2(t.productAttr.custom),3!==t.productAttr.selectType?t.inputListFormat=t.productAttr.inputList.replace(/,/g,"\n"):(t.inputListFormat=t.productAttr.inputList,t.setCustom(t.inputListFormat))}):this.resetProductAttr(),this.getCateList()},methods:{setCustom:function(t){var e=JSON.parse(t);this.editor.set(e)},setCustom2:function(t){var e=JSON.parse(t);this.editor2.set(e)},initJsonEdit:function(){var t=document.getElementById("jsoneditor");this.editor=new n.a(t,{mode:"text",indentation:2,enableSort:!1,enableTransform:!1})},initJsonEdit2:function(){var t=document.getElementById("jsoneditor2");this.editor2=new n.a(t,{mode:"text",indentation:2,enableSort:!1,enableTransform:!1})},validatorEname:function(t,e,r){if(e){/^[a-zA-Z0-9]+$/.test(e)?r():r(new Error("只能输入字母和数字"))}else r()},getCateList:function(){var t=this;Object(u.c)({pageNum:1,pageSize:200}).then(function(e){t.productAttrCateList=e.data.list})},resetProductAttr:function(){this.productAttr=s()({},d),this.productAttr.productAttributeCategoryId=Number(this.$route.query.cid),this.type=Number(this.$route.query.type)},onSubmit:function(t){var e=this;this.$refs[t].validate(function(t){if(!t)return e.$message({message:"验证失败",type:"error",duration:1e3}),!1;e.$confirm("是否提交数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){if(3===e.productAttr.selectType){var t=e.editor.get();e.productAttr.inputList=a()(t)}var r=e.editor2.get();e.productAttr.custom=a()(r),e.productAttr.type=e.type,e.isEdit?Object(c.f)(e.$route.query.id,e.productAttr).then(function(t){e.$message({message:"修改成功",type:"success",duration:1e3}),e.$router.back()}):Object(c.a)(e.productAttr).then(function(t){e.$message({message:"提交成功",type:"success",duration:1e3}),e.resetForm("productAttrFrom")})})})},resetForm:function(t){this.$refs[t].resetFields(),this.resetProductAttr()}}},m={render:function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-card",{staticClass:"form-container",attrs:{shadow:"never"}},[r("el-form",{ref:"productAttrFrom",attrs:{model:t.productAttr,rules:t.rules,"label-width":"150px"}},[r("el-form-item",{attrs:{label:"属性类型：",prop:"name"}},[r("el-select",{model:{value:t.type,callback:function(e){t.type=e},expression:"type"}},t._l(t.typeList,function(t){return r("el-option",{key:t.value,attrs:{value:t.value,label:t.label}})}),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"属性名称：",prop:"name"}},[r("el-input",{model:{value:t.productAttr.name,callback:function(e){t.$set(t.productAttr,"name",e)},expression:"productAttr.name"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"属性分组：",prop:"name"}},[r("el-input",{model:{value:t.productAttr.nameGroup,callback:function(e){t.$set(t.productAttr,"nameGroup",e)},expression:"productAttr.nameGroup"}})],1),t._v(" "),r("el-form-item",{attrs:{prop:"ename"}},[r("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n        字段名称：\n        "),r("el-popover",{attrs:{placement:"top-start",title:"（ename）搜索的时候配合sort 是32306使用",width:"200",trigger:"hover",content:"如果是32306，有ename，放入queryStrParams里搜索。如果没有 ename，还是放在属性里搜索"}},[r("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),r("el-input",{model:{value:t.productAttr.ename,callback:function(e){t.$set(t.productAttr,"ename",e)},expression:"productAttr.ename"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"商品类型："}},[r("el-select",{attrs:{placeholder:"请选择"},model:{value:t.productAttr.productAttributeCategoryId,callback:function(e){t.$set(t.productAttr,"productAttributeCategoryId",e)},expression:"productAttr.productAttributeCategoryId"}},t._l(t.productAttrCateList,function(t){return r("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),r("el-form-item",[r("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n        属性筛选方式:\n        "),r("el-popover",{attrs:{placement:"top-start",title:"(filterType)筛选条件",width:"200",trigger:"hover",content:"筛选条件根据此属性决定是多选还是单选"}},[r("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.productAttr.filterType,callback:function(e){t.$set(t.productAttr,"filterType",e)},expression:"productAttr.filterType"}},[r("el-radio",{attrs:{label:0}},[t._v("单选")]),t._v(" "),r("el-radio",{attrs:{label:1}},[t._v("多选")])],1)],1),t._v(" "),r("el-form-item",[r("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n        能否进行检索:\n        "),r("el-popover",{attrs:{placement:"top-start",title:"(searchType)检索条件",width:"200",trigger:"hover",content:"数值一般范围检索，高级检索会进入 PC 端的高级筛选"}},[r("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.productAttr.searchType,callback:function(e){t.$set(t.productAttr,"searchType",e)},expression:"productAttr.searchType"}},[r("el-radio",{attrs:{label:0}},[t._v("不需要检索")]),t._v(" "),r("el-radio",{attrs:{label:1}},[t._v("关键字检索")]),t._v(" "),r("el-radio",{attrs:{label:2}},[t._v("范围检索")]),t._v(" "),r("el-radio",{attrs:{label:3}},[t._v("高级检索")])],1)],1),t._v(" "),r("el-form-item",[r("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n        能否综合排序:\n        "),r("el-popover",{attrs:{placement:"top-start",title:"排序条件",width:"200",trigger:"hover",content:"H5 和 PC 根据此属性决定是否放入综合排序，建议 5 个以内，PC可能一行放不下太多"}},[r("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),r("el-select",{attrs:{placeholder:"请选择"},model:{value:t.productAttr.searchSort,callback:function(e){t.$set(t.productAttr,"searchSort",e)},expression:"productAttr.searchSort"}},t._l(t.searchSortList,function(t){return r("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"商品属性关联:"}},[r("el-radio-group",{model:{value:t.productAttr.relatedStatus,callback:function(e){t.$set(t.productAttr,"relatedStatus",e)},expression:"productAttr.relatedStatus"}},[r("el-radio",{attrs:{label:1}},[t._v("是")]),t._v(" "),r("el-radio",{attrs:{label:0}},[t._v("否")])],1)],1),t._v(" "),r("el-form-item",[r("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n        属性录入方式:\n        "),r("el-popover",{attrs:{placement:"top-start",title:"(selectType)属性录入方式",width:"200",trigger:"hover",content:"控制前端用户填写属性方式"}},[r("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.productAttr.selectType,callback:function(e){t.$set(t.productAttr,"selectType",e)},expression:"productAttr.selectType"}},[r("el-radio",{attrs:{label:0}},[t._v("输入框")]),t._v(" "),r("el-radio",{attrs:{label:1}},[t._v("单选")]),t._v(" "),r("el-radio",{attrs:{label:2}},[t._v("复选")]),t._v(" "),r("el-radio",{attrs:{label:3}},[t._v("级联")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"属性值的录入方式:"}},[r("el-radio-group",{attrs:{disabled:""},model:{value:t.productAttr.inputType,callback:function(e){t.$set(t.productAttr,"inputType",e)},expression:"productAttr.inputType"}},[r("el-radio",{attrs:{label:0}},[t._v("手工录入")]),t._v(" "),r("el-radio",{attrs:{label:1}},[t._v("从下面列表中选择")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"属性值可选值列表:"}},[r("div",{directives:[{name:"show",rawName:"v-show",value:3===t.productAttr.selectType,expression:"productAttr.selectType === 3"}]},[r("div",{staticStyle:{width:"500px",height:"400px"},attrs:{id:"jsoneditor"}})]),t._v(" "),r("el-input",{directives:[{name:"show",rawName:"v-show",value:3!==t.productAttr.selectType,expression:"productAttr.selectType !== 3"}],attrs:{autosize:!0,type:"textarea"},model:{value:t.inputListFormat,callback:function(e){t.inputListFormat=e},expression:"inputListFormat"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"是否客户端必填:"}},[r("el-radio-group",{model:{value:t.productAttr.handAddStatus,callback:function(e){t.$set(t.productAttr,"handAddStatus",e)},expression:"productAttr.handAddStatus"}},[r("el-radio",{attrs:{label:1}},[t._v("是")]),t._v(" "),r("el-radio",{attrs:{label:0}},[t._v("否")]),t._v(" "),r("el-radio",{attrs:{label:2}},[t._v("客户端隐藏")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"扩展字段"}},[r("div",{staticStyle:{width:"900px",height:"400px"},attrs:{id:"jsoneditor2"}})]),t._v(" "),r("el-form-item",[r("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n        排序属性：\n        "),r("el-popover",{attrs:{placement:"top-start",title:"（sort）前端排序使用",width:"200",trigger:"hover",content:"商品上传，会根据属性类型 type 和 sort 排序决定输入优先级，另外 32306 代表 H5 页面单独筛选排第一个，20000 以上的代表 PC 要显示在顶部的属性"}},[r("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),r("el-input",{model:{value:t.productAttr.sort,callback:function(e){t.$set(t.productAttr,"sort",e)},expression:"productAttr.sort"}})],1),t._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("productAttrFrom")}}},[t._v("提交")]),t._v(" "),t.isEdit?t._e():r("el-button",{on:{click:function(e){return t.resetForm("productAttrFrom")}}},[t._v("重置")])],1)],1)],1)},staticRenderFns:[]};var v=r("VU/8")(p,m,!1,function(t){r("ATaD")},"data-v-057b2bf5",null);e.a=v.exports}});