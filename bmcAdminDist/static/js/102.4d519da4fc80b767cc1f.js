webpackJsonp([102],{Lvft:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("woOf"),n=a.n(r),l=a("so1O"),i=a("Lfj9"),s=a("Sbbi"),o=a("0xDb"),u=a("1NCi"),c={components:{MyTable:l.a,orderDetail:u.a},data:function(){return{util:o.b,orderDetailDrawerFlag:!1,orderDetailDrawer:!1,orderId:"",activeName:"",options:[{value:"",label:"查看全部"},{value:"WAIT_PAY",label:"待付款"},{value:"BOOKED",label:"已预定"},{value:"REFUND",label:"已退款"},{value:"COMPLETED",label:"已完成"},{value:"CANCELED",label:"已取消"}],searchData:{},defaultListQuery:{orderSn:null,orderStatus:""},operationList:[{name:"订单编号",width:"150",value:"orderSn"},{name:"分类名称",width:"150",value:"productCategoryName"},{name:"商品图",value:"name",slotName:"goodsPic",width:"120"},{name:"订单金额",width:"100",value:"totalAmount"},{name:"实付金额",width:"100",value:"payAmount"},{name:"订单状态",width:"150",value:"status",slotName:"status"},{name:"商品标题",value:"productName",slotName:"productName"},{name:"下单时间",value:"createTime",width:"150",slotName:"createTime"}]}},methods:{handleSearchList:function(){this.searchData=n()({},this.defaultListQuery,{pageNum:1,pageSize:20})},handleClick:function(e){this.defaultListQuery.orderStatus=e.name;this.searchData=n()({},this.defaultListQuery,{pageNum:1,pageSize:20})},handleResetSearch:function(){this.defaultListQuery={orderStatus:"0"!=this.activeName?this.activeName:"",orderSn:null},this.searchData={pageNum:1,pageSize:20,orderStatus:"0"!=this.activeName?this.activeName:""},console.log(this.searchData,8989)},canRefoundBack:function(e){return-1==e.status||0==e.status||2==e.status||5==e.status},canDel:function(e){return[4,5,12].includes(e.status)},canCancel:function(e){return 1==e.status||2==e.status&&e.sellerOfferPrice>0},getStatusType:function(e){return Object(s.a)(e)},mySellerList:i.R,deleteGoods:function(e){var t=this;this.$confirm("您确定要删除吗？删除后不可恢复?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(i.i)(e.id).then(function(e){200==e.code&&(t.$message.success("删除成功"),t.searchData=n()({},t.searchData))})})},rowClick:function(e,t,a){var r=this;this.orderId=e.id,this.orderDetailDrawerFlag=!0,setTimeout(function(){r.orderDetailDrawer=!0},100),console.log(e,t,a,22222)},handleClose:function(){var e=this;this.orderDetailDrawer=!1,setTimeout(function(){e.orderDetailDrawerFlag=!1},100)}}},d={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"20px","margin-bottom":"100px"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"查看全部",name:""}}),e._v(" "),a("el-tab-pane",{attrs:{label:"待付款",name:"WAIT_PAY"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"已预定",name:"BOOKED"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"已退款",name:"REFUND"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"已完成",name:"COMPLETED"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"已取消",name:"CANCELED"}})],1),e._v(" "),a("el-form",{attrs:{inline:!0,model:e.defaultListQuery,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"订单编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入订单编号",clearable:""},model:{value:e.defaultListQuery.orderSn,callback:function(t){e.$set(e.defaultListQuery,"orderSn",t)},expression:"defaultListQuery.orderSn"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n                    重置\n                ")]),e._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n                    查询搜索\n                ")])],1)],1)],1),e._v(" "),a("MyTable",{ref:"childRef",attrs:{listApi:e.mySellerList,operationList:e.operationList,searchObj:e.searchData},scopedSlots:e._u([{key:"goodsPic",fn:function(e){var t=e.row;return[a("img",{staticStyle:{width:"100px"},attrs:{src:t.accountItem.productPic,alt:""}})]}},{key:"status",fn:function(t){var r=t.row;return[a("span",[e._v("\n                "+e._s(e.getStatusType(r.orderStatus))+"\n            ")])]}},{key:"createTime",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(e.util.timeFormatDD(r.createTime)))])]}},{key:"productName",fn:function(t){var r=t.row;return[a("div",{staticClass:"text_linTwo",attrs:{title:r.accountItem.productName}},[e._v("\n                "+e._s(r.accountItem.productName)+"\n            ")])]}},{key:"btns",fn:function(t){var r=t.row;return[a("div",[e.canDel(r)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return t.stopPropagation(),e.deleteGoods(r)}}},[e._v("删除订单")]):e._e(),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(t){return t.stopPropagation(),e.rowClick(r)}}},[e._v("详情")])],1)]}}])}),e._v(" "),e.orderDetailDrawerFlag?a("orderDetail",{attrs:{orderId:e.orderId,drawer:e.orderDetailDrawer},on:{handleClose:e.handleClose}}):e._e()],1)},staticRenderFns:[]},m=a("VU/8")(c,d,!1,null,null,null);t.default=m.exports}});