webpackJsonp([55],{L65N:function(e,t){},kEem:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=n("woOf"),a=n.n(l),i=n("0xDb"),s=n("CTAa"),o={keyword:null,pageNum:1,pageSize:20},r={components:{},data:function(){return{util:i.b,listQuery:a()({},o),list:null,list2:null,total:null,listLoading:!0,gameCateList:[]}},created:function(){this.initList()},methods:{onDel:function(e,t){var n=this;this.$confirm("是否要删除","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){memberTalkManageDelete(t.id).then(function(e){n.$message({type:"success",message:"删除成功!"}),n.initList()})})},getType:function(e){return this.gameCateList.find(function(t){return t.id==e}).name},handleSearchList:function(){this.listQuery.pageNum=1,this.initList()},handleResetSearch:function(){this.listQuery=a()({},o)},handleSelectionChange:function(){},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.initList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},initList:function(){var e=this;Object(s.h)().then(function(t){e.list=t.data}).finally(function(){e.listLoading=!1}),Object(s.g)().then(function(t){e.list2=t.data}).finally(function(){e.listLoading=!1})}}},u={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"index",width:"60",label:"序号"}}),e._v(" "),n("el-table-column",{attrs:{label:"类型",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.name))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"今日",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.today_new))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"昨日",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.yesterday_new))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"本月",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.month_new))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"总数",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.total))])]}}])})],1),e._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%","margin-top":"30px"},attrs:{data:e.list2,border:""},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"index",width:"60",label:"序号"}}),e._v(" "),n("el-table-column",{attrs:{label:"类型",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.name))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"今日",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.today_new))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"昨日",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.yesterday_new))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"本月",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.month_new))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"总数",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.total))])]}}])})],1)],1)])},staticRenderFns:[]};var c=n("VU/8")(r,u,!1,function(e){n("L65N")},"data-v-9edb77ae",null);t.default=c.exports}});