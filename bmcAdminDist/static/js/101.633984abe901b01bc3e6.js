webpackJsonp([101],{uWhi:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a("woOf"),n=a.n(r),i=a("so1O"),l=a("Lfj9"),o=a("Sbbi"),s=a("1NCi"),u=a("0xDb"),c={components:{MyTable:i.a,orderDetail:s.a},data:function(){return{util:u.b,orderDetailDrawerFlag:!1,orderDetailDrawer:!1,orderId:"",activeName:"",options:[{value:"",label:"查看全部"},{value:"WAIT_PAY",label:"待付款"},{value:"BOOKED",label:"已预定"},{value:"REFUND",label:"已退款"},{value:"COMPLETED",label:"已完成"},{value:"CANCELED",label:"已取消"}],searchData:{},defaultListQuery:{orderSn:null,orderStatus:""},operationList:[{name:"订单编号",width:"150",value:"orderSn"},{name:"分类名称",width:"150",value:"productCategoryName"},{name:"商品图",value:"name",slotName:"goodsPic",width:"120"},{name:"订单金额",width:"100",value:"totalAmount"},{name:"实付金额",width:"100",value:"payAmount"},{name:"订单状态",width:"150",value:"status",slotName:"status"},{name:"商品标题",value:"productName",slotName:"productName"},{name:"下单时间",value:"createTime",width:"150",slotName:"createTime"}]}},methods:{handleSearchList:function(){this.searchData=n()({},this.defaultListQuery,{pageNum:1,pageSize:20})},handleClick:function(t){this.defaultListQuery.orderStatus=t.name;this.searchData=n()({},this.defaultListQuery,{pageNum:1,pageSize:20})},payNowOrder:function(t){this.$router.push({path:"/BMC/payOrder?orderId="+t.id})},handleResetSearch:function(){this.defaultListQuery={orderStatus:"0"!=this.activeName?this.activeName:"",orderSn:null},this.searchData={pageNum:1,pageSize:20,orderStatus:"0"!=this.activeName?this.activeName:""}},canRefoundBack:function(t){return-1==t.status||0==t.status||2==t.status||5==t.status},canDel:function(t){return[4,5,12].includes(t.status)},canCancel:function(t){return 1==t.status||2==t.status&&t.sellerOfferPrice>0},getStatusType:function(t){return Object(o.a)(t)},myOrderList:l.Q,deleteGoods:function(t){var e=this;this.$confirm("您确定要删除吗？删除后不可恢复?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(l.i)(t.id).then(function(t){200==t.code&&(e.$message.success("删除成功"),e.searchData=n()({},e.searchData))})})},cancelOrder:function(t){var e=this;this.$confirm("您确定要取消支付？确定后不可撤销","提示",{closeOnClickModal:!1,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(l.b)({},{orderId:t}).then(function(t){200==t.code&&(e.searchData=n()({},e.searchData))})})},rowClick:function(t,e,a){var r=this;this.orderId=t.id,this.orderDetailDrawerFlag=!0,setTimeout(function(){r.orderDetailDrawer=!0},100),console.log(t,e,a,22222)},handleClose:function(){var t=this;this.orderDetailDrawer=!1,setTimeout(function(){t.orderDetailDrawerFlag=!1},100)}}},d={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"20px","margin-bottom":"100px"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"查看全部",name:""}}),t._v(" "),a("el-tab-pane",{attrs:{label:"待付款",name:"WAIT_PAY"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"已预定",name:"BOOKED"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"已退款",name:"REFUND"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"已完成",name:"COMPLETED"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"已取消",name:"CANCELED"}})],1),t._v(" "),a("el-form",{attrs:{inline:!0,model:t.defaultListQuery,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"订单编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入订单编号",clearable:""},model:{value:t.defaultListQuery.orderSn,callback:function(e){t.$set(t.defaultListQuery,"orderSn",e)},expression:"defaultListQuery.orderSn"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n                    重置\n                ")]),t._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n                    查询搜索\n                ")])],1)],1)],1),t._v(" "),a("MyTable",{ref:"childRef",attrs:{listApi:t.myOrderList,operationList:t.operationList,searchObj:t.searchData},scopedSlots:t._u([{key:"goodsPic",fn:function(t){var e=t.row;return[a("img",{staticStyle:{width:"100px"},attrs:{src:e.accountItem.productPic,alt:""}})]}},{key:"status",fn:function(e){var r=e.row;return[a("span",[t._v("\n                "+t._s(t.getStatusType(r.orderStatus))+"\n            ")])]}},{key:"createTime",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(t.util.timeFormatDD(r.createTime)))])]}},{key:"productName",fn:function(e){var r=e.row;return[a("div",{staticClass:"text_linTwo",attrs:{title:r.accountItem.productName}},[t._v("\n                "+t._s(r.accountItem.productName)+"\n            ")])]}},{key:"btns",fn:function(e){var r=e.row;return[a("div",[t.canDel(r)?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.deleteGoods(r)}}},[t._v("删除订单")]):t._e(),t._v(" "),"WAIT_PAY"==r.orderStatus?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.payNowOrder(r)}}},[t._v("立即支付")]):t._e(),t._v(" "),"WAIT_PAY"==r.orderStatus?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.cancelOrder(r.id)}}},[t._v("取消支付")]):t._e(),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(e){return e.stopPropagation(),t.rowClick(r)}}},[t._v("详情")])],1)]}}])}),t._v(" "),t.orderDetailDrawerFlag?a("orderDetail",{attrs:{orderId:t.orderId,drawer:t.orderDetailDrawer},on:{handleClose:t.handleClose}}):t._e()],1)},staticRenderFns:[]},m=a("VU/8")(c,d,!1,null,null,null);e.default=m.exports}});