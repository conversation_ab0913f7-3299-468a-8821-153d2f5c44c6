webpackJsonp([45],{"0RUd":function(t,e){},"2aLV":function(t,e,n){"use strict";e.c=function(t){return Object(a.a)({url:"/flashSession/list",method:"get",params:t})},e.d=function(t){return Object(a.a)({url:"/flashSession/selectList",method:"get",params:t})},e.f=function(t,e){return Object(a.a)({url:"/flashSession/update/status/"+t,method:"post",params:e})},e.b=function(t){return Object(a.a)({url:"/flashSession/delete/"+t,method:"post"})},e.a=function(t){return Object(a.a)({url:"/flashSession/create",method:"post",data:t})},e.e=function(t,e){return Object(a.a)({url:"/flashSession/update/"+t,method:"post",data:e})};var a=n("vLgD")},SLqX:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("2aLV"),s=n("xT6B"),o={name:"selectSessionList",data:function(){return{list:null,listLoading:!1}},created:function(){this.getList()},filters:{formatTime:function(t){if(null==t||""===t)return"N/A";var e=new Date(t);return Object(s.a)(e,"HH:mm:ss")}},methods:{handleShowRelation:function(t,e){this.$router.push({path:"/sms/flashProductRelation",query:{flashPromotionId:this.$route.query.flashPromotionId,flashPromotionSessionId:e.id}})},getList:function(){var t=this;this.listLoading=!0,Object(a.d)({flashPromotionId:this.$route.query.flashPromotionId}).then(function(e){t.listLoading=!1,t.list=e.data})}}},l={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[n("i",{staticClass:"el-icon-tickets"}),t._v(" "),n("span",[t._v("数据列表")])]),t._v(" "),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectSessionTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[n("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"秒杀时间段名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.name))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"每日开始时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatTime")(e.row.startTime)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"每日结束时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatTime")(e.row.endTime)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"商品数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productCount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(n){return t.handleShowRelation(e.$index,e.row)}}},[t._v("商品列表\n          ")])]}}])})],1)],1)],1)},staticRenderFns:[]};var r=n("VU/8")(o,l,!1,function(t){n("0RUd")},"data-v-2239d710",null);e.default=r.exports}});