webpackJsonp([41],{isPl:function(t,e){},n2Mw:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=r("woOf"),a=r.n(s),l=r("c9K3"),o=r("qtDR"),n=r("vLgD");var i=r("xT6B"),u=r("zL8q"),d={companyAddressId:null,handleMan:"admin",handleNote:null,receiveMan:"admin",receiveNote:null,returnAmount:0,status:0},c={id:null,orderId:null,companyAddressId:null,productId:null,orderSn:null,createTime:null,memberUsername:null,returnAmount:null,returnName:null,returnPhone:null,status:null,handleTime:null,productPic:null,productName:null,productBrand:null,productAttr:null,productCount:null,productPrice:null,productRealPrice:null,reason:null,description:null,proofPics:null,handleNote:null,handleMan:null,receiveMan:null,receiveTime:null,receiveNote:null},p={name:"returnApplyDetail",props:{id:{type:[String,Number],defautl:""}},components:{orderDetail:l.default},data:function(){return{id:null,orderReturnApply:a()({},c),productList:null,proofPics:null,updateStatusParam:a()({},d),companyAddressList:null,orderDetailId:"",showOrderDetail:!1}},created:function(){this.getDetail()},computed:{totalAmount:function(){return null!=this.orderReturnApply?this.orderReturnApply.productRealPrice*this.orderReturnApply.productCount:0},currentAddress:function(){var t=this.updateStatusParam.companyAddressId;if(null==this.companyAddressList)return{};for(var e=0;e<this.companyAddressList.length;e++){var r=this.companyAddressList[e];if(r.id===t)return r}return null}},filters:{formatStatus:function(t){return 0===t?"待处理":1===t?"已处理":2===t?"已完成":"已拒绝"},formatTime:function(t){if(null==t||""===t)return"N/A";var e=new Date(t);return Object(i.a)(e,"YYYY-MM-DD HH:mm:ss")},formatRegion:function(t){var e=t.province;return null!=t.city&&(e+="  "+t.city),e+="  "+t.region}},methods:{handleViewOrder:function(){this.orderDetailId=this.orderReturnApply.orderId,this.showOrderDetail=!0},getDetail:function(){var t=this;Object(o.c)(this.id).then(function(e){t.orderReturnApply=e.data,t.productList=[],t.productList.push(t.orderReturnApply),null!=t.orderReturnApply.proofPics&&(t.orderReturnApply.proofPics?t.proofPics=t.orderReturnApply.proofPics.split(","):t.proofPics=[]),t.updateStatusParam.returnAmount=t.orderReturnApply.returnAmount,1!==t.orderReturnApply.status&&2!==t.orderReturnApply.status||(t.updateStatusParam.companyAddressId=t.orderReturnApply.companyAddressId)})},getCompanyAddressList:function(){var t=this;Object(n.a)({url:"/companyAddress/list",method:"get"}).then(function(e){console.log("getCompanyAddressList()"),t.companyAddressList=e.data;for(var r=0;r<t.companyAddressList.length;r++)1===t.companyAddressList[r].receiveStatus&&0===t.orderReturnApply.status&&(t.updateStatusParam.companyAddressId=t.companyAddressList[r].id)})},handleUpdateStatus:function(t){var e=this;this.updateStatusParam.status=t,this.$confirm("是否要进行此操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=u.Loading.service({fullscreen:!0});Object(o.d)(e.id,e.updateStatusParam).then(function(t){e.$message({type:"success",message:"操作成功!",duration:1e3}),e.$emit("closeDialog")}).finally(function(){t.close()})})}}},m={render:function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"detail-container"},[r("el-card",{attrs:{shadow:"never"}},[r("span",{staticClass:"font-title-medium"},[t._v("退货商品")]),t._v(" "),r("el-table",{ref:"productTable",staticClass:"standard-margin",attrs:{border:"",data:t.productList}},[r("el-table-column",{attrs:{label:"商品图片",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("img",{staticStyle:{height:"80px"},attrs:{src:t.row.productPic}})]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"商品名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",{staticClass:"font-small"},[t._v(t._s(e.row.productName))]),r("br"),t._v(" "),r("span",{staticClass:"font-small"},[t._v("品牌："+t._s(e.row.productBrand))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"价格/编号",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",{staticClass:"font-small"},[t._v("价格：￥"+t._s(e.row.productRealPrice))]),r("br"),t._v(" "),r("span",{staticClass:"font-small"},[t._v("编号：NO."+t._s(e.row.productId))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"属性",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productAttr))]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"数量",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productCount))]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"小计",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("￥"+t._s(t.totalAmount))]}}])})],1),t._v(" "),r("div",{staticStyle:{float:"right","margin-top":"15px","margin-bottom":"15px"}},[r("span",{staticClass:"font-title-medium"},[t._v("合计：")]),t._v(" "),r("span",{staticClass:"font-title-medium color-danger"},[t._v("￥"+t._s(t.totalAmount))])])],1),t._v(" "),r("el-card",{staticClass:"standard-margin",attrs:{shadow:"never"}},[r("span",{staticClass:"font-title-medium"},[t._v("退款单信息")]),t._v(" "),r("div",{staticClass:"form-container-border"},[r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("申请人")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.returnName||"无"))])],1)],1),t._v(" "),r("div",{staticClass:"form-container-border"},[r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("退款单号")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.returnOrderSn))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("退款状态")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t._f("formatStatus")(t.orderReturnApply.status)))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"50px","line-height":"30px"},attrs:{span:6}},[t._v("订单编号\n        ")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",staticStyle:{height:"50px"},attrs:{span:18}},[t._v("\n          "+t._s(t.orderReturnApply.orderSn)+"\n          "),r("el-button",{attrs:{type:"text",size:"small"},on:{click:t.handleViewOrder}},[t._v("查看")])],1)],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("申请时间")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t._f("formatTime")(t.orderReturnApply.createTime)))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("用户账号")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.memberUsername))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("联系电话")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.returnPhone||"无"))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("退货原因")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.reason))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("问题描述")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.description||"无"))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"100px","line-height":"80px"},attrs:{span:6}},[t._v("凭证图片\n        ")]),t._v(" "),t.proofPics&&t.proofPics.length?r("el-col",{staticClass:"form-border font-small",staticStyle:{height:"100px"},attrs:{span:18}},t._l(t.proofPics,function(t){return r("img",{staticStyle:{width:"80px",height:"80px"},attrs:{src:t}})}),0):r("el-col",{staticClass:"form-border font-small",staticStyle:{height:"100px"},attrs:{span:18}},[t._v("\n          无\n        ")])],1)],1),t._v(" "),r("div",{staticClass:"form-container-border"},[r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("订单金额")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v("￥"+t._s(t.totalAmount))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"52px","line-height":"32px"},attrs:{span:6}},[t._v("确认退款金额\n        ")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",staticStyle:{height:"52px"},attrs:{span:18}},[t._v("\n          ￥\n          "),r("el-input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{size:"small",disabled:0!==t.orderReturnApply.status},model:{value:t.updateStatusParam.returnAmount,callback:function(e){t.$set(t.updateStatusParam,"returnAmount",e)},expression:"updateStatusParam.returnAmount"}})],1)],1),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:3!==t.orderReturnApply.status,expression:"orderReturnApply.status !== 3"}]})],1),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:0!==t.orderReturnApply.status,expression:"orderReturnApply.status !== 0"}],staticClass:"form-container-border"},[r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("处理人员")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.handleMan))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("处理时间")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t._f("formatTime")(t.orderReturnApply.handleTime)))])],1),t._v(" "),r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("处理备注")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.handleNote))])],1)],1),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:0===t.orderReturnApply.status,expression:"orderReturnApply.status === 0"}],staticClass:"form-container-border"},[r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"52px","line-height":"32px"},attrs:{span:6}},[t._v("处理备注")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[r("el-input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{size:"small"},model:{value:t.updateStatusParam.handleNote,callback:function(e){t.$set(t.updateStatusParam,"handleNote",e)},expression:"updateStatusParam.handleNote"}})],1)],1)],1),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:1===t.orderReturnApply.status,expression:"orderReturnApply.status === 1"}],staticClass:"form-container-border"},[r("el-row",[r("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"52px","line-height":"32px"},attrs:{span:6}},[t._v("收货备注")]),t._v(" "),r("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[r("el-input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{size:"small"},model:{value:t.updateStatusParam.receiveNote,callback:function(e){t.$set(t.updateStatusParam,"receiveNote",e)},expression:"updateStatusParam.receiveNote"}})],1)],1)],1),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:0===t.orderReturnApply.status,expression:"orderReturnApply.status === 0"}],staticStyle:{"margin-top":"15px","text-align":"center"}},[r("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleUpdateStatus(1)}}},[t._v("确认退款")]),t._v(" "),r("el-button",{attrs:{type:"danger",size:"small"},on:{click:function(e){return t.handleUpdateStatus(3)}}},[t._v("拒绝退款")])],1),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:1===t.orderReturnApply.status,expression:"orderReturnApply.status === 1"}],staticStyle:{"margin-top":"15px","text-align":"center"}},[r("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleUpdateStatus(2)}}},[t._v("完成退款")])],1)]),t._v(" "),r("el-dialog",{attrs:{modal:!1,width:"70%",visible:t.showOrderDetail},on:{"update:visible":function(e){t.showOrderDetail=e}}},[t.showOrderDetail?r("orderDetail",{attrs:{id:t.orderDetailId}}):t._e(),t._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(e){t.showOrderDetail=!1}}},[t._v("取 消")]),t._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(e){t.showOrderDetail=!1}}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var f=r("VU/8")(p,m,!1,function(t){r("isPl")},"data-v-14fb195a",null);e.default=f.exports},qtDR:function(t,e,r){"use strict";e.b=function(t){return Object(s.a)({url:"/returnApply/list",method:"get",params:t})},e.a=function(t){return Object(s.a)({url:"/returnApply/delete",method:"post",params:t})},e.d=function(t,e){return Object(s.a)({url:"/returnApply/update/status/"+t,method:"post",data:e})},e.c=function(t){return Object(s.a)({url:"/returnApply/"+t,method:"get"})};var s=r("vLgD")}});