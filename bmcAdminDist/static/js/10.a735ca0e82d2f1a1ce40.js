webpackJsonp([10],{"6Yow":function(t,e,a){t.exports=a.p+"static/img/sl_img.efce433.png"},ANBZ:function(t,e,a){"use strict";var n=a("mvHQ"),i=a.n(n),r={props:{groupName:{type:String,default:""},list:{type:Array,default:function(){return[]}}},data:function(){return{isExpand:!1,isSubExpand:!1,sholdValue:1,maxSholdValue:1,parentValue:"",attrName:""}},computed:{currentItem:function(){var t=this;return this.list.filter(function(e){return e.name===t.attrName})[0]||{}},serverOptions:function(){return this.currentItem.optionList[this.parentValue]||[]}},mounted:function(){var t=this.list[0].name;this.attrName=t},methods:{clearCurrentItem:function(t){console.log(t),this.$emit("change",[],this.currentItem)},allCurrentItem:function(t){console.log(t),this.$emit("change",this.currentItem.optionList,this.currentItem)},getValueSearchType:function(t){if(t){var e=parseInt(t.split("_")[1]);console.log(e,33332222),this.sholdValue=e}},getCurrentItemNum:function(t){var e=this.list.filter(function(e){return e.name===t})[0]||{};console.log(t,e,"dui");var a=0;return e&&e.selectValue&&e.selectValue.length&&(a=e.selectValue.length),console.log(e,a),a},isType:function(t){var e=!0;return t.forEach(function(t){2!=t.selectType&&(e=!1)}),e},getSearchType:function(t){return t[0].valueSearchType},searchTypeClick:function(t,e){this.$emit("searchTypeClick",t,"must"==e?"should_1":"must")},handleChange:function(t,e,a){console.log(a,11111),this.$emit("searchTypeClick",t,"should_"+a)},handelParentClick:function(t){this.parentValue=t,this.choose(t)},handelAttrClick:function(t){this.attrName=t},toggel:function(){this.isExpand=!this.isExpand},toggelSub:function(){this.isSubExpand=!this.isSubExpand},clearParentValue:function(){},clearValue:function(t){"服务器"!==t?(this.parentValue="",this.currentItem.selectValue&&this.currentItem.selectValue.length&&this.$emit("change",[],this.currentItem)):this.$emit("change",[this.parentValue],this.currentItem)},choose:function(t){var e=this.currentItem.selectValue||[],a=JSON.parse(i()(e)),n=this.currentItem,r=n.filterType,s=n.selectType;if("gameAccountQufu"===n.ename&&3===s&&this.parentValue!==t&&(t=this.parentValue+"|"+t),1!==r?a=e.includes(t)?[]:[t]:e.includes(t)?a=e.filter(function(e){return e!==t}):a.push(t),a.length){var o=parseInt(a.length);this.maxSholdValue=o,console.log(this.maxSholdValue,"执行了")}this.$emit("change",a,this.currentItem)}}},s={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.currentItem&&"gameAccountQufu"===t.currentItem.ename&&3===t.currentItem.selectType?[a("div",{staticClass:"spaceStart playSearch_wrap",class:t.isExpand?"expand":""},[a("div",{staticClass:"playSearch_tit"},[t._v(t._s(t.groupName))]),t._v(" "),a("div",{staticStyle:{flex:"1"}},[a("div",{staticClass:"spaceStart item-box"},[a("div",{staticClass:"spaceStart flexWrap item-list",staticStyle:{flex:"1"}},[a("div",{staticClass:"playSearch_item",class:t.parentValue?"":"active",on:{click:function(e){return e.stopPropagation(),t.clearValue.apply(null,arguments)}}},[t._v("\n              不限\n            ")]),t._v(" "),t._l(t.currentItem.pOptionList,function(e,n){return a("div",{key:n,staticClass:"playSearch_item",class:t.parentValue===e?"active":"",on:{click:function(a){return a.stopPropagation(),t.handelParentClick(e)}}},[t._v("\n              "+t._s(e)+"\n            ")])})],2),t._v(" "),t.currentItem.pOptionList.length>8?a("div",{staticClass:"more_btn",on:{click:t.toggel}},[t._v("\n            "+t._s(t.isExpand?"收起":"更多")+"\n            "),a("i",{class:t.isExpand?"el-icon-arrow-up":"el-icon-arrow-down",staticStyle:{"font-size":"12px",cursor:"pointer"}})]):t._e()])])]),t._v(" "),a("div",{staticClass:"spaceStart playSearch_wrap",class:t.isSubExpand?"expand":""},[a("div",{staticClass:"playSearch_tit"},[t._v("服务器")]),t._v(" "),a("div",{staticStyle:{flex:"1"}},[a("div",{staticClass:"spaceStart item-box"},[a("div",{staticClass:"spaceStart flexWrap item-list",staticStyle:{flex:"1"}},[a("div",{staticClass:"playSearch_item",class:t.currentItem.selectValue&&t.currentItem.selectValue.find(function(t){return t.includes("|")})?"":"active",on:{click:function(e){return e.stopPropagation(),t.clearValue("服务器")}}},[t._v("\n              不限\n            ")]),t._v(" "),t._l(t.serverOptions,function(e,n){return a("div",{key:n,staticClass:"playSearch_item",class:t.currentItem.selectValue.includes(t.parentValue+"|"+e)?"active":"",on:{click:function(a){return a.stopPropagation(),t.choose(e)}}},[t._v("\n              "+t._s(e)+"\n            ")])})],2),t._v(" "),t.serverOptions.length>8?a("div",{staticClass:"more_btn",on:{click:t.toggelSub}},[t._v("\n            "+t._s(t.isSubExpand?"收起":"更多")+"\n            "),a("i",{class:t.isSubExpand?"el-icon-arrow-up":"el-icon-arrow-down",staticStyle:{"font-size":"12px",cursor:"pointer"}})]):t._e()])])])]:a("div",{staticClass:"spaceStart playSearch_wrap",class:t.isExpand?"expand":""},[a("div",{staticClass:"playSearch_tit"},[t._v(t._s(t.groupName)+"\n      "),t.isType(t.list)?a("div",{staticClass:"searchTypeBox",on:{click:function(e){return t.searchTypeClick(t.currentItem,t.currentItem.valueSearchType)}}},["must"==t.currentItem.valueSearchType?a("span",[t._v("\n              全部都要有\n            ")]):a("span",{staticStyle:{display:"flex","align-items":"center"}},[t._v("\n              有\n                "),a("span",{on:{click:function(t){t.stopPropagation()}}},[a("el-input-number",{staticClass:"sholdNumberInput",attrs:{min:0,max:t.currentItem.maxSholdValue},on:{change:function(e){return t.handleChange(t.currentItem,t.currentItem.valueSearchType,t.currentItem.valueSearchTypeValue)}},model:{value:t.currentItem.valueSearchTypeValue,callback:function(e){t.$set(t.currentItem,"valueSearchTypeValue",e)},expression:"currentItem.valueSearchTypeValue"}})],1),t._v("\n                个就行 \n                "),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[t._v("【有0个就行】该条件不做强制命中要求，如命中则会增加搜索得分"),a("br"),t._v("【有1+个就行】该条件会强制命中要求，命中越多得分越高")]),t._v(" "),a("i",{staticClass:"el-icon-question"})])],1),t._v(" "),a("div",{on:{click:function(t){t.stopPropagation()}}},[a("span",{staticStyle:{color:"red"},on:{click:function(e){return t.clearCurrentItem(t.currentItem)}}},[t._v("清空")]),t._v(" "),a("span",{on:{click:function(e){return t.allCurrentItem(t.currentItem)}}},[t._v("全选")])])]):t._e()]),t._v(" "),a("div",{staticStyle:{flex:"1"}},[t.list.length>1?a("div",{staticClass:"spaceStart flexWrap"},t._l(t.list,function(e,n){return a("div",{key:n,staticClass:"playSearch_item_parent",class:t.attrName===e.name?"active":"",on:{click:function(a){return a.stopPropagation(),t.handelAttrClick(e.name)}}},[a("el-badge",{staticClass:"item",attrs:{hidden:!t.getCurrentItemNum(e.name),value:t.getCurrentItemNum(e.name)}},[t._v("\n          "+t._s(e.name)+"\n        ")])],1)}),0):t._e(),t._v(" "),a("div",{staticClass:"spaceStart item-box"},[a("div",{staticClass:"spaceStart flexWrap item-list",staticStyle:{flex:"1"}},[t.currentItem.hasClear?a("div",{staticClass:"playSearch_item",class:t.currentItem.selectValue&&t.currentItem.selectValue.length?"":"active",on:{click:function(e){return e.stopPropagation(),t.clearValue.apply(null,arguments)}}},[t._v("\n            不限\n          ")]):t._e(),t._v(" "),t._l(t.currentItem.optionList,function(e,n){return a("div",{key:n,staticClass:"playSearch_item",class:t.currentItem.selectValue.includes(e)?"active":"",on:{click:function(a){return a.stopPropagation(),t.choose(e)}}},[t._v("\n            "+t._s(e)+"\n          ")])})],2),t._v(" "),t.currentItem.optionList&&t.currentItem.optionList.length>8?a("div",{staticClass:"more_btn",on:{click:t.toggel}},[t._v("\n          "+t._s(t.isExpand?"收起":"更多")+"\n          "),a("i",{class:t.isExpand?"el-icon-arrow-up":"el-icon-arrow-down",staticStyle:{"font-size":"12px",cursor:"pointer"}})]):t._e()])])])],2)},staticRenderFns:[]};var o=a("VU/8")(r,s,!1,function(t){a("r5is")},"data-v-49c9d560",null);e.a=o.exports},FnAM:function(t,e,a){"use strict";var n=a("Dd8w"),i=a.n(n),r=a("gRE1"),s=a.n(r),o=a("fZjL"),c=a.n(o),l=a("oXIA"),u=a("3Q1+"),d=a("3opN"),h=a("Lfj9"),p={CONTAIN:{name:"contain",icon:"el-icon-full-screen"},ORIGINAL:{name:"original",icon:"el-icon-c-scale-to-original"}},f=Object(u.a)()?"DOMMouseScroll":"mousewheel",m={name:"ElImageViewer",props:{urlList:{type:Array,default:function(){return[]}},tableData:{type:Array,default:function(){return[]}},tableDataFlag:{type:Boolean,default:!1},productSn:{type:String,default:""},zIndex:{type:Number,default:2e3},onSwitch:{type:Function,default:function(){}},onClose:{type:Function,default:function(){}},initialIndex:{type:Number,default:0},appendToBody:{type:Boolean,default:!0},maskClosable:{type:Boolean,default:!0},gameSysinfoReadcount:{type:Number,default:0},gameSysinfoCollectcount:{type:Number,default:0},price:{type:Number,default:0},product:{type:Object,default:function(){}}},data:function(){return{kfObj:{},tabPosition:"detail",dialogVisibleDaishou:!0,scale:1,minScale:.5,maxScale:20,zoomRate:.1,index:this.initialIndex,isShow:!1,infinite:!0,loading:!1,mode:p.CONTAIN,transform:{scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1},distanceToBottom:0,targetDiv:""}},computed:{isSingle:function(){return this.urlList.length<=1},isFirst:function(){return 0===this.index},isLast:function(){return this.index===this.urlList.length-1},currentImg:function(){return this.urlList[this.index]},imgStyle:function(){var t=this.transform,e=(t.scale,t.deg),a=t.offsetX,n=t.offsetY,i=t.enableTransition,r={transform:"scale("+this.scale+") rotate("+e+"deg)",transition:i?"transform 0s":"","margin-left":a+"px","margin-top":n+"px"};return this.mode===p.CONTAIN&&(r.maxWidth=r.maxHeight="100%"),r},viewerZIndex:function(){var t=d.a.nextZIndex();return this.zIndex>t?this.zIndex:t}},watch:{index:{handler:function(t){this.reset(),this.onSwitch(t)}},currentImg:function(t){var e=this;this.$nextTick(function(t){e.$refs.img[0].complete||(e.loading=!0)})}},mounted:function(){var t=this;Object(h.w)({game:this.product.brandName,type:"咨询客服"}).then(function(e){if(200==e.code){var a=e.data||[];a.length&&(t.kfObj=_.sample(a))}}),this.deviceSupportInstall(),this.appendToBody&&document.body.appendChild(this.$el);var e=setTimeout(function(){t.product&&c()(t.product).length>0&&t.product.price<5e6&&Object(h._10)({productId:t.product.id,categoryId:t.product.productCategoryId,productPrice:t.product.price})},1e3);this.$once("hook:beforeDestroy",function(){clearTimeout(e)})},destroyed:function(){this.appendToBody&&this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},methods:{createWx:function(){var t=""+this.kfObj.wxurl,e={errorCorrectionLevel:"H",type:"image/png",quality:.3,margin:0,width:150,height:150,text:t,color:{dark:"#333333",light:"#fff"}},a=document.getElementById("QRCode_header_kfwx");QRCode.toCanvas(a,t,e,function(t){t&&this.$message.error("二维码加载失败")})},popupCustom:function(){var t=this;this.kfObj.wxurl&&this.kfObj["微信号"]?(this.dialogVisibleDaishou=!0,this.$nextTick(function(){t.createWx()})):Object(h.x)({cateId:this.product.productCategoryId,productId:this.product.id}).then(function(e){if(200==e.code){var a=e.data;if(a){var n=a,i="p2p-"+n;if(window.__xkit_store__){var r=window.__xkit_store__,s=(r.nim,r.store);Object(h.O)({cateId:t.product.productCategoryId,kfIM:n}),s.sessionStore.sessions.get(i)?s.uiStore.selectSession(i):s.sessionStore.insertSessionActive("p2p",n),t.$store.dispatch("ToggleProductCardId",t.product.id),t.$store.dispatch("ToggleIM",!0)}}else t.$store.dispatch("ToggleIM",!0);setTimeout(function(){t.hide()},300)}})},getJJPrice:function(t){var e=JSON.parse(t.priceHistory||"[]");if(e.sort(function(t,e){return t.changeTime-e.changeTime}),!e.length)return"";var a=e[0].price-e[e.length-1].price;return a<0?"":a},arraySpanMethod:function(t){var e=t.row,a=(t.column,t.rowIndex,t.columnIndex);if(2===e.selectType){if(0===a)return[1,2];if(1===a)return[0,0]}},handleWheel:function(t){t.preventDefault();var e=t.deltaY;t.currentTarget.scrollLeft+=e},handleMouseWheel:function(t){t.preventDefault(),t.deltaY<0?this.scale<this.maxScale&&(this.scale+=this.zoomRate):this.scale>this.minScale&&(this.scale-=this.zoomRate)},showCurrent:function(t){this.index=t,this.reset(),this.onSwitch(this.index),this.moveThumbToCenter(t)},moveThumbToCenter:function(t){this.scale=1;var e=this.$refs.thumbList,a=e.querySelectorAll("li")[t],n=a.offsetWidth,i=e.offsetWidth,r=a.offsetLeft,s=(e.scrollLeft,r-(i/2-n/2));e.scrollTo({left:s,behavior:"smooth"})},hide:function(){this.deviceSupportUninstall(),this.onClose()},deviceSupportInstall:function(){var t=this;this._keyDownHandler=function(e){switch(e.stopPropagation(),e.keyCode){case 27:t.hide();break;case 32:t.toggleMode();break;case 37:t.prev();break;case 38:t.handleActions("zoomIn");break;case 39:t.next();break;case 40:t.handleActions("zoomOut")}},this._mouseWheelHandler=Object(u.b)(function(e){(e.wheelDelta?e.wheelDelta:-e.detail)>0?t.handleActions("zoomIn",{zoomRate:.015,enableTransition:!1}):t.handleActions("zoomOut",{zoomRate:.015,enableTransition:!1})}),Object(l.e)(document,"keydown",this._keyDownHandler),Object(l.e)(document,f,this._mouseWheelHandler)},deviceSupportUninstall:function(){Object(l.d)(document,"keydown",this._keyDownHandler),Object(l.d)(document,f,this._mouseWheelHandler),this._keyDownHandler=null,this._mouseWheelHandler=null},handleImgLoad:function(t){this.loading=!1},handleImgError:function(t){this.loading=!1,t.target.alt="加载失败"},handleMouseDown:function(t){var e=this;if(!this.loading&&0===t.button){var a=this.transform,n=a.offsetX,i=a.offsetY,r=t.pageX,s=t.pageY;this._dragHandler=Object(u.b)(function(t){e.transform.offsetX=n+2*(t.pageX-r),e.transform.offsetY=i+2*(t.pageY-s)}),Object(l.e)(document,"mousemove",this._dragHandler),Object(l.e)(document,"mouseup",function(t){Object(l.d)(document,"mousemove",e._dragHandler)}),t.preventDefault()}},handleMaskClick:function(){this.maskClosable&&this.hide()},reset:function(){this.transform={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}},toggleMode:function(){if(!this.loading){var t=c()(p),e=(s()(p).indexOf(this.mode)+1)%t.length;this.mode=p[t[e]],this.reset()}},prev:function(){if(!this.isFirst||this.infinite){var t=this.urlList.length;this.index=(this.index-1+t)%t,this.moveThumbToCenter(this.index)}},next:function(){if(!this.isLast||this.infinite){var t=this.urlList.length;this.index=(this.index+1)%t,this.moveThumbToCenter(this.index)}},handleActions:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.loading){var a=i()({zoomRate:.2,rotateDeg:90,enableTransition:!0},e),n=a.zoomRate,r=a.rotateDeg,s=a.enableTransition,o=this.transform;switch(t){case"zoomOut":o.scale>.2&&(o.scale=parseFloat((o.scale-n).toFixed(3)));break;case"zoomIn":o.scale=parseFloat((o.scale+n).toFixed(3));break;case"clocelise":o.deg+=r;break;case"anticlocelise":o.deg-=r}o.enableTransition=s}}}},v={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("transition",{attrs:{name:"viewer-fade"}},[n("div",{ref:"el-image-viewer__wrapper",staticClass:"el-image-viewer__wrapper",style:{"z-index":t.viewerZIndex},attrs:{tabindex:"-1"},on:{wheel:t.handleMouseWheel}},[n("div",{staticClass:"el-image-viewer__mask el-image-viewer__mask2",staticStyle:{background:"rgba(0, 0, 0, 0.65)","backdrop-filter":"blur(3px)"},on:{click:function(e){return e.target!==e.currentTarget?null:t.handleMaskClick.apply(null,arguments)}}}),t._v(" "),n("span",{staticClass:"el-image-viewer__btn el-image-viewer__close",on:{click:t.hide}},[n("i",{staticClass:"el-icon-close"})]),t._v(" "),t.isSingle?t._e():[n("span",{staticClass:"el-image-viewer__btn el-image-viewer__prev",class:{"is-disabled":!t.infinite&&t.isFirst},on:{click:t.prev}},[n("i",{staticClass:"el-icon-arrow-left"})]),t._v(" "),n("span",{staticClass:"el-image-viewer__btn el-image-viewer__next",class:{"is-disabled":!t.infinite&&t.isLast},on:{click:t.next}},[n("i",{staticClass:"el-icon-arrow-right"})])],t._v(" "),t.urlList&&t.urlList.length>0?n("div",{staticClass:"el-image-viewer__btn el-image-viewer__actions",staticStyle:{background:"rgba(0, 0, 0, 0.5)",color:"#fff","font-family":"PingFang SC","font-size":"17px","font-weight":"500",position:"absolute","z-index":"99998","border-radius":"10px",height:"100px"},style:{width:t.urlList.length>6?"800px":120*t.urlList.length+"px"}},[n("div",{staticStyle:{position:"absolute",bottom:"73px","z-index":"999999"}},[t._v(t._s(t.index+1)+"/"+t._s(t.urlList.length))]),t._v(" "),n("div",{staticClass:"thumb-wrap"},[t.urlList.length>6?n("span",{staticStyle:{"font-size":"26px","margin-right":"10px",color:"#fff",position:"relative","z-index":"999999"},on:{click:t.prev}},[n("i",{staticClass:"el-icon-arrow-left"})]):t._e(),t._v(" "),n("ul",{ref:"thumbList",on:{wheel:function(e){return e.stopPropagation(),t.handleWheel.apply(null,arguments)}}},t._l(t.urlList,function(e,a){return n("li",{key:e,class:{active:t.index===a},on:{click:function(e){return t.showCurrent(a)}}},[n("img",{attrs:{src:e}})])}),0),t._v(" "),t.urlList.length>6?n("span",{staticStyle:{"font-size":"26px","margin-left":"10px",color:"#fff"},on:{click:t.next}},[n("i",{staticClass:"el-icon-arrow-right"})]):t._e()])]):t._e(),t._v(" "),n("div",{ref:"targetDiv",staticClass:"el-image-viewer__canvas img-list-wrap el-image-viewer__canvas_table"},[0==t.index&&t.tableDataFlag?n("div",{staticClass:"priviewTableStyle",on:{wheel:function(t){t.stopPropagation()}}},[n("div",{staticClass:"topTips_tit topTips_tit_play"},[n("div",{staticClass:"spaceBetween",staticStyle:{"margin-top":"20px"}},[n("div",{staticClass:"spaceStart"},[n("div",[t._v("商品编号｜"+t._s(t.productSn))]),t._v(" "),n("div",{staticClass:"spaceBetween topGame_price",staticStyle:{"margin-left":"20px"}},[n("div",{staticClass:"spaceStart",staticStyle:{"font-family":"'PingFang SC'",color:"#ff720c"}},[t._v("\n                  ¥ "+t._s(t.price)+"\n                  "),t.product&&t.getJJPrice(t.product)?n("div",{staticClass:"priviewJjPrice"},[n("img",{staticStyle:{width:"18px","margin-left":"-3px"},attrs:{src:a("OyM5"),alt:""}}),t._v("已降价¥ "+t._s(t.getJJPrice(t.product))+"\n                          ")]):t._e()])])]),t._v(" "),n("div",[n("a",{staticStyle:{"font-size":"14px",color:"#ff720c"},attrs:{href:"https://www.yokoye.com/gd/"+t.productSn,target:"_blank"}},[t._v("查看商品")])])])]),t._v(" "),t.tableData.length>1?n("el-table",{staticClass:"priviewTableBk",staticStyle:{"margin-top":"10px","border-radius":"5px",padding:"0px 0px 0px 20px","margin-right":"20px"},attrs:{"max-height":"550",data:t.tableData,"span-method":t.arraySpanMethod}},[n("el-table-column",{attrs:{prop:"name",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{domProps:{innerHTML:t._s(e.row.name)}})]}}],null,!1,4213795588)}),t._v(" "),n("el-table-column",{attrs:{prop:"value",width:"840"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{domProps:{innerHTML:t._s(2==e.row.selectType?e.row.name:e.row.value)}})]}}],null,!1,3209636136)})],1):t._e()],1):t._e(),t._v(" "),t._l(t.urlList,function(e,a){return(t.tableDataFlag?a===t.index&&t.index>0:a===t.index)?n("img",{key:e,ref:"img",refInFor:!0,staticClass:"el-image-viewer__img",staticStyle:{"border-radius":"12px"},style:t.imgStyle,attrs:{src:t.currentImg},on:{load:t.handleImgLoad,error:t.handleImgError,mousedown:t.handleMouseDown}}):t._e()})],2)],2)])},staticRenderFns:[]};var g=a("VU/8")(m,v,!1,function(t){a("Hyge"),a("JlRY")},"data-v-1ce4145e",null);e.a=g.exports},GJdt:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("lHA8"),i=a.n(n),r=a("Dd8w"),s=a.n(r),o=a("mvHQ"),c=a.n(o),l=a("d7EF"),u=a.n(l),d=a("Gu7T"),h=a.n(d),p=a("bOdI"),f=a.n(p),m=a("mRsl"),v=a("FnAM"),g=a("Joqx"),y=a("ANBZ"),x=a("zBuL"),S=(a("6Yow"),a("/dO2")),b=a("PJh5"),w=a.n(b),C=a("0Dnf"),k=a("Lfj9"),T={components:{CheckBoxList:y.a,InputNumber:x.a,swperImagePriview:v.a,swperImagePriviewPage:g.a},name:"ProductCateList",filters:{levelFilter:function(t){return 0===t?"一级":1===t?"二级":void 0},disableNextLevel:function(t){return 0!==t}},data:function(){var t;return t={excludeBmc:"",startTimeNumber:"",startTimeUnit:"小时",startTimeUnitList:["分钟","小时","天"],queryOrderSortParams:[],startTime:"",endTime:"",tableData:[],productObj:{},productSn:"",showViewer:!1,imgViewer:0,arrDtPicForShow:[],gameSysinfoReadcount:0,gameSysinfoCollectcount:0,price:0},f()(t,"gameSysinfoReadcount",""),f()(t,"gameSysinfoCollectcount",""),f()(t,"comprehensiveData",[]),f()(t,"cateList",[]),f()(t,"sortableInstance",null),f()(t,"list",null),f()(t,"total",null),f()(t,"listLoading",!0),f()(t,"listQuery",{pageNum:1,pageSize:20}),f()(t,"searchDrawer",!1),f()(t,"pmsSearchTags",[]),f()(t,"keyword",""),f()(t,"keyword2",""),f()(t,"checkedTags",[]),f()(t,"productCategoryId",""),f()(t,"parentId",74),f()(t,"attributeData",[]),f()(t,"checkBoxAttributeList",[]),f()(t,"harborConfigList",[]),f()(t,"searchParam",{productCategoryId:this.$route.query.id,pageNum:1,pageSize:20}),f()(t,"optsSearchResult",[]),f()(t,"inputAttributeList",[]),t},watch:{$route:function(t){this.resetParentId()}},created:function(){this.productCategoryId=this.$route.query.id,this.doInit(),this.resetParentId(),this.getTagList()},computed:{newCheckBoxAttrGroup:function(){return console.log(this.checkBoxAttrGroup,11111111),this.checkBoxAttrGroup}},mounted:function(){},methods:{getWzText:function(t,e,a){var n=this;if(82!=a.productCategoryId)return e;var i=[].concat(h()(t)).map(function(t){return{name:t.name||t.productAttributeName,values:t.values||t.value.split(",")}}),r=C.c.concat(C.d);return i.filter(function(t){if(r.map(function(t){return t.label}).includes(t.name)&&t.values&&t.values.length&&"0"!==t.values[0])return n.$set(t,"index",r.find(function(e){return e.label===t.name}).index),t}).sort(function(t,e){return t.index-e.index}).map(function(t){return("贵族等级"===t.name?t.values[0].replace("贵族","V"):C.d.map(function(t){return t.label}).includes(t.name)?t.values.length:t.values[0])+r.find(function(e){return e.label===t.name}).children.label}).join(" ")+e},initDragSort:function(){var t=this,e=document.querySelectorAll(".sortItemBox")[0];this.sortableInstance=S.a.create(e,{onEnd:function(e){var a=e.oldIndex,n=e.newIndex,i=[].concat(h()(t.comprehensiveData)),r=i.splice(a,1),s=u()(r,1)[0];i.splice(n,0,s),t.comprehensiveData=JSON.parse(c()(i)),t.$forceUpdate()}})},sortChos:function(t){var e=this;this.comprehensiveData.forEach(function(a,n){if(a.sortName===t.sortName){var i=""===t.sort?"desc":"desc"===t.sort?"asc":"";e.$set(a,"sort",i)}}),this.$forceUpdate()},formatClasData:function(){var t=this;if(this.comprehensiveData&&this.comprehensiveData.length){var e=[];e.push({sortName:"上架时间",sort:"",order:"publishTime",sortId:0}),e.push({sortName:"价格",sort:"",order:"price",sortId:1});var a=2;return this.sortCateList.forEach(function(t){var n=_.cloneDeep(t);if(n.searchSort){var i={sortName:""+n.name,sort:"",order:n.searchSort,sortId:a};e.push(i),a++}}),void e.forEach(function(e){if(!t.comprehensiveData.some(function(t){return t.name===e.name})){var a=Date.now()+Math.floor(1e4*Math.random());t.comprehensiveData.push(s()({},e,{sortId:a}))}})}this.comprehensiveData=[],this.comprehensiveData.push({sortName:"上架时间",sort:"",order:"publishTime",sortId:0}),this.comprehensiveData.push({sortName:"价格",sort:"",order:"price",sortId:1});var n=2;this.sortCateList.forEach(function(e){var a=_.cloneDeep(e);if(a.searchSort){var i={sortName:""+a.name,sort:"",order:a.searchSort,sortId:n};t.comprehensiveData.push(i),n++}})},showImagePriview:function(t){this.$refs.swperImagePriviewPage.showImagePriview(t)},doInit:function(){var t=this;this.loadCateList(this.productCategoryId).then(function(){t.searchByCate()})},handleCheckedTagsChange:function(t){var e=this;if(!t.length)return this.searchConfigList=[],this.harborConfigList=[],void this.cleanAllChoose();var a=[],n=[];t.forEach(function(t){var i=e.pmsSearchTags.find(function(e){return e.tagName===t});i&&(1==i.isUnique?a.push(t):n.push(t))}),a.length>1&&(a=[a[a.length-1]]),this.checkedTags=[].concat(h()(a),n),this.searchConfigList=[];var r=JSON.parse(c()(this.pmsSearchTags));r.sort(function(t,a){return e.checkedTags.indexOf(t.tagName)-e.checkedTags.indexOf(a.tagName)}),console.log(r,"执行了"),r.forEach(function(t){console.log(t);var a=JSON.parse(t.searchConditions);e.checkedTags.includes(t.tagName)&&(e.excludeBmc=a.excludeBmc,e.startTimeNumber=a.startTimeNumber,e.startTimeUnit=a.startTimeUnit,e.queryOrderSortParams=a.queryOrderSortParams,e.comprehensiveData=a.queryOrderSortParams,e.startTime=a.startTime,e.endTime=a.endTime,0==e.searchConfigList.length?(e.searchConfigList=a.attrValueList,e.keyword=a.keyword||""):a.attrValueList.forEach(function(t){var a=e.searchConfigList.find(function(e){return e.name===t.name});a?t.valueSearchType&&"must"!==t.valueSearchType?a.selectValue=[].concat(h()(new i.a([].concat(h()(a.selectValue),h()(t.selectValue))))):a.selectValue=t.selectValue:e.searchConfigList.push(t)}))}),this.harborConfigList=[];var s=JSON.parse(c()(this.attributeData)),o=s.some(function(t){return"价格"===t.name}),l=this.searchConfigList.find(function(t){return"价格"===t.name});!o&&l&&s.unshift({name:"价格",inputType:0,selectValue:l.selectValue}),s.forEach(function(t){var a=e.searchConfigList.find(function(e){return e.name===t.name}),n=e.searchConfigList.filter(function(t){return!s.some(function(e){return e.name===t.name})});a?(t.selectValue=a.selectValue,t.valueSearchType=a.valueSearchType):t.selectValue=[],n.length&&(e.harborConfigList=n)}),console.log(s,11111),this.getSeachConfig(s),this.searchByCate()},getTagList:function(){var t=this;Object(k.F)(this.productCategoryId,this.listQuery).then(function(e){e.data.list&&e.data.list.length&&(t.pmsSearchTags=e.data.list.sort(function(t,e){return t.sort-e.sort}))})},getSeachConfig:function(t){console.log(t,222222),this.attributeData=JSON.parse(c()(t));t.filter(function(t){return 0!==t.type&&0!==t.searchType}).sort(function(t,e){var a=JSON.parse(t.custom||"{}"),n=JSON.parse(e.custom||"{}");return(n&&n.searchSortWeight||0)-(a&&a.searchSortWeight||0)});var e=t.filter(function(t){return 0!==t.type&&0!==t.searchType&&!1!==JSON.parse(t.custom||"{}").showSearch}).sort(function(t,e){return e.sort-t.sort}),a=[];this.checkBoxAttributeList=e.filter(function(t){return 0!==t.inputType}).map(function(t){var e=t.inputList,n=t.selectType,i=t.name,r=t.searchType,o=t.nameGroup,c=t.custom,l=t.selectValue,u=o||i;a.push(u);var d=e.split(","),h=null;if(3===n){d={};var p=JSON.parse(e);h=p.map(function(t){return t.parent_name}),p.forEach(function(t){var e=t.parent_name,a=t.childList;d[e]=a})}var f=[];if("账号专区"===i&&(f=["在售专区"]),"商品类型"===i){var m=new URLSearchParams(window.location.search);if(m.has("goodsType"))f=d.includes(m.get("goodsType"))?[m.get("goodsType")]:[];else if(c&&"{}"!==c){var v=JSON.parse(c);f=d.includes(v.sdefault)?[v.sdefault]:[]}else f=[d[0]]}return s()({},t,{selectValue:l||f,childValue:{},hasClear:"商品类型"!=i&&3!==r,optionList:d,pOptionList:h,defaultValue:f,nameGroup:u,valueSearchType:t.valueSearchType||"must"})}),console.log("checkBoxAttrGroup",[].concat(h()(new i.a(a))),this.checkBoxAttributeList),this.checkBoxAttrGroup=[].concat(h()(new i.a(a)));var n=e.filter(function(t){return 0===t.inputType&&"价格"!=t.name}).map(function(t){return s()({},t,{selectValue:t.selectValue||[],defaultValue:[]})});this.inputAttributeList=[{name:"价格",selectType:0,inputType:0,inputList:"",sort:0,filterType:0,searchType:2,type:1,searchSort:0,selectValue:this.getTagDetailSelectValue("价格",t)||[],defaultValue:[]}].concat(n);var r=[];return t.forEach(function(t){0!==t.type&&0!==t.searchType&&(t.moreTxt="展开全部",r.push(t))}),this.sortCateList=r,this.formatClasData(),!0},getTagDetailSelectValue:function(t,e){var a=[];return e.forEach(function(e){e.name==t&&(a=e.selectValue)}),a},loadCateList:function(){var t=this;return Object(k.B)(this.productCategoryId).then(function(e){if(200==e.code)return t.getSeachConfig(e.data)})},resetPage:function(){this.searchParam.pageNum=1,this.totalPage=0},searchByCate:function(t,e){var a=this;this.loadFlag=!1,1!=t&&this.resetPage(),"shaixuan"==e&&this.checkInputSearch(),console.log(this.checkBoxAttributeList,2222222);var n=JSON.parse(c()(this.checkBoxAttributeList));if(this.harborConfigList.length){var i=this.harborConfigList.filter(function(t){return 0!=t.inputType});n=n.concat(i)}var r=n.map(function(t){return s()({},t,{value:t.selectValue.join(",")})}),o=void 0,l=JSON.parse(c()(this.inputAttributeList));if(this.harborConfigList.length){var d=this.harborConfigList.filter(function(t){return 0==t.inputType});l=l.concat(d)}var h=l.map(function(t){var e=t.selectValue||[],a=u()(e,2),n=a[0],i=a[1];return"价格"!==t.name||isNaN(n)&&isNaN(i)||(o=[{min:isNaN(n)?void 0:parseInt(n,10),key:"price",max:isNaN(i)?void 0:parseInt(i,10)}]),s()({},t,{value:isNaN(n)&&isNaN(i)?"":(n||0)+"-"+(i||9999999)})}).filter(function(t){return"价格"!==t.name}),p=[],f=r.findIndex(function(t){return 32306===t.sort&&t.ename});if(-1!==f){var m=r[f],v=m.value,g=m.ename;v&&p.push({key:g,value:v}),r.splice(f,1)}this.listLoading=!0;var y=s()({},this.searchParam),x={attrValueList:r.concat(h),keyword:this.keyword,startTime:this.startTime?w()(this.startTime).format("YYYY-MM-DD HH:mm:ss"):"",endTime:this.endTime?w()(this.endTime).format("YYYY-MM-DD HH:mm:ss"):"",sort:this.comprehensiveDataSort,order:this.comprehensiveDataOrder,queryStrParams:p,queryIntParams:o,excludeBmc:this.excludeBmc,startTimeNumber:this.startTimeNumber,startTimeUnit:this.startTimeUnit,queryOrderSortParams:this.comprehensiveData};Object(k._13)(y,x).then(function(t){if(a.listLoading=!1,200==t.code){var e=t.data.list||[];a.total=t.data.total,a.list=e,a.list.forEach(function(t){var e=t.attrValueList.find(function(t){return"已使用天赏石"===t.name}),a=t.attrValueList.find(function(t){return"未使用天赏石"===t.name}),n=t.attrValueList.find(function(t){return"账号专区"===t.name}),i=t.attrValueList.find(function(t){return"账号类型"===t.name}),r=t.attrValueList.find(function(t){return"职业"===t.name}),s=t.attrValueList.find(function(t){return"稀有外观"===t.name}),o=t.attrValueList.find(function(t){return"天赏祥瑞"===t.name}),c=t.attrValueList.find(function(t){return"天赏发型"===t.name}),l=t.attrValueList.find(function(t){return"灵韵数量"===t.name}),u=t.attrValueList.find(function(t){return"天霓染"===t.name}),d=t.attrValueList.find(function(t){return"已使用女娲石数量"===t.name}),h=t.attrValueList.find(function(t){return"未使用女娲石数量"===t.name});t.tssnum=0,t.tssnum=0,t.topAccount="",t.accountType="",t.careers="",t.appearance="",t.auspiciousSymbol="",t.hairstyle="",t.aura="",t.tianniRan="",t.zxtssnum=0,d&&-1!==d.intValue&&(t.zxtssnum=d.intValue),h&&-1!==h.intValue&&(-1===h.intValue&&(h.intValue=0),-1===t.zxtssnum&&(t.zxtssnum=0),t.zxtssnum=t.zxtssnum+h.intValue),e&&(t.tssnum=e.intValue),a&&(t.tssnum=t.tssnum+a.intValue),n&&(t.topAccount=n.value),i&&(t.accountType=i.value),r&&(t.careers=r.value),s&&(t.appearance=s.value),o&&(t.auspiciousSymbol=o.value),c&&(t.hairstyle=c.value),l&&(t.aura=l.value),u&&(t.tianniRan=u.value)}),setTimeout(function(){a.loadFlag=!0},500)}})},checkSn:function(t){return!1},deleteIndex:function(){var t=this;this.$confirm("是否要删除所有索引","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(m.b)().then(function(e){200===e.code&&t.$message.success("删除索引成功")})})},cleanAllChoose:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.searchConfigList=[],this.harborConfigList=[],this.checkedTags=[],this.keyword="",this.comprehensiveDataOrder="",this.comprehensiveDataSort="",this.comprehensiveData.forEach(function(t,e){t.selected="",t.sort=""}),this.inputAttributeList=this.inputAttributeList.map(function(t){return s()({},t,{selectValue:t.defaultValue})}),this.checkBoxAttributeList=this.checkBoxAttributeList.map(function(t){return s()({},t,{selectValue:t.defaultValue})}),this.$forceUpdate(),this.serverDateSub=[],t&&this.searchByCate()},handleSearchProductNext:function(){var t=this;this.searchDrawer=!0,setTimeout(function(){console.log(t.comprehensiveData,111222),t.initDragSort()})},refreshIndex:function(t,e){var a=this;Object(m.g)({id:e.id}).then(function(t){200==t.code&&a.$message.success("刷新索引成功")})},resetParentId:function(){this.listQuery.pageNum=1,this.parentId=74},handleAddProductCate:function(){this.$router.push("/pms/addProductCate")},handleSizeChange:function(t){console.log(t,11122233),this.searchParam.pageSize=t,this.searchByCate()},handleCurrentChange:function(t){this.searchParam.pageNum=t,this.searchByCate(1)},handleNavStatusChange:function(t,e){var a=this,n=new URLSearchParams,i=[];i.push(e.id),n.append("ids",i),n.append("navStatus",e.navStatus),Object(m.h)(n).then(function(t){200==t.code&&a.$message({message:"修改成功",type:"success",duration:1e3})})},handleShowStatusChange:function(t,e){var a=this,n=new URLSearchParams,i=[];i.push(e.id),n.append("ids",i),n.append("showStatus",e.showStatus),Object(m.j)(n).then(function(t){200==t.code&&a.$message({message:"修改成功",type:"success",duration:1e3})})},handleXsProductNext:function(t,e){var a=this,n={productSn:e.productSn,status:"PENDING",type:"RECYCLED",subTitle:this.getWzText(e.attrValueList,e.name,e)};Object(k._1)(n).then(function(t){200===t.code&&a.$message.success("添加成功")})},handleShowNextLevel:function(t,e){this.$router.push({path:"/pms/productAttrTypeList",query:{cid:e.attriCateId}})},handleSearchConfigNext:function(t,e){this.$router.push({path:"/BMC-HSZX/searchConfig",query:{id:e.id}})},handleTransferProduct:function(t,e){console.log("handleAddProductCate")},handleUpdate:function(t,e){this.$router.push({path:"/pms/updateProductCateList",query:{id:e.id}})},handleDelete:function(t,e){var a=this;this.$confirm("是否要删除该品牌","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(m.c)(e.id).then(function(t){200==t.code&&(a.$message({message:"删除成功",type:"success",duration:1e3}),a.getList())})})},handelOptsSearch:function(t){var e=[];t&&this.checkBoxAttributeList.forEach(function(a){if(3===a.selectType){var n=function(n){a.optionList[n].forEach(function(i){i.includes(t)&&e.push({name:a.name,parent:n,value:i})})};for(var i in a.optionList)n(i)}else e=e.concat(a.optionList.filter(function(e){return e.includes(t)}).map(function(t){return{name:a.name,value:t}}))}),this.optsSearchResult=e},handelInputAttrChange:function(t,e){var a=!1,n="";this.checkBoxAttributeList.concat(this.inputAttributeList).forEach(function(t){t.selectValue&&t.selectValue.length&&t.selectValue.some(function(t){return""!==t})&&(a=!0,n=t.name)}),0==this.isUniqueFlag&&a&&n!==this.inputAttributeList[t].name?this.$message.error("不是唯一只能选择一个属性"):this.$set(this.inputAttributeList[t],"selectValue",e)},handelCheckboxAttrChange:function(t,e){var a=e.name,n=!1,i="";if(this.checkBoxAttributeList.concat(this.inputAttributeList).forEach(function(t){t.selectValue&&t.selectValue.length&&t.selectValue.some(function(t){return""!==t})&&(n=!0,i=t.name)}),0==this.isUniqueFlag&&n&&i!==a)this.$message.error("不是唯一只能选择一个属性");else{var r=this.checkBoxAttributeList.findIndex(function(t){return t.name===a});this.$set(this.checkBoxAttributeList[r],"selectValue",t)}},searchTypeClick:function(t,e){var a=this.checkBoxAttributeList.findIndex(function(e){return e.name===t.name});this.$set(this.checkBoxAttributeList[a],"valueSearchType",e)},selectValueList:function(){console.log(1111);var t=[];return this.checkBoxAttributeList.forEach(function(e){var a=e.selectValue.map(function(t){return{name:e.name,value:t,parent:"gameAccountQufu"===e.ename&&3===e.selectType?t.split("|")[0]:""}});t=t.concat(a)}),t},selectNumValueList:function(){var t=[];return this.inputAttributeList.forEach(function(e){var a=JSON.parse(c()(e.selectValue));if(console.log(a,8888),a.length&&!a.every(function(t){return""===t})){""!==a[0]&&null!==a[0]||(a[0]=0),""!==a[1]&&null!==a[1]||(a[1]=9999999);var n={name:e.name,value:e.name+a.join("-")};t=t.concat(n)}}),t},getOptIsCheck:function(t){var e=t.name,a=t.ename,n=t.selectType,i=t.parent,r=t.value;return"gameAccountQufu"===a&&3===n&&(r=i+"|"+r),this.selectValueList().find(function(t){return t.value===r&&t.name===e})},handelOptClick:function(t){var e=t.name,a=t.parent,n=t.value,i=t.selectType,r=t.ename,s=this.checkBoxAttributeList.findIndex(function(t){return t.name===e}),o=this.checkBoxAttributeList[s]||{},c=o.selectValue,l=o.filterType,u=c;"gameAccountQufu"!==r||3!==i||n.includes("|")||(n=a+"|"+n),1!==l?u=c.includes(n)?[]:[n]:c.includes(n)?u=c.filter(function(t){return t!==n}):u.push(n),this.$set(this.checkBoxAttributeList[s],"selectValue",u),this.$forceUpdate()},submitForm:function(){this.searchDrawer=!1,this.searchByCate()}}},I={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticStyle:{width:"100%",display:"flex"}},[t.pmsSearchTags.length?a("div",{staticClass:"spaceStart",staticStyle:{flex:"1"}},[a("div",{staticClass:"playSearch_tit"},[t._v("快捷筛选：")]),t._v(" "),a("div",[a("el-checkbox-group",{on:{change:t.handleCheckedTagsChange},model:{value:t.checkedTags,callback:function(e){t.checkedTags=e},expression:"checkedTags"}},t._l(t.pmsSearchTags,function(e){return a("el-checkbox",{key:e.tagName,attrs:{label:e.tagName}},[t._v(t._s(e.tagName))])}),1)],1)]):t._e(),t._v(" "),a("div",{staticStyle:{width:"100px"}},[a("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.handleSearchProductNext()}}},[t._v("\n        高级搜索\n      ")])],1)]),t._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productCateTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[a("el-table-column",{attrs:{label:"商品编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productSn))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品图",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"goodsItem_pic_img"},[a("img",{staticStyle:{width:"80px",height:"80px","border-radius":"12px"},attrs:{src:e.row.pic,alt:""}}),t._v(" "),a("div",{staticClass:"goodsItem_pic_img_box",on:{click:function(a){return a.stopPropagation(),t.showImagePriview(e.row)}}},[t._v("预览")])])]}}])}),t._v(" "),a("el-table-column",{attrs:{width:"100",label:"商品价格",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{color:"red"}},[t._v("¥"+t._s(e.row.price))])]}}])}),t._v(" "),a("el-table-column",{attrs:{width:"170",label:"上架时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[t._v("\n            "+t._s(t._f("formatTimetoSS")(e.row.publishTime))+"\n          ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tooltip",{staticClass:"tooltipItemBox",attrs:{effect:"light",content:t.getWzText(e.row.attrValueList,e.row.name,e.row),placement:"top","popper-class":"custom-tooltip-width"}},[a("div",{staticClass:"text_linThree custom-tooltip-text-label"},[t._v(t._s(t.getWzText(e.row.attrValueList,e.row.name,e.row)))])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return t.handleXsProductNext(e.$index,e.row)}}},[t._v("\n            添加为线索\n          ")])]}}])})],1)],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("el-drawer",{attrs:{title:"我是标题",size:920,visible:t.searchDrawer,"with-header":!1},on:{"update:visible":function(e){t.searchDrawer=e}}},[a("div",{staticStyle:{padding:"20px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-input",{staticStyle:{width:"321px","margin-bottom":"24px"},attrs:{placeholder:"请输入内容","prefix-icon":"el-icon-search",clearable:""},on:{input:t.handelOptsSearch},model:{value:t.keyword2,callback:function(e){t.keyword2=e},expression:"keyword2"}}),t._v(" "),!t.keyword2||t.optsSearchResult&&t.optsSearchResult.length?t._e():a("div",{staticStyle:{color:"rgba(0, 0, 0, 0.4)","font-family":"PingFang SC","font-size":"14px","font-style":"normal","font-weight":"400","line-height":"normal","letter-spacing":"0.56px"}},[t._v("\n          暂无符合条件的筛选项\n        ")]),t._v(" "),a("div",{staticStyle:{display:"flex","align-items":"center","flex-wrap":"wrap"}},t._l(t.optsSearchResult,function(e){return a("div",{key:e.value,staticClass:"opt-item spaceAround",class:t.getOptIsCheck(e)?"active":"",on:{click:function(a){return t.handelOptClick(e)}}},[t._v("\n            "+t._s(e.value)+"\n          ")])}),0),t._v(" "),t._l(t.checkBoxAttrGroup,function(e,n){return a("CheckBoxList",{key:e,attrs:{"group-name":e,index:n,list:t.checkBoxAttributeList.filter(function(t){return t.nameGroup===e}).map(function(t){return Object.assign({},t,{valueSearchType:t.valueSearchType||"must",valueSearchTypeValue:"must"!=t.valueSearchType?parseInt(t.valueSearchType.split("_")[1])||0:"",maxSholdValue:t.selectValue?t.selectValue.length:1})})},on:{change:t.handelCheckboxAttrChange,searchTypeClick:t.searchTypeClick}})}),t._v(" "),a("div",{staticStyle:{"margin-top":"10px"}},[a("div",{staticClass:"spaceStart flexWrap sxbox"},t._l(t.inputAttributeList,function(e,n){return a("InputNumber",{key:n,attrs:{item:e},on:{change:function(e,a){return t.handelInputAttrChange(n,e,a)}}})}),1),t._v(" "),a("div",{staticClass:"keyword_box"},[a("div",{staticClass:"spaceBetween"},[a("div",{staticClass:"spaceStart"},[a("div",{staticClass:"playSearch_tit"},[t._v("关键词")]),t._v(" "),a("el-input",{staticClass:"search_keyword",attrs:{placeholder:"请输入您要查找账号/关键词"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchListFun.apply(null,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)])])])],2),t._v(" "),a("div",{staticStyle:{"border-top":"dashed 1px #dcdcdc","padding-top":"10px","margin-top":"-10px"}},[a("div",{staticClass:"search_dzTitle"},[t._v("排序条件（可拖拽排序）")]),t._v(" "),a("div",{staticClass:"spaceStart sortItemBox",staticStyle:{"flex-wrap":"wrap","margin-bottom":"10px"}},t._l(t.comprehensiveData,function(e,n){return a("div",{key:e.sortId,staticClass:"spaceStart sort_item",class:""!=e.sort?"active":"",on:{click:function(a){return t.sortChos(e)}}},[a("div",[t._v(t._s(e.sortName))]),t._v(" "),""==e.sort&&""!=e.value?a("IconFont",{staticStyle:{margin:"0 0 0 4px"},attrs:{size:15,icon:"sort"}}):t._e(),t._v(" "),"asc"==e.sort&&""!=e.value?a("IconFont",{staticStyle:{margin:"0 0 0 4px"},attrs:{size:15,icon:"asc"}}):t._e(),t._v(" "),"desc"==e.sort&&""!=e.value?a("IconFont",{staticStyle:{margin:"0 0 0 4px"},attrs:{size:15,icon:"desc"}}):t._e()],1)}),0)]),t._v(" "),a("div",{staticStyle:{"border-top":"dashed 1px #dcdcdc","padding-top":"10px"}},[a("div",{staticClass:"search_dzTitle"},[t._v("其他条件")]),t._v(" "),a("div",{staticClass:"spaceStart",staticStyle:{"margin-bottom":"20px","flex-wrap":"wrap"}},[a("div",{staticClass:"labelTitle"},[t._v("是否排除号商")]),t._v(" "),a("el-radio",{attrs:{label:"1"},model:{value:t.excludeBmc,callback:function(e){t.excludeBmc=e},expression:"excludeBmc"}},[t._v("是")]),t._v(" "),a("el-radio",{attrs:{label:"0"},model:{value:t.excludeBmc,callback:function(e){t.excludeBmc=e},expression:"excludeBmc"}},[t._v("否")]),t._v(" "),a("div",{staticClass:"labelTitle",staticStyle:{width:"120px","margin-left":"50px"}},[t._v("\n            筛选时间范围：\n          ")]),t._v(" "),a("el-date-picker",{attrs:{type:"datetime","default-time":"00:00:00",placeholder:"选择开始时间"},model:{value:t.startTime,callback:function(e){t.startTime=e},expression:"startTime"}}),t._v(" "),a("el-date-picker",{attrs:{type:"datetime","default-time":"23:59:59",placeholder:"选择结束时间"},model:{value:t.endTime,callback:function(e){t.endTime=e},expression:"endTime"}}),t._v(" "),a("div",{staticClass:"labelTitle",staticStyle:{width:"85px"}},[t._v("\n            快捷时间：\n          ")]),t._v(" "),a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入分钟",oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:t.startTimeNumber,callback:function(e){t.startTimeNumber=e},expression:"startTimeNumber"}},[a("template",{slot:"append"},[a("el-select",{staticStyle:{width:"80px"},attrs:{placeholder:"请选择"},model:{value:t.startTimeUnit,callback:function(e){t.startTimeUnit=e},expression:"startTimeUnit"}},t._l(t.startTimeUnitList,function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})}),1)],1)],2)],1)]),t._v(" "),t.selectValueList().length||t.selectNumValueList().length?a("div",{staticClass:"spaceStart",staticStyle:{"align-items":"baseline","border-bottom":"0.5px solid #ff7a00","margin-bottom":"0px","padding-bottom":"20px","margin-top":"20px","max-height":"200px","overflow-y":"auto"}},[a("span",{staticClass:"playSearch_tit",staticStyle:{"font-weight":"600"}},[t._v("您已选择：")]),t._v(" "),a("div",{staticClass:"spaceStart flexWrap",staticStyle:{flex:"1"}},[t._l(t.selectValueList(),function(e){return a("span",{key:e.value,staticClass:"opt-item",on:{click:function(a){return t.handelOptClick(e)}}},[t._v("\n            "+t._s(e.value)+" "),a("i",{staticClass:"el-icon-close",staticStyle:{"font-size":"14px",cursor:"pointer"}})])}),t._v(" "),t._l(t.selectNumValueList(),function(e){return a("span",{key:e.value,staticClass:"opt-item",on:{click:function(a){return t.handelOptNumClick(e)}}},[t._v("\n            "+t._s(e.value)+" "),a("i",{staticClass:"el-icon-close",staticStyle:{"font-size":"14px",cursor:"pointer"}})])})],2)]):t._e(),t._v(" "),a("div",{staticClass:"config-footer",staticStyle:{position:"relative",top:"10px"}},[a("el-button",{on:{click:function(e){t.searchDrawer=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")])],1)])]),t._v(" "),a("swperImagePriviewPage",{ref:"swperImagePriviewPage"})],1)},staticRenderFns:[]};var A=a("VU/8")(T,I,!1,function(t){a("jXgK"),a("RkEX")},"data-v-33192787",null);e.default=A.exports},Hyge:function(t,e){},JlRY:function(t,e){},Joqx:function(t,e,a){"use strict";var n=a("d7EF"),i=a.n(n),r=a("Dd8w"),s=a.n(r),o=a("bOdI"),c=a.n(o),l=a("FnAM"),u=a("6Yow"),d=a.n(u),h=a("Lfj9"),p={components:{swperImagePriview:l.a},data:function(){var t;return t={tableData:[],productObj:{},productSn:"",showViewer:!1,imgViewer:0,arrDtPicForShow:[],gameSysinfoReadcount:0,gameSysinfoCollectcount:0,price:0},c()(t,"gameSysinfoReadcount",""),c()(t,"gameSysinfoCollectcount",""),c()(t,"checkBoxAttributeList",[]),t},mounted:function(){},methods:{closeViewer:function(){this.showViewer=!1},getSarchArr:function(t){console.log(this.checkBoxAttributeList,11122222);var e=this.checkBoxAttributeList.map(function(t){return console.log(t,111111),s()({},t,{value:t.value})}),a=e.findIndex(function(t){return 32306===t.sort&&t.ename});if(-1!==a){var n=e[a],r=n.value,o=n.ename;r&&[].push({key:o,value:r}),e.splice(a,1)}var c=t.map(function(t){var e=t.selectValue||[],a=i()(e,2),n=a[0],r=a[1];return"价格"!==t.name||isNaN(n)&&isNaN(r)||[{min:isNaN(n)?void 0:parseInt(n,10),key:"price",max:isNaN(r)?void 0:parseInt(r,10)}],s()({},t,{value:isNaN(n)&&isNaN(r)?"":(n||0)+"-"+(r||9999999)})}).filter(function(t){return"价格"!==t.name});return e.concat(c)},formatValue:function(t){return t.replace(/[,]/g,"，").replace(/\[核\]/g,"").replace(/\[绝\]/g,"").replace(/\[钱\]/g,"")},mergeOptions:function(t,e){var a=this;t.sort(function(t,e){return t.type-e.type}),t.sort(function(t,e){return e.sort-t.sort});var n=[];return t.forEach(function(t){if("营地ID"==t.name){var i=e.find(function(e){return e.productAttributeId===t.id});a.wzryId=i&&i.value}if(1===t.type||2===t.type){var r=e.find(function(e){return e.productAttributeId===t.id});r&&r.value&&n.push({name:2==t.selectType?"【"+t.name+"】"+a.formatValue(r.value):t.name,label:t.name,value:a.formatValue(r.value),sort:t.sort,selectType:t.selectType})}}),n},showImagePriview:function(t,e){var a=this;console.log(t,1111111),Object(h.s)({productSn:t.productSn}).then(function(e){a.checkBoxAttributeList=e.data.productAttributeList;var n=e.data.product;if(-2!=n.publishStatus){var i=n.albumPicsJson?n.albumPicsJson:[],r=[];n.albumPics?r=n.albumPics.split(",").filter(function(t){return""!==t.trim()}):(i=JSON.parse(i)).forEach(function(t){r.length<10&&t.url&&r.push(t.url)}),a.productObj=t,a.productSn=t.productSn,a.gameSysinfoReadcount=t.gameSysinfoReadcount,a.gameSysinfoCollectcount=t.gameSysinfoCollectcount,a.price=t.price;var s=[],o=[];a.mergeOptions(e.data.productAttributeList,e.data.productAttributeValueList).forEach(function(t){2==t.selectType?o.push(t):s.push(t)}),s.sort(function(t,e){return e.type-t.type}),o.sort(function(t,e){return e.sort-t.sort}),e.data.product.description&&o.push({name:"【卖家说】"+e.data.product.description,value:"",sort:11,selectType:2});var c=s.concat(o),l=a.getSarchArr(e.data.productAttributeList);console.log(c,l,11111),c.forEach(function(t){var e=l.find(function(e){return e.name==t.label});if(e&&e.value&&e.value.length>0){var a=e.value.split(",");a.sort(function(t,e){return e.length-t.length}),a.forEach(function(e){var a=new RegExp("("+e+")","gi");2==t.selectType&&(t.name=t.name.replace(a,'<span style="color:#ff720c">$1</span>')),t.value=t.value.replace(a,'<span style="color:#ff720c">$1</span>')})}}),a.tableData=c,console.log(a.tableData),a.playTableLoading=!1,r.unshift(d.a),a.arrDtPicForShow=r,a.showViewer=!0}else a.$message.warning("当前商品已下架，不可预览")})}}},f={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.showViewer?a("swperImagePriview",{attrs:{tableData:t.tableData,product:t.productObj,productSn:t.productSn,"z-index":1e4,"initial-index":t.imgViewer,"on-close":t.closeViewer,"url-list":t.arrDtPicForShow,tableDataFlag:!0,gameSysinfoReadcount:t.gameSysinfoReadcount,gameSysinfoCollectcount:t.gameSysinfoCollectcount,price:t.price}}):t._e()],1)},staticRenderFns:[]},m=a("VU/8")(p,f,!1,null,null,null);e.a=m.exports},NYDq:function(t,e){},OyM5:function(t,e){t.exports="data:image/png;base64,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"},RkEX:function(t,e){},jXgK:function(t,e){},r5is:function(t,e){},zBuL:function(t,e,a){"use strict";var n=a("mvHQ"),i=a.n(n),r={props:{item:{type:Object,default:function(){}},index:{type:Number,default:0}},data:function(){return{}},methods:{change:function(t,e){var a=this.item.selectValue,n=JSON.parse(i()(a));n[e]=t,this.$emit("change",n)}}},s={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"spaceStart playSearch_wrap",staticStyle:{width:"33.3333333%","flex-shrink":"0","padding-right":"20px","margin-bottom":"24px"}},[a("div",{staticClass:"playSearch_tit"},[t._v(t._s(t.item.name))]),t._v(" "),a("div",{staticClass:"spaceStart sxboxItem"},[a("el-input",{staticStyle:{"margin-right":"15px"},attrs:{value:t.item.selectValue[0],placeholder:"最低"+t.item.name,size:"small",type:"tel",onkeypress:"return(/[\\d]/.test(String.fromCharCode(event.keyCode)))"},on:{input:function(e){return t.change(e,0)}}}),t._v(" "),a("div",{staticStyle:{"margin-right":"15px",color:"rgb(153, 153, 153)"}},[t._v("-")]),t._v(" "),a("el-input",{attrs:{value:t.item.selectValue[1],placeholder:"最高"+t.item.name,size:"small",type:"tel",onkeypress:"return(/[\\d]/.test(String.fromCharCode(event.keyCode)))"},on:{input:function(e){return t.change(e,1)}}})],1)])},staticRenderFns:[]};var o=a("VU/8")(r,s,!1,function(t){a("NYDq")},"data-v-25f451be",null);e.a=o.exports}});