webpackJsonp([96],{"1fbc":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("woOf"),r=a.n(i),o=a("FWz8"),l=a("UgCr"),n=a("xT6B"),s={pageNum:1,pageSize:20,productCategoryId:108,productSn:null,orderSn:null},c={bankNo:"",accountName:"",productCategoryName:"",payeeType:1,dehh:"",price:"",applyNote:"",password:"",bank_code:"",cnapsCode:""},u={0:"银行卡",2:"支付宝",1:"其他汇款"},d={0:"待处理",1:"汇款中",2:"已完成",3:"已拒绝",4:"失败",5:"已结单"},f={name:"<PERSON>fuguan<PERSON>",components:{SingleUpload:a("TZVV").a},filters:{shouType:function(e){return u[e]},getType:function(e){return 2==e?"对公银行账户":"对私银行账户"},formatCreateTime:function(e){var t=new Date(e);return Object(n.a)(t,"YYYY-MM-DD HH:mm:ss")},verifyStatusFilter:function(e){return e?"启用":"禁用"},verifyStatusFilter2:function(e){return e?"禁用":"启用"},formatStatus:function(e){return d[e]}},data:function(){return{submitVerifyReview:!1,showConfirm:!1,balance:"",isCheckDetail:!1,verfiyForm:r()({status:null,id:""},c),cardList:[],cardList2:[],bankList:[],areas:[],subjectList:[],prefrenceAreaList:[],listLoading:!0,list:[],total:0,showAddModel:!1,listQuery:r()({},s),value:r()({},c),rules:{},rules2:{status:[{required:!0,message:"请先选择审核结果",trigger:"blur"}]},formLabelWidth:"140px",showVerifyDialog:!1}},computed:{selectSubject:{get:function(){var e=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return e;for(var t=0;t<this.value.subjectProductRelationList.length;t++)e.push(this.value.subjectProductRelationList[t].subjectId);return e},set:function(e){this.value.subjectProductRelationList=[];for(var t=0;t<e.length;t++)this.value.subjectProductRelationList.push({subjectId:e[t]})}}},created:function(){this.getList(),this.init()},methods:{handleResetSearch:function(){this.listQuery=r()({},s)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},verfiyFormBankCodeChange:function(e){var t=this.bankList.find(function(t){return t.node.bank_code==e});this.verfiyForm.bank_name=t.node.bank_name},submitVerify:function(){var e=this;this.isCheckDetail?this.showVerifyDialog=!1:this.$refs.verifyForm.validate(function(t){t&&(e.submitVerifyReview=!0)})},submitVerifyReal:function(){var e=this;this.submitVerifyReview=!1;var t=void 0,a=this.verfiyForm.bank_code||"",i=this.bankList.find(function(e){return e.node.bank_code==a}),r=i?i.node.bank_name:"";t=2===this.verfiyForm.payeeType?{accountName:this.verfiyForm.accountName,bankNo:this.verfiyForm.bankNo,payeeType:this.verfiyForm.payeeType,applyNote:this.verfiyForm.applyNote,price:this.verfiyForm.price,bankCode:a,bankName:r,cnapsCode:this.verfiyForm.cnapsCode,status:this.verfiyForm.status,password:this.verfiyForm.password,verifyNote:this.verfiyForm.verifyNote}:{bankCode:a,bankName:r,bankNo:this.verfiyForm.bankNo,accountName:this.verfiyForm.accountName,price:this.verfiyForm.price,payeeType:this.verfiyForm.payeeType,applyNote:this.verfiyForm.applyNote,status:this.verfiyForm.status,password:this.verfiyForm.password,verifyNote:this.verfiyForm.verifyNote},this.showVerifyDialog=!1,Object(l.o)(this.verfiyForm.id,t).then(function(t){200==t.code&&e.$message.success("审核成功")})},checkDetail:function(e,t){var a=this;this.verfiyForm.status=null,this.isCheckDetail=!0,Object(l.n)(t.id).then(function(e){200==e.code&&(a.verfiyForm.id=t.id,a.verfiyForm=r()({},a.verfiyForm,e.data),a.verfiyForm.bank_code=e.data.bankCode,a.verfiyForm.cnapsCode=null,a.showVerifyDialog=!0,1!=t.type?Object(o.i)({orderId:t.orderId}).then(function(e){200==e.code&&(a.balance=e.data.balance)}):a.balance=null)})},doVerify:function(e,t){var a=this;this.verfiyForm.status=null,this.isCheckDetail=!1,Object(l.n)(t.id).then(function(e){200==e.code&&(a.verfiyForm.id=t.id,a.verfiyForm=r()({},a.verfiyForm,e.data),a.verfiyForm.status=null,a.verfiyForm.bank_code=e.data.bankCode,a.verfiyForm.cnapsCode=null,a.showVerifyDialog=!0,1!=t.type?Object(o.i)({orderId:t.orderId}).then(function(e){200==e.code&&(a.balance=e.data.balance)}):a.balance=null)})},searchCnap2:function(){var e=this,t=this.verfiyForm.bank_code,a=this.bankList.find(function(e){return e.node.bank_code==t}).node.bank_name,i=this.verfiyForm.city_code?this.verfiyForm.city_code[this.verfiyForm.city_code.length-1]:"",r={bank_code:t,bank_name:a,city_code:i};Object(l.z)(r).then(function(t){e.cardList2=t.data.card_list||[]})},searchCnap:function(){var e=this,t=this.value.bank_code,a=this.bankList.find(function(e){return e.node.bank_code==t}).node.bank_name,i=this.value.city_code?this.value.city_code[this.value.city_code.length-1]:"",r={bank_code:t,bank_name:a,city_code:i};Object(l.z)(r).then(function(t){e.cardList=t.data.card_list||[]})},init:function(){var e=this;Object(l.y)().then(function(t){var a=t.data,i=void 0===a?"[]":a;e.areas=JSON.parse(i)}),Object(l.x)().then(function(t){var a=t.data,i=void 0===a?"[]":a;e.bankList=JSON.parse(i)})},filterMethod:function(e,t){return t.label.indexOf(e)>-1},getButtonType:function(e){return e?"danger":"success"},getList:function(){var e=this;this.listLoading=!0,Object(l.k)(this.listQuery).then(function(t){e.listLoading=!1;var a=t.data.list;e.list=a,e.total=t.data.total})},createHelp:function(){this.clearValue(),this.id="",this.isEdit=!1,this.showAddModel=!0},clearValue:function(){this.value=r()({},c)},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},cancel:function(){this.showAddModel=!1},submitForm:function(){this.showConfirm=!0},doSubmitForm:function(){var e=this;this.showConfirm=!1,this.showAddModel=!1;var t=this.value.bank_code,a=this.bankList.find(function(e){return e.node.bank_code==t}),i=a?a.node.bank_name:"",r={};r=2===this.value.payeeType?{accountName:this.value.accountName,bankNo:this.value.bankNo,payeeType:this.value.payeeType,applyNote:this.value.applyNote,price:this.value.price,password:this.value.password,bankCode:t,bankName:i,cnapsCode:this.value.cnapsCode}:{bankCode:t,bankName:i,bankNo:this.value.bankNo,accountName:this.value.accountName,price:this.value.price,payeeType:this.value.payeeType,applyNote:this.value.applyNote,password:this.value.password},Object(l.c)(r).then(function(){e.getList()})}}},v={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"订单编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"订单编号"},model:{value:e.listQuery.orderSn,callback:function(t){e.$set(e.listQuery,"orderSn",t)},expression:"listQuery.orderSn"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"商品编号"},model:{value:e.listQuery.productSn,callback:function(t){e.$set(e.listQuery,"productSn",t)},expression:"listQuery.productSn"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询搜索\n          ")]),e._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),a("div",{staticClass:"table-container"},[a("el-button",{staticClass:"add-help",attrs:{type:"primary"},on:{click:e.createHelp}},[e._v("汇款")]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[a("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"收款人",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.accountName))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"商品编号",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.productSn))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"汇款金额",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.price))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"收款类型",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(e._f("shouType")(t.row.type)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"交易状态",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.txnStatus))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"订单编号",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.orderSn))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"汇款结果",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(e._f("formatStatus")(t.row.status)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(e._f("formatCreateTime")(t.row.createTime)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(e._f("formatCreateTime")(t.row.updatedTime)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[t.row.status>0?a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.checkDetail(t.$index,t.row)}}},[e._v("查看详情\n            ")]):a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.doVerify(t.$index,t.row)}}},[e._v("审核\n            ")])],1)]}}])})],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),e.showAddModel?a("el-dialog",{attrs:{visible:e.showAddModel,width:"50%",top:"1vh"},on:{"update:visible":function(t){e.showAddModel=t}}},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("汇款信息")])]),e._v(" "),a("el-form",{ref:"helpForm",staticClass:"form-box",attrs:{model:e.value,rules:e.rules,"label-width":e.formLabelWidth}},[a("el-form-item",{attrs:{label:"银行卡号"}},[a("el-input",{attrs:{placeholder:"请输入银行卡号"},model:{value:e.value.bankNo,callback:function(t){e.$set(e.value,"bankNo",t)},expression:"value.bankNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"收款人"}},[a("el-input",{attrs:{placeholder:"请输入收款人"},model:{value:e.value.accountName,callback:function(t){e.$set(e.value,"accountName",t)},expression:"value.accountName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"汇款金额"}},[a("el-input",{attrs:{placeholder:"请输入汇款金额"},model:{value:e.value.price,callback:function(t){e.$set(e.value,"price",t)},expression:"value.price"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"申请备注"}},[a("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.value.applyNote,callback:function(t){e.$set(e.value,"applyNote",t)},expression:"value.applyNote"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"密码"}},[a("el-input",{attrs:{type:"password",placeholder:"请输入密码"},model:{value:e.value.password,callback:function(t){e.$set(e.value,"password",t)},expression:"value.password"}})],1)],1),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("helpForm")}}},[e._v("确 定")])],1)],1)],1):e._e(),e._v(" "),a("el-dialog",{attrs:{visible:e.showVerifyDialog,width:"50%",top:"1vh"},on:{"update:visible":function(t){e.showVerifyDialog=t}}},[a("el-form",{ref:"verifyForm",attrs:{"label-width":e.formLabelWidth,model:e.verfiyForm,rules:e.rules2}},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("汇款信息")])]),e._v(" "),a("el-form-item",{attrs:{label:"商品编号"}},[e._v("\n            "+e._s(e.verfiyForm.productSn)),a("span",{staticClass:"red"},[e._v("剩余金额：¥"+e._s(e.balance))])]),e._v(" "),a("el-form-item",{attrs:{label:"收款类型"}},[e._v("\n            "+e._s(e._f("shouType")(e.verfiyForm.type))+"\n          ")]),e._v(" "),a("el-form-item",{attrs:{label:"收款账号"}},[a("el-input",{attrs:{disabled:!0,placeholder:"请输入银行卡号"},model:{value:e.verfiyForm.bankNo,callback:function(t){e.$set(e.verfiyForm,"bankNo",t)},expression:"verfiyForm.bankNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"收款人"}},[a("el-input",{attrs:{disabled:!0,placeholder:"请输入收款人"},model:{value:e.verfiyForm.accountName,callback:function(t){e.$set(e.verfiyForm,"accountName",t)},expression:"verfiyForm.accountName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"汇款金额"}},[a("el-input",{attrs:{disabled:!0,placeholder:"请输入汇款金额"},model:{value:e.verfiyForm.price,callback:function(t){e.$set(e.verfiyForm,"price",t)},expression:"verfiyForm.price"}})],1),e._v(" "),e.isCheckDetail?a("el-form-item",{attrs:{label:"看看汇款单号"}},[a("el-input",{attrs:{disabled:e.isCheckDetail},model:{value:e.verfiyForm.txnSeqno,callback:function(t){e.$set(e.verfiyForm,"txnSeqno",t)},expression:"verfiyForm.txnSeqno"}})],1):e._e(),e._v(" "),e.isCheckDetail?a("el-form-item",{attrs:{label:"交易状态"}},[a("el-input",{attrs:{disabled:e.isCheckDetail},model:{value:e.verfiyForm.txnStatus,callback:function(t){e.$set(e.verfiyForm,"txnStatus",t)},expression:"verfiyForm.txnStatus"}})],1):e._e(),e._v(" "),e.isCheckDetail?a("el-form-item",{attrs:{label:"连连交易单号"}},[a("el-input",{attrs:{disabled:e.isCheckDetail},model:{value:e.verfiyForm.accpTxno,callback:function(t){e.$set(e.verfiyForm,"accpTxno",t)},expression:"verfiyForm.accpTxno"}})],1):e._e(),e._v(" "),a("el-form-item",{attrs:{label:"申请备注"}},[a("el-input",{attrs:{disabled:!0,placeholder:"请输入备注"},model:{value:e.verfiyForm.applyNote,callback:function(t){e.$set(e.verfiyForm,"applyNote",t)},expression:"verfiyForm.applyNote"}})],1),e._v(" "),e.verfiyForm.lianFailReason?a("el-form-item",{attrs:{label:"失败原因"}},[e._v("\n            "+e._s(e.verfiyForm.lianFailReason)+"\n          ")]):e._e()],1),e._v(" "),a("el-card",[a("el-form-item",{attrs:{label:"密码"}},[a("el-input",{attrs:{disabled:e.isCheckDetail,type:"password",placeholder:"请输入密码"},model:{value:e.verfiyForm.password,callback:function(t){e.$set(e.verfiyForm,"password",t)},expression:"verfiyForm.password"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"审核备注"}},[a("el-input",{attrs:{disabled:e.isCheckDetail,placeholder:"请输入备注"},model:{value:e.verfiyForm.verifyNote,callback:function(t){e.$set(e.verfiyForm,"verifyNote",t)},expression:"verfiyForm.verifyNote"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"status"}},[a("el-radio-group",{attrs:{disabled:e.isCheckDetail},model:{value:e.verfiyForm.status,callback:function(t){e.$set(e.verfiyForm,"status",t)},expression:"verfiyForm.status"}},[a("el-radio",{attrs:{label:1}},[e._v("通过")]),e._v(" "),a("el-radio",{attrs:{label:3}},[e._v("拒绝")]),e._v(" "),a("el-radio",{attrs:{label:5}},[e._v("结单")])],1)],1)],1),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:function(t){e.showVerifyDialog=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.submitVerify}},[e._v("确 定")])],1)],1)],1),e._v(" "),a("el-dialog",{attrs:{visible:e.showConfirm,width:"40%",top:"2vh"},on:{"update:visible":function(t){e.showConfirm=t}}},[a("el-form",[a("el-form-item",{attrs:{label:"银行卡号"}},[e._v("\n          "+e._s(e.value.bankNo)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"收款人"}},[e._v("\n          "+e._s(e.value.accountName)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"汇款金额"}},[a("span",{staticClass:"red"},[e._v("¥"+e._s(e.value.price))])])],1),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:function(t){e.showConfirm=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.doSubmitForm("helpForm")}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{visible:e.submitVerifyReview,width:"40%",top:"2vh"},on:{"update:visible":function(t){e.submitVerifyReview=t}}},[a("el-form",[a("el-form-item",{attrs:{label:"收款账号"}},[e._v("\n          "+e._s(e.verfiyForm.bankNo)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"收款人"}},[e._v("\n          "+e._s(e.verfiyForm.accountName)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"汇款金额"}},[a("span",{staticClass:"red"},[e._v("¥"+e._s(e.verfiyForm.price))])])],1),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:function(t){e.submitVerifyReview=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.submitVerifyReal}},[e._v("确 定")])],1)],1)],1)],1)},staticRenderFns:[]};var m=a("VU/8")(f,v,!1,function(e){a("9iNk")},"data-v-18cb7a84",null);t.default=m.exports},"9iNk":function(e,t){}});