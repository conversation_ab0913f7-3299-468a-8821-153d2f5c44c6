webpackJsonp([3],{Ooug:function(e,t){},tklY:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("//Fk"),a=i.n(n),l=i("woOf"),s=i.n(l),o=i("M9A7"),r=i("STSY"),c=i("xT6B"),d=i("II7+"),u=i("0xDb"),m=i("TZVV"),p=i("mRsl"),f=i("ocgh"),h=0,v={pageNum:1,pageSize:20,keyword:null},g={gender:"",baopeiConfirm:"",icon:"",nickname:"",password:"",password2:"",phone:"",realNameConfirm:"",realPersionConfirm:"",status:"",username:"",vxname:""},b={name:"AdminList",components:{SingleUpload:m.a,tedian:d.a},filters:{formatDateTime:function(e){if(null==e||""===e)return"N/A";var t=new Date(e);return Object(c.a)(t,"YYYY-MM-DD HH:mm:ss")}},data:function(){return{detailOptions:[],opetionDate:[],pwd0:"",pwd1:"",listQuery:s()({},v),list:null,total:null,listLoading:!1,dialogVisible:!1,dialogVisiblePwd:!1,admin:s()({},g),isEdit:!1,allocDialogVisible:!1,allocRoleIds:[],allRoleList:[],allocAdminId:null,hs:!1}},watch:{$route:function(e,t){this.beforeInit()}},created:function(){this.beforeInit()},methods:{onMemberImInit:function(e){var t=this,i={memberId:e.id};Object(f.b)(i).then(function(e){200==e.code&&(t.getList(),t.$message.success(e.message))})},beforeInit:function(){"bigMember"===this.$route.name?(this.hs=!0,this.admin.type=1,g.type=1):(this.hs=!1,this.admin.type=0,g.type=0),this.doInit()},clearValue:function(){this.detailOptions=[],this.admin=s()({},g)},changepwd0:function(e){this.pwd0=e},changepwd1:function(e){this.pwd1=e},doInit:function(){console.log(this.hs),this.getList(),this.getAllRoleList()},handleResetSearch:function(){this.listQuery=s()({},v)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},handleAdd:function(){var e=this;this.getGameCate().then(function(t){e.clearValue(),e.dialogVisible=!0,e.isEdit=!1,e.admin=s()({},g)})},handleStatusChange:function(e,t){var i=this;this.$confirm("是否要修改该状态?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(f.g)(t.id,{status:t.status,id:t.id,type:t.type}).then(function(e){i.$message({type:"success",message:"修改成功!"})})}).catch(function(){i.$message({type:"info",message:"取消修改"}),i.getList()})},handleDelete:function(e,t){var i=this;this.$confirm("是否要删除该用户?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(f.d)(t.id).then(function(e){i.$message({type:"success",message:"删除成功!"}),i.getList()})})},getGameCate:function(){var e=this;return new a.a(function(t,i){a.a.all([Object(p.d)(74,{pageNum:1,pageSize:999}),Object(p.d)(73,{pageNum:1,pageSize:999})]).then(function(i){var n=[];n.push({icon:"",name:"手游",splitTitle:1,checked:!1});var a=i[0].data.list.map(function(e){return{icon:"",name:e.name,checked:!1,id:e.id}});(n=n.concat(a)).push({icon:"",name:"端游",splitTitle:1,checked:!1});var l=i[1].data.list.map(function(e){return{icon:"",name:e.name,checked:!1,id:e.id}}),s={childList:n=n.concat(l),name:"关联游戏",is_required:0,field_type:2,default_word:"点击可下拉选择",tdtype:3,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[],id:+new Date};e.opetionDate=[s],t()})})},handleUpdate:function(e,t){var i=this;this.getGameCate().then(function(e){Object(f.a)(t.id).then(function(e){i.clearValue(),i.admin=e.data;var t=i.admin.categoryList.map(function(e){return{icon:"",id:e.id,name:e.name,checked:!0}});i.detailOptions=[{title:"关联游戏",tdtype:3,value:t,id:h++}],i.dialogVisible=!0,i.isEdit=!0,u.b.async2opetionDate(i.detailOptions,i.opetionDate)})})},handleUpdatePwd:function(e,t){this.dialogVisiblePwd=!0,this.pwd0="",this.pwd1="",this.admin=s()({},t)},handleDialogConfirmPwd:function(){var e=this;this.pwd0?this.pwd0===this.pwd1?this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(f.h)({},{id:e.admin.id,password:e.pwd0,pass2:e.pwd1}).then(function(t){e.$message({message:"密码修改成功",type:"success"}),e.dialogVisiblePwd=!1})}):this.$message({message:"两次密码不一致，请重新输入",type:"error"}):this.$message({message:"请输入密码",type:"error"})},handleDialogConfirm:function(){var e=this;if(!this.isEdit){if(!this.admin.password)return void this.$message({message:"请输入密码",type:"error"});if(this.admin.password!==this.admin.password2)return void this.$message({message:"两次密码不一致，请重新输入",type:"error"})}this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=e.opetionDate[0].choosedList;e.admin.categoryList=t,e.isEdit?Object(f.f)(e.admin.id,e.admin).then(function(t){e.$message({message:"修改成功！",type:"success"}),e.dialogVisible=!1,e.getList()}):Object(f.c)(e.admin).then(function(t){e.$message({message:"添加成功！",type:"success"}),e.dialogVisible=!1,e.getList()})})},handleAllocDialogConfirm:function(){var e=this;this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=new URLSearchParams;t.append("adminId",e.allocAdminId),t.append("roleIds",e.allocRoleIds),Object(o.a)(t).then(function(t){e.$message({message:"分配成功！",type:"success"}),e.allocDialogVisible=!1})})},handleSelectRole:function(e,t){this.allocAdminId=t.id,this.allocDialogVisible=!0,this.getRoleListByAdmin(t.id)},getList:function(){var e=this;this.listLoading=!0,this.hs?this.listQuery.type=1:this.listQuery.type=0,Object(f.e)(this.listQuery).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})},getAllRoleList:function(){var e=this;Object(r.e)().then(function(t){e.allRoleList=t.data})},getRoleListByAdmin:function(e){var t=this;Object(o.f)(e).then(function(e){var i=e.data;if(t.allocRoleIds=[],null!=i&&i.length>0)for(var n=0;n<i.length;n++)t.allocRoleIds.push(i[n].id)})}}},_={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[i("div",{staticStyle:{"margin-top":"15px"}},[i("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[i("el-form-item",{attrs:{label:"输入搜索："}},[i("el-input",{staticClass:"input-width",attrs:{placeholder:"帐号/姓名",clearable:""},model:{value:e.listQuery.keyword,callback:function(t){e.$set(e.listQuery,"keyword",t)},expression:"listQuery.keyword"}})],1),e._v(" "),i("el-form-item",[i("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询搜索\n          ")]),e._v(" "),i("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),i("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[i("i",{staticClass:"el-icon-tickets"}),e._v(" "),i("span",[e._v("数据列表")]),e._v(" "),i("el-button",{staticClass:"btn-add",staticStyle:{"margin-left":"20px"},attrs:{size:"mini"},on:{click:function(t){return e.handleAdd()}}},[e._v("添加")])],1),e._v(" "),i("div",{staticClass:"table-container"},[e.hs?i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"adminTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[i("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"帐号",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.username))]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"昵称",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.nickname))]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"IM号",width:"260",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.imaccount)+"\n          "),i("el-link",{attrs:{type:"primary"},on:{click:function(i){return e.onMemberImInit(t.row)}}},[e._v("初始化IM号")])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"添加时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatDateTime")(t.row.createTime)))]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"是否启用",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(i){return e.handleStatusChange(t.$index,t.row)}},model:{value:t.row.status,callback:function(i){e.$set(t.row,"status",i)},expression:"scope.row.status"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleUpdatePwd(t.$index,t.row)}}},[e._v("\n            修改密码\n          ")]),e._v(" "),i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleUpdate(t.$index,t.row)}}},[e._v("\n            编辑\n          ")]),e._v(" "),i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleDelete(t.$index,t.row)}}},[e._v("删除\n          ")])]}}])})],1):i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"adminTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[i("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}],null,!1,283767728)}),e._v(" "),i("el-table-column",{attrs:{label:"帐号",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.username))]}}],null,!1,1495375691)}),e._v(" "),i("el-table-column",{attrs:{label:"昵称",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.nickname))]}}],null,!1,**********)}),e._v(" "),i("el-table-column",{attrs:{label:"手机号",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone))]}}],null,!1,**********)}),e._v(" "),i("el-table-column",{attrs:{label:"IM号",width:"260",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.imaccount)+"\n          "),i("el-link",{attrs:{type:"primary"},on:{click:function(i){return e.onMemberImInit(t.row)}}},[e._v("初始化IM号")])]}}],null,!1,**********)}),e._v(" "),i("el-table-column",{attrs:{label:"包赔认证",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.baopeiConfirm?"是":"否"))]}}],null,!1,**********)}),e._v(" "),i("el-table-column",{attrs:{label:"是否实名认证",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.realNameConfirm?"是":"否"))]}}],null,!1,**********)}),e._v(" "),i("el-table-column",{attrs:{label:"是否实人认证",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.realPersionConfirm?"是":"否"))]}}],null,!1,**********)}),e._v(" "),i("el-table-column",{attrs:{label:"添加时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatDateTime")(t.row.createTime)))]}}],null,!1,1741732567)}),e._v(" "),i("el-table-column",{attrs:{label:"是否启用",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(i){return e.handleStatusChange(t.$index,t.row)}},model:{value:t.row.status,callback:function(i){e.$set(t.row,"status",i)},expression:"scope.row.status"}})]}}],null,!1,819699884)}),e._v(" "),i("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleUpdatePwd(t.$index,t.row)}}},[e._v("\n            修改密码\n          ")]),e._v(" "),i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleUpdate(t.$index,t.row)}}},[e._v("\n            编辑\n          ")]),e._v(" "),i("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleDelete(t.$index,t.row)}}},[e._v("删除\n          ")])]}}],null,!1,2821315970)})],1)],1),e._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),e.dialogVisible?i("el-dialog",{attrs:{title:e.isEdit?"编辑用户":"添加用户",visible:e.dialogVisible,width:"80%",top:"1vh"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-form",{ref:"adminForm",attrs:{model:e.admin,"label-width":"150px",size:"small"}},[e.hs?e._e():i("el-form-item",{attrs:{label:"性别："}},[i("el-radio-group",{model:{value:e.admin.gender,callback:function(t){e.$set(e.admin,"gender",t)},expression:"admin.gender"}},[i("el-radio",{attrs:{label:1}},[e._v("男")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("女")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"头像："}},[i("single-upload",{staticClass:"pic-box",model:{value:e.admin.icon,callback:function(t){e.$set(e.admin,"icon",t)},expression:"admin.icon"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"账号："}},[i("el-input",{model:{value:e.admin.username,callback:function(t){e.$set(e.admin,"username",t)},expression:"admin.username"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"昵称："}},[i("el-input",{model:{value:e.admin.nickname,callback:function(t){e.$set(e.admin,"nickname",t)},expression:"admin.nickname"}})],1),e._v(" "),e.isEdit?e._e():i("el-form-item",{attrs:{label:"密码："}},[i("el-input",{attrs:{type:"password"},model:{value:e.admin.password,callback:function(t){e.$set(e.admin,"password",t)},expression:"admin.password"}})],1),e._v(" "),e.isEdit?e._e():i("el-form-item",{attrs:{label:"重复密码："}},[i("el-input",{attrs:{type:"password"},model:{value:e.admin.password2,callback:function(t){e.$set(e.admin,"password2",t)},expression:"admin.password2"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"手机号："}},[i("el-input",{model:{value:e.admin.phone,callback:function(t){e.$set(e.admin,"phone",t)},expression:"admin.phone"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"IM号："}},[i("el-input",{model:{value:e.admin.imaccount,callback:function(t){e.$set(e.admin,"imaccount",t)},expression:"admin.imaccount"}})],1),e._v(" "),e.hs?i("el-form-item",{attrs:{label:"排序："}},[i("el-input",{model:{value:e.admin.integration,callback:function(t){e.$set(e.admin,"integration",t)},expression:"admin.integration"}})],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"是否包赔认证："}},[i("el-radio-group",{model:{value:e.admin.baopeiConfirm,callback:function(t){e.$set(e.admin,"baopeiConfirm",t)},expression:"admin.baopeiConfirm"}},[i("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"是否实名认证："}},[i("el-radio-group",{model:{value:e.admin.realNameConfirm,callback:function(t){e.$set(e.admin,"realNameConfirm",t)},expression:"admin.realNameConfirm"}},[i("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"是否实人认证："}},[i("el-radio-group",{model:{value:e.admin.realPersionConfirm,callback:function(t){e.$set(e.admin,"realPersionConfirm",t)},expression:"admin.realPersionConfirm"}},[i("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),e.hs?e._e():i("el-form-item",{attrs:{label:"是否系统用户："}},[i("el-radio-group",{model:{value:e.admin.type,callback:function(t){e.$set(e.admin,"type",t)},expression:"admin.type"}},[i("el-radio",{attrs:{label:10}},[e._v("是")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),e.isEdit?e._e():i("el-form-item",{attrs:{label:"是否启用："}},[i("el-radio-group",{model:{value:e.admin.status,callback:function(t){e.$set(e.admin,"status",t)},expression:"admin.status"}},[i("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),e.hs?i("el-form-item",{attrs:{label:""}},[i("tedian",{attrs:{"detail-options":e.detailOptions,"opetion-date":e.opetionDate}})],1):e._e()],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleDialogConfirm()}}},[e._v("确 定")])],1)],1):e._e(),e._v(" "),i("el-dialog",{attrs:{visible:e.allocDialogVisible,title:"分配角色",width:"30%",top:"1vh"},on:{"update:visible":function(t){e.allocDialogVisible=t}}},[i("el-select",{staticStyle:{width:"80%"},attrs:{multiple:"",placeholder:"请选择",size:"small"},model:{value:e.allocRoleIds,callback:function(t){e.allocRoleIds=t},expression:"allocRoleIds"}},e._l(e.allRoleList,function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}),1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(t){e.allocDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleAllocDialogConfirm()}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{visible:e.dialogVisiblePwd,title:"修改密码",width:"50%",top:"1vh"},on:{"update:visible":function(t){e.dialogVisiblePwd=t}}},[i("el-form",{ref:"adminForm",attrs:{model:e.admin,"label-width":"150px",size:"small"}},[i("el-form-item",{attrs:{label:"密码："}},[i("el-input",{staticStyle:{width:"250px"},attrs:{value:e.pwd0,type:"password"},on:{input:e.changepwd0}})],1),e._v(" "),i("el-form-item",{attrs:{label:"确认密码："}},[i("el-input",{staticStyle:{width:"250px"},attrs:{value:e.pwd1,type:"password"},on:{input:e.changepwd1}})],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogVisiblePwd=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleDialogConfirmPwd()}}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var w=i("VU/8")(b,_,!1,function(e){i("Ooug")},null,null);t.default=w.exports}});