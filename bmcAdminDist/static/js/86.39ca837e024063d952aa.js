webpackJsonp([86],{ZL9b:function(t,e){},ykmN:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("woOf"),l=a.n(n),i=a("vLgD");function s(t,e){return Object(i.a)({url:"/collectionAccounts/update/"+t,method:"post",data:e})}var u=a("xT6B"),r={pageNum:1,pageSize:20},o={name:"",bankName:"",bankAddress:"",number:""},c={name:"Ke<PERSON><PERSON>n<PERSON>",components:{SingleUpload:a("TZVV").a},filters:{formatCreateTime:function(t){var e=new Date(t);return Object(u.a)(e,"YYYY-MM-DD HH:mm:ss")},verifyStatusFilter:function(t){return t?"启用":"禁用"},verifyStatusFilter2:function(t){return t?"禁用":"启用"}},data:function(){return{subjectList:[],prefrenceAreaList:[],listLoading:!0,list:[],total:0,showAddModel:!1,listQuery:l()({},r),value:l()({},o),rules:{},formLabelWidth:"140px"}},computed:{selectSubject:{get:function(){var t=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return t;for(var e=0;e<this.value.subjectProductRelationList.length;e++)t.push(this.value.subjectProductRelationList[e].subjectId);return t},set:function(t){this.value.subjectProductRelationList=[];for(var e=0;e<t.length;e++)this.value.subjectProductRelationList.push({subjectId:t[e]})}}},created:function(){this.getList()},methods:{filterMethod:function(t,e){return e.label.indexOf(t)>-1},getButtonType:function(t){return t?"danger":"success"},toggleState:function(t,e){var a=this,n=e.id,i=l()({},e);i.status=i.status?0:1,s(n,i).then(function(){a.getList()})},getList:function(){var t,e=this;this.listLoading=!0,(t=this.listQuery,Object(i.a)({url:"/collectionAccounts/list",method:"get",params:t})).then(function(t){e.listLoading=!1;var a=t.data.list;e.list=a,e.total=t.data.total})},handleUpdateProduct:function(t,e){var a=this,n=e.id;(function(t){return Object(i.a)({url:"/collectionAccounts/updateInfo/"+t,method:"get"})})(n).then(function(t){a.value=l()({},t.data),a.id=n,a.isEdit=!0,a.showAddModel=!0})},createHelp:function(){this.clearValue(),this.id="",this.isEdit=!1,this.showAddModel=!0},clearValue:function(){this.value=l()({},o)},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},cancel:function(){this.showAddModel=!1},submitForm:function(){var t,e=this;this.showAddModel=!1,this.isEdit?s(this.id,this.value).then(function(){e.getList()}):(t=this.value,Object(i.a)({url:"/collectionAccounts/create",method:"post",data:t})).then(function(){e.getList()})}}},d={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"table-container"},[a("el-button",{staticClass:"add-help",attrs:{type:"primary"},on:{click:t.createHelp}},[t._v("新建收款账户")]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[a("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"账户持有人姓名",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.name))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"银行名称",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.bankName))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"银行地址",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.bankAddress))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"银行账户号",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.number))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"账户状态",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.status?"启用":"禁用"))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"更新时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatCreateTime")(e.row.updateTime)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatCreateTime")(e.row.createTime)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return t.handleUpdateProduct(e.$index,e.row)}}},[t._v("详情/编辑\n            ")]),t._v(" "),a("el-button",{attrs:{type:t.getButtonType(e.row.publishStatus),size:"mini"},on:{click:function(a){return t.toggleState(e.$index,e.row)}}},[t._v("\n              "+t._s(t._f("verifyStatusFilter2")(e.row.status))+"\n            ")])],1)]}}])})],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":t.listQuery.pageNum,"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),t.showAddModel?a("el-dialog",{attrs:{visible:t.showAddModel,width:"80%",top:"1vh"},on:{"update:visible":function(e){t.showAddModel=e}}},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("编辑收款信息")])]),t._v(" "),a("el-form",{ref:"helpForm",staticClass:"form-box",attrs:{model:t.value,rules:t.rules,"label-width":t.formLabelWidth}},[a("el-form-item",{attrs:{label:"账户持有人姓名"}},[a("el-input",{model:{value:t.value.name,callback:function(e){t.$set(t.value,"name",e)},expression:"value.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"银行名称"}},[a("el-input",{model:{value:t.value.bankName,callback:function(e){t.$set(t.value,"bankName",e)},expression:"value.bankName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"银行地址"}},[a("el-input",{model:{value:t.value.bankAddress,callback:function(e){t.$set(t.value,"bankAddress",e)},expression:"value.bankAddress"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"银行账户号"}},[a("el-input",{model:{value:t.value.number,callback:function(e){t.$set(t.value,"number",e)},expression:"value.number"}})],1)],1),t._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:t.cancel}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("helpForm")}}},[t._v("确 定")])],1)],1)],1):t._e()],1)])},staticRenderFns:[]};var f=a("VU/8")(c,d,!1,function(t){a("ZL9b")},"data-v-43349e36",null);e.default=f.exports}});