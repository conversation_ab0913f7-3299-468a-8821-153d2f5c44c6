webpackJsonp([65],{hclF:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n("woOf"),l=n.n(a),i=n("Mdfv"),s=n("xT6B"),r={pageNum:1,pageSize:20,name:null,type:null,endTime:null},o=[{label:"PC首页轮播",value:0},{label:"APP首页轮播",value:1}],u={name:"homeAdvertiseList",data:function(){return{listQuery:l()({},r),typeOptions:l()({},o),list:null,total:null,listLoading:!1,multipleSelection:[],operates:[{label:"删除",value:0}],operateType:null}},created:function(){this.getList()},filters:{formatType:function(e){return 1===e?"APP首页轮播":2===e?"主题页轮播":"PC首页轮播"},formatTime:function(e){if(null==e||""===e)return"N/A";var t=new Date(e);return Object(s.a)(t,"YYYY-MM-DD HH:mm:ss")}},methods:{handleResetSearch:function(){this.listQuery=l()({},r)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSelectionChange:function(e){this.multipleSelection=e},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},handleUpdateStatus:function(e,t){var n=this;this.$confirm("是否要修改上线/下线状态?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(i.f)(t.id,{status:t.status}).then(function(e){n.getList(),n.$message({type:"success",message:"修改成功!"})})}).catch(function(){n.$message({type:"success",message:"已取消操作!"}),n.getList()})},handleDelete:function(e,t){this.deleteHomeAdvertise(t.id)},handleBatchOperate:function(){if(this.multipleSelection<1)this.$message({message:"请选择一条记录",type:"warning",duration:1e3});else{for(var e=[],t=0;t<this.multipleSelection.length;t++)e.push(this.multipleSelection[t].id);0===this.operateType?this.deleteHomeAdvertise(e):this.$message({message:"请选择批量操作类型",type:"warning",duration:1e3})}},handleAdd:function(){this.$router.push({path:"/sms/addAdvertise"})},handleUpdate:function(e,t){this.$router.push({path:"/sms/updateAdvertise",query:{id:t.id}})},getList:function(){var e=this;this.listLoading=!0,Object(i.c)(this.listQuery).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})},deleteHomeAdvertise:function(e){var t=this;this.$confirm("是否要删除该广告?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var n=new URLSearchParams;n.append("ids",e),Object(i.b)(n).then(function(e){t.getList(),t.$message({type:"success",message:"删除成功!"})})})}}},c={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[n("div",{staticStyle:{"margin-top":"15px"}},[n("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[n("el-form-item",{attrs:{label:"广告名称："}},[n("el-input",{staticClass:"input-width",attrs:{placeholder:"广告名称"},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"广告位置："}},[n("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.type,callback:function(t){e.$set(e.listQuery,"type",t)},expression:"listQuery.type"}},e._l(e.typeOptions,function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),n("el-form-item",{attrs:{label:"到期时间："}},[n("el-date-picker",{staticClass:"input-width",attrs:{"value-format":"yyyy-MM-dd",type:"date",placeholder:"请选择时间"},model:{value:e.listQuery.endTime,callback:function(t){e.$set(e.listQuery,"endTime",t)},expression:"listQuery.endTime"}})],1),e._v(" "),n("el-form-item",[n("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询搜索\n          ")]),e._v(" "),n("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),n("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[n("i",{staticClass:"el-icon-tickets"}),e._v(" "),n("span",[e._v("数据列表")]),e._v(" "),n("el-button",{staticClass:"btn-add",attrs:{size:"mini"},on:{click:function(t){return e.handleAdd()}}},[e._v("添加广告")])],1),e._v(" "),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"homeAdvertiseTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"60",align:"center"}}),e._v(" "),n("el-table-column",{attrs:{label:"编号",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"广告名称",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"广告位置",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatType")(t.row.type)))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"广告图片",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[n("img",{staticStyle:{height:"80px"},attrs:{src:e.row.pic}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"时间",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v("开始时间："+e._s(e._f("formatTime")(t.row.startTime)))]),e._v(" "),n("p",[e._v("到期时间："+e._s(e._f("formatTime")(t.row.endTime)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"上线/下线",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(n){return e.handleUpdateStatus(t.$index,t.row)}},model:{value:t.row.status,callback:function(n){e.$set(t.row,"status",n)},expression:"scope.row.status"}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"点击次数",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.clickCount))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"生成订单",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.orderCount))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(n){return e.handleUpdate(t.$index,t.row)}}},[e._v("编辑\n          ")]),e._v(" "),n("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(n){return e.handleDelete(t.$index,t.row)}}},[e._v("删除\n          ")])]}}])})],1)],1),e._v(" "),n("div",{staticClass:"batch-operate-container"},[n("el-select",{attrs:{size:"small",placeholder:"批量操作"},model:{value:e.operateType,callback:function(t){e.operateType=t},expression:"operateType"}},e._l(e.operates,function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),e._v(" "),n("el-button",{staticClass:"search-button",staticStyle:{"margin-left":"20px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleBatchOperate()}}},[e._v("\n      确定\n    ")])],1),e._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":e.listQuery.pageNum,total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)}}})],1)],1)},staticRenderFns:[]};var d=n("VU/8")(u,c,!1,function(e){n("vEVn")},"data-v-6e07a94c",null);t.default=d.exports},vEVn:function(e,t){}});