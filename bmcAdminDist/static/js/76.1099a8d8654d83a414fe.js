webpackJsonp([76],{GUXj:function(e,t){},djCt:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("woOf"),i=a.n(n),r=a("so1O"),s=a("Lfj9"),o=a("Sbbi"),c=a("0xDb"),u={components:{MyTable:r.a},data:function(){return{util:c.b,dialogVisibleInden:!1,counteroffer_price:"",activeName:"NEGOTIATING",dicker_id:"",options:[{value:"",label:"查看全部"},{value:"NEGOTIATING",label:"议价中"},{value:"NEGOTIATION_SUCCESS",label:"议价成功"},{value:"NEGOTIATION_FAILED_OR_CANCELED",label:"议价失败/取消"}],searchData:{negoStatus:"NEGOTIATING"},defaultListQuery:{productSn:null,negoStatus:""},operationList:[{name:"商品编号",width:"150",value:"productSn"},{name:"分类名称",width:"150",value:"productCategoryName"},{name:"商品图",value:"name",slotName:"goodsPic",width:"120"},{name:"单价",width:"100",value:"originPrice"},{name:"议价",width:"100",value:"offerPrice"},{name:"状态",width:"100",value:"status",slotName:"status"},{name:"商品标题",value:"productName",slotName:"productName"},{name:"提交时间",value:"createTime",width:"150",slotName:"createTime"}]}},methods:{handleSearchList:function(){var e={pageNum:1,pageSize:20,negoStatus:"0"!=this.activeName?this.activeName:""};this.searchData=i()({},this.defaultListQuery,e)},handleResetSearch:function(){this.defaultListQuery={negoStatus:"",productSn:null},this.searchData={pageNum:1,pageSize:20,negoStatus:"0"!=this.activeName?this.activeName:""}},canRefoundBack:function(e){return-1==e.status||0==e.status||2==e.status||5==e.status},canDel:function(e){return 3==e.status||6==e.status},getBuyerList:s.q,getStatusType:function(e,t,a){return Object(o.b)(e,t,a)},deleteGoods:function(e){var t=this;this.$confirm("您确定要删除吗？删除后不可恢复?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(s.T)(e.id).then(function(e){200==e.code&&(t.$message.success("删除成功"),t.searchData=i()({},t.searchData))})})},refoundBack:function(e){var t=this;this.$confirm("您确定要撤回议价吗？确定后不可撤销。如已支付意向金，将在30分钟内原路退回","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(s.S)({negoId:e.id}).then(function(e){200==e.code&&(t.$message.success("撤销成功"),t.searchData=i()({},t.searchData))})})},handleClick:function(e){this.defaultListQuery.negoStatus=e.name;this.searchData=i()({},this.defaultListQuery,{pageNum:1,pageSize:20})},huanjiaFun:function(e){this.dicker_id=e.id,this.dialogVisibleInden=!0},payNowOrder:function(e){this.$router.push({path:"/BMC/payOrder?orderId="+e.orderId})},payProduct:function(e){var t=this,a={buyType:0,negoId:e.id,sourceType:3};Object(s.p)(a).then(function(e){200==e.code&&t.$router.push({path:"/BMC/payOrder?orderId="+e.data.id})})},counterPriceSure:function(){var e=this;this.counteroffer_price?this.counteroffer_price.length>9?this.$message.error("最多输入9位数字"):Object(s.U)({negoId:this.dicker_id,price:this.counteroffer_price}).then(function(t){e.dialogVisibleInden=!1,200==t.code&&(e.$message.success("议价回复成功！"),e.searchData=i()({},e.searchData))}):this.$message.error("请输入还价金额")},handleClose:function(){this.dialogVisibleInden=!1}}},l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"20px","margin-bottom":"100px"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"查看全部",name:""}}),e._v(" "),a("el-tab-pane",{attrs:{label:"议价中",name:"NEGOTIATING"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"议价成功",name:"NEGOTIATION_SUCCESS"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"议价失败/取消",name:"NEGOTIATION_FAILED_OR_CANCELED"}})],1),e._v(" "),a("el-form",{attrs:{inline:!0,model:e.defaultListQuery,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入商品编号",clearable:""},model:{value:e.defaultListQuery.productSn,callback:function(t){e.$set(e.defaultListQuery,"productSn",t)},expression:"defaultListQuery.productSn"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n                    重置\n                ")]),e._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n                    查询搜索\n                ")])],1)],1)],1),e._v(" "),a("MyTable",{ref:"childRef",attrs:{listApi:e.getBuyerList,operationList:e.operationList,searchObj:e.searchData},scopedSlots:e._u([{key:"goodsPic",fn:function(e){var t=e.row;return[a("img",{staticStyle:{width:"100px"},attrs:{src:t.productPic,alt:""}})]}},{key:"status",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(e.getStatusType(n.status,n.sellerOfferPrice,n.endOrderId)))])]}},{key:"productName",fn:function(t){var n=t.row;return[a("div",{staticClass:"text_linTwo",attrs:{title:n.productName}},[e._v("\n                "+e._s(n.productName)+"\n            ")])]}},{key:"createTime",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(e.util.timeFormat(n.createTime)))])]}},{key:"btns",fn:function(t){var n=t.row;return[a("div",[e.canDel(n)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteGoods(n)}}},[e._v("删除")]):e._e(),e._v(" "),e.canRefoundBack(n)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.refoundBack(n)}}},[e._v("撤销议价")]):e._e(),e._v(" "),1==n.status||2==n.status&&n.sellerOfferPrice?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.payProduct(n)}}},[e._v("立即购买")]):e._e(),e._v(" "),-1==n.status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.payNowOrder(n)}}},[e._v("立即支付")]):e._e(),e._v(" "),2==n.status&&n.sellerOfferPrice?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.huanjiaFun(n)}}},[e._v("还价")]):e._e()],1)]}}])}),e._v(" "),a("el-dialog",{attrs:{title:"还价",visible:e.dialogVisibleInden,width:"500px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisibleInden=t}}},[a("div",[a("el-input",{staticClass:"editNum",attrs:{type:"number",placeholder:"请输入还价金额",clearable:""},model:{value:e.counteroffer_price,callback:function(t){e.counteroffer_price=t},expression:"counteroffer_price"}}),e._v(" "),a("div",[e._v(" 如卖家同意还价金额，不能无责取消")])],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisibleInden=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.counterPriceSure}},[e._v("确 定")])],1)])],1)},staticRenderFns:[]};var d=a("VU/8")(u,l,!1,function(e){a("GUXj")},null,null);t.default=d.exports}});