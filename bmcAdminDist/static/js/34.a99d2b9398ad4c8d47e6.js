webpackJsonp([34],{"5Wss":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=r("fZjL"),i=r.n(a),o=r("Xxa5"),s=r.n(o),n=r("exGp"),l=r.n(n),u=r("//Fk"),c=r.n(u),m=r("mtWM"),d=r.n(m),p=r("ZW30"),h=r("TrAZ"),f=r.n(h),v=r("0xDb"),g={name:"SingleUpload",props:{value:String,isDeletWaterList:{type:Number,default:1},height:{type:String,default:"100"},width:{type:String,default:"100"}},data:function(){return{dataObj:{policy:"",signature:"",key:"",ossaccessKeyId:"",dir:"",host:""},dialogVisible:!1,useOss:!0,ossUploadUrl:"https://images2.kkzhw.com",minioUploadUrl:"https://api2.kkzhw.com/mall-portal/minio/upload"}},computed:{imageUrl:function(){return this.value},imageName:function(){return null!=this.value&&""!==this.value?this.value.substr(this.value.lastIndexOf("/")+1):null},fileList:function(){return[{name:this.imageName,url:this.imageUrl}]},showFileList:{get:function(){return null!==this.value&&""!==this.value&&void 0!==this.value},set:function(e){}}},methods:{compressionImage:function(e){return new c.a(function(t,r){new f.a({file:e,quality:.2,convertSize:1e5,redressOrientation:!1,beforeCompress:function(e){},success:function(e){var r=new File([e],e.name,{type:e.type});t(r)},error:function(e){r(e)}})})},imgToCanvas:function(e){var t=this;return l()(s.a.mark(function r(){var a,i;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return(a=document.createElement("img")).setAttribute("src",e),t.next=4,new c.a(function(e){return a.onload=e});case 4:return(i=document.createElement("canvas")).width=a.width,i.height=a.height,i.getContext("2d").drawImage(a,0,0),t.abrupt("return",i);case 9:case"end":return t.stop()}},r,t)}))()},addWatermark:function(e,t){var r=this;return l()(s.a.mark(function t(){var a,i;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a=e.getContext("2d"),i=a.createPattern(r.$refs.waterImg,"repeat"),a.fillStyle=i,a.fillRect(0,0,e.width,e.height),t.abrupt("return",e);case 5:case"end":return t.stop()}},t,r)}))()},convasToImg:function(e){var t=new Image;return t.src=e.toDataURL("image/png"),t},emitInput:function(e){this.$emit("input",e)},handleRemove:function(e,t){this.emitInput("")},handlePreview:function(e){this.dialogVisible=!0},customUpload:function(e){var t=this,r=new FormData,a=this.dataObj;i()(a).forEach(function(e){"fileName"!==e&&r.append(e,a[e])}),r.append("success_action_status","200"),r.append("file",e,e.name),d.a.post(this.ossUploadUrl,r,{headers:{"Content-Type":"multipart/form-data"}}).then(function(r){200===r.status&&t.handleUploadSuccess(e)})},addWater:function(e){var t=this;return new c.a(function(r,a){var i;1!==t.isDeletWaterList?Object(v.c)(e,(i=l()(s.a.mark(function a(i){var o,n,l,u;return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,t.imgToCanvas(i);case 2:return o=a.sent,a.next=5,t.addWatermark(o);case 5:n=a.sent,l=t.convasToImg(n),u=Object(v.a)(l.src,e.name),r(u);case 9:case"end":return a.stop()}},a,t)})),function(e){return i.apply(this,arguments)})):r(e)})},rename:function(e,t){return new File([e],t,{type:e.type})},beforeUpload:function(e){var t=this;return l()(s.a.mark(function r(){var a,i,o,n,l;return s.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return a=e.target.files,i=a[0],o=t,r.next=5,Object(p.a)().then(function(e){var r=i.name.split(".").pop(),a=e.data.fileName;return o.dataObj.policy=e.data.policy,o.dataObj.signature=e.data.signature,o.dataObj.ossaccessKeyId=e.data.accessKeyId,o.dataObj.key=e.data.dir+"/"+a+"."+r,o.dataObj.dir=e.data.dir,o.dataObj.host=e.data.host,t.rename(i,a+"."+r)}).catch(function(e){return console.log(e),!1});case 5:if(n=r.sent){r.next=8;break}return r.abrupt("return",!1);case 8:return r.next=10,t.addWater(n);case 10:return l=r.sent,r.abrupt("return",new c.a(function(e,r){t.useOss?t.customUpload(l):e(l)}));case 12:case"end":return r.stop()}},r,t)}))()},handleUploadSuccess:function(e){this.showFileList=!0,this.fileList.pop();var t=void 0;this.useOss&&(t=this.dataObj.host+"/"+this.dataObj.dir+"/"+e.name),this.fileList.push({name:e.name,url:t}),this.emitInput(this.fileList[0].url),this.$refs.upload.value=""}}},b={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{style:{height:e.height+"px"}},[e.value?a("div",{staticStyle:{position:"relative"},style:{width:e.width+"px",height:e.height+"px"}},[a("el-image",{style:{width:e.width+"px",height:e.height+"px"},attrs:{src:e.value,"preview-src-list":[e.value]}}),e._v(" "),a("i",{staticClass:"el-icon-delete del",on:{click:function(t){return e.handleRemove(e.index)}}})],1):e._e(),e._v(" "),e.value?e._e():a("div",{staticClass:"uploadBoxBorder",staticStyle:{position:"relative"},style:{width:e.width+"px",height:e.height+"px"}},[a("i",{staticClass:"el-icon-plus"}),e._v(" "),a("input",{ref:"upload",staticClass:"picUpload_btn",attrs:{type:"file",accept:"image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"},on:{change:e.beforeUpload}})]),e._v(" "),a("el-dialog",{attrs:{visible:e.dialogVisible,"append-to-body":!0},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("img",{attrs:{src:e.fileList[0].url,width:"100%",alt:""}})]),e._v(" "),a("img",{ref:"waterImg",staticStyle:{height:"0",width:"0"},attrs:{src:r("2w58")}})],1)},staticRenderFns:[]};var F=r("VU/8")(g,b,!1,function(e){r("5l59")},"data-v-e7fba470",null).exports,x=r("Lfj9"),w={components:{singleUploadImg:F},data:function(){return{qcsy2:0,hasImgType:!1,ruleForm:{shopName:"",shopAddress:"",domainRecordNumber:"",shopDomain:"",companyName:"",shopIntroduction:"",taxpayerId:"",desc:"",serviceIntroduction:"",afterSalesIntroduction:""},rules:{name:[{required:!0,message:"请输入店铺名称",trigger:"blur"}]}}},mounted:function(){this.getDetail()},methods:{getDetail:function(){var e=this;Object(x.H)().then(function(t){console.log(t,2222222),e.ruleForm=t.data})},submitForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return console.log("error submit!!"),!1;Object(x._19)(t.ruleForm.id,t.ruleForm).then(function(e){200===e.code&&t.$message.success("修改成功")})})},resetForm:function(e){this.getDetail()}}},y={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{padding:"20px"}},[r("div",{staticClass:"headerDetail"},[r("singleUploadImg",{model:{value:e.ruleForm.shopLogo,callback:function(t){e.$set(e.ruleForm,"shopLogo",t)},expression:"ruleForm.shopLogo"}}),e._v(" "),r("div",{staticStyle:{"margin-left":"10px"}},[r("div",[e._v(e._s(e.ruleForm.shopName))]),e._v(" "),r("div",[r("span",{staticStyle:{color:"#9a9a9a"}},[e._v("店铺ID：")]),e._v(e._s(e.ruleForm.shopNumber))])])],1),e._v(" "),r("div",{staticClass:"shopTitle"},[e._v("\n        企业信息\n    ")]),e._v(" "),r("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",staticStyle:{"margin-top":"20px"},attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[r("div",{staticClass:"formLineBox"},[r("el-form-item",{attrs:{label:"企业全称"}},[r("el-input",{attrs:{placeholder:"请输入企业全称"},model:{value:e.ruleForm.companyName,callback:function(t){e.$set(e.ruleForm,"companyName",t)},expression:"ruleForm.companyName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"纳税人识别号"}},[r("el-input",{attrs:{placeholder:"请输入纳税人识别号"},model:{value:e.ruleForm.taxpayerId,callback:function(t){e.$set(e.ruleForm,"taxpayerId",t)},expression:"ruleForm.taxpayerId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"店主微信号"}},[r("el-input",{attrs:{placeholder:"请输入微信号"},model:{value:e.ruleForm.wechat,callback:function(t){e.$set(e.ruleForm,"wechat",t)},expression:"ruleForm.wechat"}})],1)],1),e._v(" "),r("div",{staticClass:"formLineBox"},[r("el-form-item",{attrs:{label:"店主支付宝"}},[r("el-input",{attrs:{disabled:"",placeholder:"请输入店主支付宝"},model:{value:e.ruleForm.name,callback:function(t){e.$set(e.ruleForm,"name",t)},expression:"ruleForm.name"}})],1)],1),e._v(" "),r("el-form-item",{staticStyle:{width:"460px"},attrs:{disabled:"",label:"营业执照"}},[r("singleUploadImg",{attrs:{disabled:""},model:{value:e.ruleForm.pic,callback:function(t){e.$set(e.ruleForm,"pic",t)},expression:"ruleForm.pic"}})],1)],1),e._v(" "),r("div",{staticClass:"shopTitle"},[e._v("\n        店铺信息\n    ")]),e._v(" "),r("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",staticStyle:{"margin-top":"20px"},attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[r("div",{staticClass:"formLineBox"},[r("el-form-item",{attrs:{label:"店铺名称",prop:"shopName"}},[r("el-input",{attrs:{placeholder:"请输入店铺名称"},model:{value:e.ruleForm.shopName,callback:function(t){e.$set(e.ruleForm,"shopName",t)},expression:"ruleForm.shopName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"店铺域名"}},[r("el-input",{attrs:{disabled:""},model:{value:e.ruleForm.shopDomain,callback:function(t){e.$set(e.ruleForm,"shopDomain",t)},expression:"ruleForm.shopDomain"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"域名备案号"}},[r("el-input",{attrs:{disabled:"",placeholder:"请填写域名对应的工商部备案号"},model:{value:e.ruleForm.domainRecordNumber,callback:function(t){e.$set(e.ruleForm,"domainRecordNumber",t)},expression:"ruleForm.domainRecordNumber"}})],1)],1),e._v(" "),r("div",{staticClass:"formLineBox"},[r("el-form-item",{attrs:{label:"店铺地址"}},[r("el-input",{attrs:{placeholder:"请输入店铺地址"},model:{value:e.ruleForm.shopAddress,callback:function(t){e.$set(e.ruleForm,"shopAddress",t)},expression:"ruleForm.shopAddress"}})],1),e._v(" "),r("el-form-item",{staticStyle:{width:"350px"},attrs:{label:"热门账号图"}},[r("singleUploadImg",{model:{value:e.ruleForm.hotProductPic,callback:function(t){e.$set(e.ruleForm,"hotProductPic",t)},expression:"ruleForm.hotProductPic"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"估价回收图"}},[r("singleUploadImg",{model:{value:e.ruleForm.recyclingPic,callback:function(t){e.$set(e.ruleForm,"recyclingPic",t)},expression:"ruleForm.recyclingPic"}})],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"店铺简介"}},[r("el-input",{staticStyle:{width:"600px"},attrs:{type:"textarea",autosize:{minRows:5,maxRows:10},placeholder:"清输入店铺简介"},model:{value:e.ruleForm.shopIntroduction,callback:function(t){e.$set(e.ruleForm,"shopIntroduction",t)},expression:"ruleForm.shopIntroduction"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"服务介绍"}},[r("el-input",{staticStyle:{width:"600px"},attrs:{type:"textarea",autosize:{minRows:5,maxRows:10},placeholder:"清输入服务介绍"},model:{value:e.ruleForm.serviceIntroduction,callback:function(t){e.$set(e.ruleForm,"serviceIntroduction",t)},expression:"ruleForm.serviceIntroduction "}})],1),e._v(" "),r("el-form-item",{attrs:{label:"安全售后"}},[r("el-input",{staticStyle:{width:"600px"},attrs:{type:"textarea",autosize:{minRows:5,maxRows:10},placeholder:"清输入安全售后"},model:{value:e.ruleForm.afterSalesIntroduction,callback:function(t){e.$set(e.ruleForm,"afterSalesIntroduction",t)},expression:"ruleForm.afterSalesIntroduction "}})],1),e._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("确认修改")]),e._v(" "),r("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("取消")])],1)],1)],1)},staticRenderFns:[]};var _=r("VU/8")(w,y,!1,function(e){r("pUPK")},"data-v-e7ef005e",null);t.default=_.exports},"5l59":function(e,t){},pUPK:function(e,t){}});