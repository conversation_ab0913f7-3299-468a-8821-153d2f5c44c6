webpackJsonp([33],{"Gpb+":function(t,e,r){"use strict";var a=r("mvHQ"),i=r.n(a),o=r("woOf"),n=r.n(o),l=r("/ekJ"),s=r.n(l),u=(r("mERs"),r("mRsl")),c=r("KhLR"),d=r("3idm"),p=r("TZVV"),f=r("M4fF"),m=r.n(f),v={keywords:"",icon:"",description:"",name:"",navStatus:0,parentId:0,productUnit:"",showStatus:0,sort:0,productAttributeIdList:[],categoryTag:""},h={name:"ProductCateDetail",components:{SingleUpload:p.a},filters:{filterLabelFilter:function(t){return 0===t?"筛选属性：":""}},props:{isEdit:{type:Boolean,default:!1}},data:function(){return{categoryTagOptions:[{value:"mobile_game",label:"手游"},{value:"pc_game",label:"端游"}],navStatusList:[{id:0,name:"联系客服"},{id:1,name:"自主截图+官方截图"},{id:2,name:"自主截图"},{id:3,name:"官方截图"},{id:4,name:"无需截图"}],productUnitList:[{id:1,name:"NEW"},{id:2,name:"HOT"},{id:0,name:"无"}],attriCateId:"",productCate:n()({},v),selectProductCateList:[],rules:{name:[{required:!0,message:"请输入品牌名称",trigger:"blur"},{min:2,max:140,message:"长度在 2 到 140 个字符",trigger:"blur"}]},filterAttrs:[],filterAttrs2:[],filterProductAttrList:[{value:[]}]}},created:function(){},mounted:function(){var t=this;this.initJsonEdit(),this.initJsonEdit2(),this.isEdit?(Object(u.f)(this.$route.query.id).then(function(e){t.productCate=e.data,t.getSelectProductCateList(),t.getProductAttrCateList(),t.setCustom()}),Object(d.e)(this.$route.query.id).then(function(e){if(null!=e.data&&e.data.length>0){t.filterProductAttrList=[];for(var r=0;r<e.data.length;r++)t.filterProductAttrList.push({key:Date.now()+r,value:[e.data[r].attributeCategoryId,e.data[r].attributeId]})}})):(this.productCate=n()({},v),this.getSelectProductCateList(),this.getProductAttrCateList())},methods:{setCustom:function(){var t=this.productCate.custom||"{}";t=JSON.parse(t),this.editor.set(t);var e=this.productCate.custom2||"{}";e=JSON.parse(e),this.editor2.set(e)},initJsonEdit:function(){var t=document.getElementById("jsoneditor");this.editor=new s.a(t,{mode:"text",indentation:2,enableSort:!1,enableTransform:!1})},initJsonEdit2:function(){var t=document.getElementById("jsoneditor2");this.editor2=new s.a(t,{mode:"text",indentation:2,enableSort:!1,enableTransform:!1})},onChange:function(t){this.filterProductAttrList[0].value=[],this.filterAttrs=this.savefilterAttrs.filter(function(e){return e.value===t})},getSelectProductCateList:function(){var t=this;Object(u.d)(0,{pageSize:200,pageNum:1}).then(function(e){t.selectProductCateList=e.data.list,t.selectProductCateList.unshift({id:0,name:"无上级分类"})})},getProductAttrCateList:function(){var t=this;Object(c.d)().then(function(e){for(var r=e.data,a=0;a<r.length;a++){var i=r[a],o=[];if(null!=i.productAttributeList&&i.productAttributeList.length>0)for(var n=0;n<i.productAttributeList.length;n++)o.push({label:i.productAttributeList[n].name,value:i.productAttributeList[n].id});t.filterAttrs.push({label:i.name,value:i.id,children:o}),t.savefilterAttrs=m.a.cloneDeep(t.filterAttrs),t.filterAttrs2.push({key:i.name,value:i.id})}t.isEdit&&(t.filterAttrs=t.savefilterAttrs.filter(function(e){return e.value==t.productCate.attriCateId}))})},getProductAttributeIdList:function(){for(var t=[],e=0;e<this.filterProductAttrList.length;e++){var r=this.filterProductAttrList[e];null!==r.value&&2===r.value.length&&t.push(r.value[1])}return t},onSubmit:function(t){var e=this;this.$refs[t].validate(function(r){if(!r)return e.$message({message:"验证失败",type:"error",duration:1e3}),!1;e.$confirm("是否提交数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var r=e.editor.get(),a=e.editor2.get();e.productCate.custom=i()(r),e.productCate.custom2=i()(a),e.isEdit?(e.productCate.productAttributeIdList=e.getProductAttributeIdList(),Object(u.i)(e.$route.query.id,e.productCate).then(function(t){e.$message({message:"修改成功",type:"success",duration:1e3}),e.$router.back()})):(e.productCate.productAttributeIdList=e.getProductAttributeIdList(),Object(u.a)(e.productCate).then(function(r){e.$refs[t].resetFields(),e.resetForm(t),e.$message({message:"提交成功",type:"success",duration:1e3})}))})})},resetForm:function(t){this.$refs[t].resetFields(),this.productCate=n()({},v),this.getSelectProductCateList(),this.filterProductAttrList=[{value:[]}]},removeFilterAttr:function(t){if(1!==this.filterProductAttrList.length){var e=this.filterProductAttrList.indexOf(t);-1!==e&&this.filterProductAttrList.splice(e,1)}else this.$message({message:"至少要留一个",type:"warning",duration:1e3})},handleAddFilterAttr:function(){this.filterProductAttrList.push({value:null,key:Date.now()})}}},b={render:function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-card",{staticClass:"form-container",attrs:{shadow:"never"}},[r("el-form",{ref:"productCateFrom",attrs:{model:t.productCate,rules:t.rules,"label-width":"150px"}},[r("el-form-item",{attrs:{label:"分类代号："}},[r("el-input",{attrs:{autosize:!0,type:"textarea"},model:{value:t.productCate.keywords,callback:function(e){t.$set(t.productCate,"keywords",e)},expression:"productCate.keywords"}})],1),t._v(" "),74==t.productCate.parentId?r("el-form-item",{attrs:{label:"游戏分类：",prop:"categoryTag"}},[r("el-select",{attrs:{placeholder:"请选择"},model:{value:t.productCate.categoryTag,callback:function(e){t.$set(t.productCate,"categoryTag",e)},expression:"productCate.categoryTag"}},t._l(t.categoryTagOptions,function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1):t._e(),t._v(" "),r("el-form-item",{attrs:{label:"分类名称：",prop:"name"}},[r("el-input",{model:{value:t.productCate.name,callback:function(e){t.$set(t.productCate,"name",e)},expression:"productCate.name"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"上级分类："}},[r("el-select",{attrs:{placeholder:"请选择分类"},model:{value:t.productCate.parentId,callback:function(e){t.$set(t.productCate,"parentId",e)},expression:"productCate.parentId"}},t._l(t.selectProductCateList,function(t){return r("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"分类属性："}},[r("el-select",{attrs:{placeholder:"请选择"},model:{value:t.productCate.productUnit,callback:function(e){t.$set(t.productCate,"productUnit",e)},expression:"productCate.productUnit"}},t._l(t.productUnitList,function(t){return r("el-option",{key:t.id,attrs:{label:t.name,value:t.name}})}),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"排序："}},[r("el-input",{model:{value:t.productCate.sort,callback:function(e){t.$set(t.productCate,"sort",e)},expression:"productCate.sort"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"是否发布："}},[r("el-radio-group",{model:{value:t.productCate.showStatus,callback:function(e){t.$set(t.productCate,"showStatus",e)},expression:"productCate.showStatus"}},[r("el-radio",{attrs:{label:1}},[t._v("是")]),t._v(" "),r("el-radio",{attrs:{label:0}},[t._v("否")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"截图方式："}},[r("el-select",{model:{value:t.productCate.navStatus,callback:function(e){t.$set(t.productCate,"navStatus",e)},expression:"productCate.navStatus"}},t._l(t.navStatusList,function(t){return r("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"分类图标："}},[r("single-upload",{model:{value:t.productCate.icon,callback:function(e){t.$set(t.productCate,"icon",e)},expression:"productCate.icon"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"筛选属性"}},[r("el-select",{staticStyle:{width:"280px"},on:{change:t.onChange},model:{value:t.productCate.attriCateId,callback:function(e){t.$set(t.productCate,"attriCateId",e)},expression:"productCate.attriCateId"}},t._l(t.filterAttrs2,function(e){return r("el-option",{key:e.value,attrs:{label:e.key,value:e.value,disabled:1!==t.filterProductAttrList.length}})}),1)],1),t._v(" "),t._l(t.filterProductAttrList,function(e,a){return r("el-form-item",{key:e.key},[r("el-cascader",{staticStyle:{width:"280px"},attrs:{options:t.filterAttrs,clearable:""},model:{value:e.value,callback:function(r){t.$set(e,"value",r)},expression:"filterProductAttr.value"}}),t._v(" "),r("el-button",{staticStyle:{"margin-left":"20px"},on:{click:function(r){return r.preventDefault(),t.removeFilterAttr(e)}}},[t._v("删除")])],1)}),t._v(" "),r("el-form-item",[r("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.handleAddFilterAttr()}}},[t._v("新增")])],1),t._v(" "),r("el-form-item",{attrs:{label:"游戏资费："}},[r("el-input",{model:{value:t.productCate.description,callback:function(e){t.$set(t.productCate,"description",e)},expression:"productCate.description"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"扩展字段："}},[r("div",{staticStyle:{width:"900px",height:"400px"},attrs:{id:"jsoneditor"}})]),t._v(" "),r("el-form-item",{attrs:{label:"扩展字段2："}},[r("div",{staticStyle:{width:"900px",height:"400px"},attrs:{id:"jsoneditor2"}})]),t._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("productCateFrom")}}},[t._v("提交")]),t._v(" "),t.isEdit?t._e():r("el-button",{on:{click:function(e){return t.resetForm("productCateFrom")}}},[t._v("重置")])],1)],2)],1)},staticRenderFns:[]};var C=r("VU/8")(h,b,!1,function(t){r("WhN+")},"data-v-d65712f6",null);e.a=C.exports},"WhN+":function(t,e){},holW:function(t,e){},"tU/9":function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a={name:"addProductCate",components:{ProductCateDetail:r("Gpb+").a}},i={render:function(){var t=this.$createElement;return(this._self._c||t)("product-cate-detail",{attrs:{"is-edit":!1}})},staticRenderFns:[]};var o=r("VU/8")(a,i,!1,function(t){r("holW")},null,null);e.default=o.exports}});