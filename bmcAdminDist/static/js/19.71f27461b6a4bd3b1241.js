webpackJsonp([19,41],{"RD+8":function(t,e){},ggMu:function(t,e,l){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=l("woOf"),a=l.n(r),s=l("xT6B"),n=l("qtDR"),o={pageNum:1,pageSize:20,memberUsername:null,orderSn:null,returnOrderSn:null,receiverKeyword:null,status:null,createTime:null,handleMan:null,handleTime:null,productSn:null},i=[{label:"待处理",value:0},{label:"已处理",value:1},{label:"已完成",value:2},{label:"已拒绝",value:3}],u={name:"returnApplyList",components:{applyDetail:l("n2Mw").default},data:function(){return{applyDetailDialogId:"",showApplyDetailDialog:!1,listQuery:a()({},o),statusOptions:a()({},i),list:null,total:null,listLoading:!1,multipleSelection:[],operateType:1,operateOptions:[{label:"批量删除",value:1}]}},created:function(){this.getList()},filters:{formatTime:function(t){if(null==t||""===t)return"N/A";var e=new Date(t);return Object(s.a)(e,"YYYY-MM-DD HH:mm:ss")},formatStatus:function(t){for(var e=0;e<i.length;e++)if(t===i[e].value)return i[e].label},formatReturnAmount:function(t){return t.returnAmount||0}},methods:{handleSelectionChange:function(t){this.multipleSelection=t},handleResetSearch:function(){this.listQuery=a()({},o)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleViewDetail:function(t,e){this.showApplyDetailDialog=!0,this.applyDetailDialogId=e.id},handleBatchOperate:function(){var t=this;null==this.multipleSelection||this.multipleSelection.length<1?this.$message({message:"请选择要操作的申请",type:"warning",duration:1e3}):1===this.operateType&&this.$confirm("是否要进行删除操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){for(var e=new URLSearchParams,l=[],r=0;r<t.multipleSelection.length;r++)l.push(t.multipleSelection[r].id);e.append("ids",l),Object(n.a)(e).then(function(e){t.getList(),t.$message({type:"success",message:"删除成功!"})})})},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},getList:function(){var t=this;this.listLoading=!0,Object(n.b)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})}}},c={render:function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"app-container"},[l("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[l("div",{staticStyle:{"margin-top":"15px"}},[l("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[l("el-form-item",{attrs:{label:"买家账号："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"买家账号"},model:{value:t.listQuery.memberUsername,callback:function(e){t.$set(t.listQuery,"memberUsername",e)},expression:"listQuery.memberUsername"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"退款单号："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"退款单号"},model:{value:t.listQuery.returnOrderSn,callback:function(e){t.$set(t.listQuery,"returnOrderSn",e)},expression:"listQuery.returnOrderSn"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"商品编号："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"商品编号"},model:{value:t.listQuery.productSn,callback:function(e){t.$set(t.listQuery,"productSn",e)},expression:"listQuery.productSn"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"订单编号："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"订单编号"},model:{value:t.listQuery.orderSn,callback:function(e){t.$set(t.listQuery,"orderSn",e)},expression:"listQuery.orderSn"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"处理状态："}},[l("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"申请时间："}},[l("el-date-picker",{staticClass:"input-width",attrs:{"value-format":"yyyy-MM-dd",type:"date",placeholder:"请选择时间"},model:{value:t.listQuery.createTime,callback:function(e){t.$set(t.listQuery,"createTime",e)},expression:"listQuery.createTime"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"操作人员："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"全部"},model:{value:t.listQuery.handleMan,callback:function(e){t.$set(t.listQuery,"handleMan",e)},expression:"listQuery.handleMan"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"处理时间："}},[l("el-date-picker",{staticClass:"input-width",attrs:{"value-format":"yyyy-MM-dd",type:"date",placeholder:"请选择时间"},model:{value:t.listQuery.handleTime,callback:function(e){t.$set(t.listQuery,"handleTime",e)},expression:"listQuery.handleTime"}})],1),t._v(" "),l("el-form-item",[l("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询搜索\n          ")]),t._v(" "),l("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),l("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[l("i",{staticClass:"el-icon-tickets"}),t._v(" "),l("span",[t._v("数据列表")])]),t._v(" "),l("div",{staticClass:"table-container"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"returnApplyTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""},on:{"selection-change":t.handleSelectionChange}},[l("el-table-column",{attrs:{label:"商品编号",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productSn))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"订单号",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.orderSn))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"退款单号",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.returnOrderSn))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"申请时间",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatTime")(e.row.createTime)))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"用户账号",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.memberUsername))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"退款金额",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("￥"+t._s(t._f("formatReturnAmount")(e.row)))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"退款状态",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatStatus")(e.row.status)))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"处理时间",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatTime")(e.row.handleTime)))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"操作",fixed:"right",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-button",{attrs:{size:"mini"},on:{click:function(l){return t.handleViewDetail(e.$index,e.row)}}},[t._v("查看详情")])]}}])})],1)],1),t._v(" "),l("div",{staticClass:"pagination-container"},[l("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":t.listQuery.pageNum,"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)}}})],1),t._v(" "),l("el-dialog",{attrs:{visible:t.showApplyDetailDialog,width:"70%"},on:{"update:visible":function(e){t.showApplyDetailDialog=e}}},[t.showApplyDetailDialog?l("applyDetail",{attrs:{id:t.applyDetailDialogId},on:{closeDialog:function(e){t.showApplyDetailDialog=!1}}}):t._e()],1)],1)},staticRenderFns:[]};var d=l("VU/8")(u,c,!1,function(t){l("RD+8")},"data-v-2c5829a8",null);e.default=d.exports},isPl:function(t,e){},n2Mw:function(t,e,l){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=l("woOf"),a=l.n(r),s=l("c9K3"),n=l("qtDR"),o=l("vLgD");var i=l("xT6B"),u=l("zL8q"),c={companyAddressId:null,handleMan:"admin",handleNote:null,receiveMan:"admin",receiveNote:null,returnAmount:0,status:0},d={id:null,orderId:null,companyAddressId:null,productId:null,orderSn:null,createTime:null,memberUsername:null,returnAmount:null,returnName:null,returnPhone:null,status:null,handleTime:null,productPic:null,productName:null,productBrand:null,productAttr:null,productCount:null,productPrice:null,productRealPrice:null,reason:null,description:null,proofPics:null,handleNote:null,handleMan:null,receiveMan:null,receiveTime:null,receiveNote:null},p={name:"returnApplyDetail",props:{id:{type:[String,Number],defautl:""}},components:{orderDetail:s.default},data:function(){return{id:null,orderReturnApply:a()({},d),productList:null,proofPics:null,updateStatusParam:a()({},c),companyAddressList:null,orderDetailId:"",showOrderDetail:!1}},created:function(){this.getDetail()},computed:{totalAmount:function(){return null!=this.orderReturnApply?this.orderReturnApply.productRealPrice*this.orderReturnApply.productCount:0},currentAddress:function(){var t=this.updateStatusParam.companyAddressId;if(null==this.companyAddressList)return{};for(var e=0;e<this.companyAddressList.length;e++){var l=this.companyAddressList[e];if(l.id===t)return l}return null}},filters:{formatStatus:function(t){return 0===t?"待处理":1===t?"已处理":2===t?"已完成":"已拒绝"},formatTime:function(t){if(null==t||""===t)return"N/A";var e=new Date(t);return Object(i.a)(e,"YYYY-MM-DD HH:mm:ss")},formatRegion:function(t){var e=t.province;return null!=t.city&&(e+="  "+t.city),e+="  "+t.region}},methods:{handleViewOrder:function(){this.orderDetailId=this.orderReturnApply.orderId,this.showOrderDetail=!0},getDetail:function(){var t=this;Object(n.c)(this.id).then(function(e){t.orderReturnApply=e.data,t.productList=[],t.productList.push(t.orderReturnApply),null!=t.orderReturnApply.proofPics&&(t.orderReturnApply.proofPics?t.proofPics=t.orderReturnApply.proofPics.split(","):t.proofPics=[]),t.updateStatusParam.returnAmount=t.orderReturnApply.returnAmount,1!==t.orderReturnApply.status&&2!==t.orderReturnApply.status||(t.updateStatusParam.companyAddressId=t.orderReturnApply.companyAddressId)})},getCompanyAddressList:function(){var t=this;Object(o.a)({url:"/companyAddress/list",method:"get"}).then(function(e){console.log("getCompanyAddressList()"),t.companyAddressList=e.data;for(var l=0;l<t.companyAddressList.length;l++)1===t.companyAddressList[l].receiveStatus&&0===t.orderReturnApply.status&&(t.updateStatusParam.companyAddressId=t.companyAddressList[l].id)})},handleUpdateStatus:function(t){var e=this;this.updateStatusParam.status=t,this.$confirm("是否要进行此操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=u.Loading.service({fullscreen:!0});Object(n.d)(e.id,e.updateStatusParam).then(function(t){e.$message({type:"success",message:"操作成功!",duration:1e3}),e.$emit("closeDialog")}).finally(function(){t.close()})})}}},m={render:function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"detail-container"},[l("el-card",{attrs:{shadow:"never"}},[l("span",{staticClass:"font-title-medium"},[t._v("退货商品")]),t._v(" "),l("el-table",{ref:"productTable",staticClass:"standard-margin",attrs:{border:"",data:t.productList}},[l("el-table-column",{attrs:{label:"商品图片",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[l("img",{staticStyle:{height:"80px"},attrs:{src:t.row.productPic}})]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"商品名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("span",{staticClass:"font-small"},[t._v(t._s(e.row.productName))]),l("br"),t._v(" "),l("span",{staticClass:"font-small"},[t._v("品牌："+t._s(e.row.productBrand))])]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"价格/编号",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("span",{staticClass:"font-small"},[t._v("价格：￥"+t._s(e.row.productRealPrice))]),l("br"),t._v(" "),l("span",{staticClass:"font-small"},[t._v("编号：NO."+t._s(e.row.productId))])]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"属性",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productAttr))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"数量",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productCount))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"小计",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("￥"+t._s(t.totalAmount))]}}])})],1),t._v(" "),l("div",{staticStyle:{float:"right","margin-top":"15px","margin-bottom":"15px"}},[l("span",{staticClass:"font-title-medium"},[t._v("合计：")]),t._v(" "),l("span",{staticClass:"font-title-medium color-danger"},[t._v("￥"+t._s(t.totalAmount))])])],1),t._v(" "),l("el-card",{staticClass:"standard-margin",attrs:{shadow:"never"}},[l("span",{staticClass:"font-title-medium"},[t._v("退款单信息")]),t._v(" "),l("div",{staticClass:"form-container-border"},[l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("申请人")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.returnName||"无"))])],1)],1),t._v(" "),l("div",{staticClass:"form-container-border"},[l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("退款单号")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.returnOrderSn))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("退款状态")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t._f("formatStatus")(t.orderReturnApply.status)))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"50px","line-height":"30px"},attrs:{span:6}},[t._v("订单编号\n        ")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",staticStyle:{height:"50px"},attrs:{span:18}},[t._v("\n          "+t._s(t.orderReturnApply.orderSn)+"\n          "),l("el-button",{attrs:{type:"text",size:"small"},on:{click:t.handleViewOrder}},[t._v("查看")])],1)],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("申请时间")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t._f("formatTime")(t.orderReturnApply.createTime)))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("用户账号")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.memberUsername))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("联系电话")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.returnPhone||"无"))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("退货原因")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.reason))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("问题描述")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.description||"无"))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"100px","line-height":"80px"},attrs:{span:6}},[t._v("凭证图片\n        ")]),t._v(" "),t.proofPics&&t.proofPics.length?l("el-col",{staticClass:"form-border font-small",staticStyle:{height:"100px"},attrs:{span:18}},t._l(t.proofPics,function(t){return l("img",{staticStyle:{width:"80px",height:"80px"},attrs:{src:t}})}),0):l("el-col",{staticClass:"form-border font-small",staticStyle:{height:"100px"},attrs:{span:18}},[t._v("\n          无\n        ")])],1)],1),t._v(" "),l("div",{staticClass:"form-container-border"},[l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("订单金额")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v("￥"+t._s(t.totalAmount))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"52px","line-height":"32px"},attrs:{span:6}},[t._v("确认退款金额\n        ")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",staticStyle:{height:"52px"},attrs:{span:18}},[t._v("\n          ￥\n          "),l("el-input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{size:"small",disabled:0!==t.orderReturnApply.status},model:{value:t.updateStatusParam.returnAmount,callback:function(e){t.$set(t.updateStatusParam,"returnAmount",e)},expression:"updateStatusParam.returnAmount"}})],1)],1),t._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:3!==t.orderReturnApply.status,expression:"orderReturnApply.status !== 3"}]})],1),t._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:0!==t.orderReturnApply.status,expression:"orderReturnApply.status !== 0"}],staticClass:"form-container-border"},[l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("处理人员")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.handleMan))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("处理时间")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t._f("formatTime")(t.orderReturnApply.handleTime)))])],1),t._v(" "),l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",attrs:{span:6}},[t._v("处理备注")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[t._v(t._s(t.orderReturnApply.handleNote))])],1)],1),t._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:0===t.orderReturnApply.status,expression:"orderReturnApply.status === 0"}],staticClass:"form-container-border"},[l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"52px","line-height":"32px"},attrs:{span:6}},[t._v("处理备注")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[l("el-input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{size:"small"},model:{value:t.updateStatusParam.handleNote,callback:function(e){t.$set(t.updateStatusParam,"handleNote",e)},expression:"updateStatusParam.handleNote"}})],1)],1)],1),t._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:1===t.orderReturnApply.status,expression:"orderReturnApply.status === 1"}],staticClass:"form-container-border"},[l("el-row",[l("el-col",{staticClass:"form-border form-left-bg font-small",staticStyle:{height:"52px","line-height":"32px"},attrs:{span:6}},[t._v("收货备注")]),t._v(" "),l("el-col",{staticClass:"form-border font-small",attrs:{span:18}},[l("el-input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{size:"small"},model:{value:t.updateStatusParam.receiveNote,callback:function(e){t.$set(t.updateStatusParam,"receiveNote",e)},expression:"updateStatusParam.receiveNote"}})],1)],1)],1),t._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:0===t.orderReturnApply.status,expression:"orderReturnApply.status === 0"}],staticStyle:{"margin-top":"15px","text-align":"center"}},[l("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleUpdateStatus(1)}}},[t._v("确认退款")]),t._v(" "),l("el-button",{attrs:{type:"danger",size:"small"},on:{click:function(e){return t.handleUpdateStatus(3)}}},[t._v("拒绝退款")])],1),t._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:1===t.orderReturnApply.status,expression:"orderReturnApply.status === 1"}],staticStyle:{"margin-top":"15px","text-align":"center"}},[l("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleUpdateStatus(2)}}},[t._v("完成退款")])],1)]),t._v(" "),l("el-dialog",{attrs:{modal:!1,width:"70%",visible:t.showOrderDetail},on:{"update:visible":function(e){t.showOrderDetail=e}}},[t.showOrderDetail?l("orderDetail",{attrs:{id:t.orderDetailId}}):t._e(),t._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(e){t.showOrderDetail=!1}}},[t._v("取 消")]),t._v(" "),l("el-button",{attrs:{type:"primary"},on:{click:function(e){t.showOrderDetail=!1}}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var f=l("VU/8")(p,m,!1,function(t){l("isPl")},"data-v-14fb195a",null);e.default=f.exports},qtDR:function(t,e,l){"use strict";e.b=function(t){return Object(r.a)({url:"/returnApply/list",method:"get",params:t})},e.a=function(t){return Object(r.a)({url:"/returnApply/delete",method:"post",params:t})},e.d=function(t,e){return Object(r.a)({url:"/returnApply/update/status/"+t,method:"post",data:e})},e.c=function(t){return Object(r.a)({url:"/returnApply/"+t,method:"get"})};var r=l("vLgD")}});