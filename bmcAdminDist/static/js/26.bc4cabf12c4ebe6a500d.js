webpackJsonp([26],{Asoe:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"zimu_container spaceStart",staticStyle:{margin:"0"}},[a("div",{staticClass:"spaceBetween"},t._l(t.initZimu,function(e,i){return a("div",{key:i,staticClass:"zimu_item",class:e==t.letter?"active":"",on:{click:function(a){return t.choseLetter(e)}}},[t._v("\n      "+t._s(e)+"\n    ")])}),0)])},staticRenderFns:[]};var s=a("VU/8")({data:function(){return{initZimu:["全部游戏","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],letter:"全部游戏"}},watch:{},created:function(){},methods:{reset:function(){this.letter="全部游戏"},choseLetter:function(t){this.letter=t,this.$emit("chooseLetter",this.letter)}}},i,!1,function(t){a("fBct")},"data-v-576c08bc",null).exports,n=a("mvHQ"),c=a.n(n),r={props:{dataItem:{type:Object,default:function(){return{}}}},data:function(){return{}},watch:{},created:function(){},methods:{playPage:function(){this.cashFlagDate(),this.$emit("gamePage")},cashFlagDate:function(){var t=this.dataItem;if("逆水寒手游"!==t.name){var e=JSON.parse(localStorage.getItem("cashFlagDateArr2"));if(!e)return(e=[]).unshift(t),void localStorage.setItem("cashFlagDateArr2",c()(e));-1==e.findIndex(function(e){return e.id==t.id})&&(e.length>=8&&e.pop(),e.unshift(t),localStorage.setItem("cashFlagDateArr2",c()(e)))}}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"game_wrap",on:{click:t.playPage}},["NEW"==t.dataItem.productUnit?a("div",{staticClass:"game_wrap_new"}):t._e(),t._v(" "),a("div",{staticClass:"game_wrap_pic"},[a("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.dataItem.icon,fit:"cover"}})],1),t._v(" "),a("div",[t._v(t._s(t.dataItem.name))])])},staticRenderFns:[]};var l=a("VU/8")(r,o,!1,function(t){a("quUI")},"data-v-3d678657",null).exports,u=a("Lfj9"),m={components:{zimuList:s,gameItem:l},data:function(){return{activeName:"all",type:0,gameList:[],hotListGame:[],gameListAll:[],gameListmb:[],gameListpc:[],searchValue:"",tipFlag:!1}},mounted:function(){this.initGame()},methods:{playPage:function(t){this.$router.push({path:"/BMC-DDZX/pushAccount?productCategoryId="+t.id+"&attriCateId="+t.attriCateId+"&type=push"})},initGame:function(){var t=this;Object(u.t)().then(function(e){if(200==e.code){var a=[];e.data.forEach(function(t){1==t.index&&a.push(t)}),t.hotListGame=a,t.gameListAll=e.data,t.gameListAll.sort(function(t,e){return e.sort-t.sort}),t.gameListmb=[],t.gameListpc=[],t.gameListAll.forEach(function(e){"mobile_game"===e.categoryTag?t.gameListmb.push(e):"pc_game"===e.categoryTag&&t.gameListpc.push(e)}),t.gameList=t.copyList(t.gameListAll)}})},resetGameList:function(){0==this.type?this.gameList=this.copyList(this.gameListAll):1==this.type?this.gameList=this.copyList(this.gameListpc):2==this.type&&(this.gameList=this.copyList(this.gameListmb))},copyList:function(t){return _.cloneDeep(t)},searchValueChange:function(t){this.resetGameList(),this.gameList=this.gameList.filter(function(e){return-1!==e.name.toLowerCase().indexOf(t.toLowerCase())}),this.gameList&&0==this.gameList.length?this.tipFlag=!0:this.tipFlag=!1},handleClick:function(t,e){console.log(t.index,111111),this.type=t.index||0,this.resetGameList(),this.$refs.zimuList.reset()},choseLetter:function(t){this.resetGameList(),"全部游戏"!==t&&(this.gameList=this.gameList.filter(function(e){return(e.keywords[0]||"").toLowerCase()===t.toLowerCase()}))}}},h={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"20px",width:"100%"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"全部游戏",name:"all"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"网络游戏",name:"network"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"手机游戏",name:"iphone"}})],1),t._v(" "),a("div",{staticClass:"game_search"},[a("zimuList",{ref:"zimuList",on:{chooseLetter:t.choseLetter}}),t._v(" "),a("div",{staticClass:"game_search_input"},[a("el-input",{attrs:{placeholder:"输入游戏名称"},on:{input:t.searchValueChange},model:{value:t.searchValue,callback:function(e){t.searchValue=e},expression:"searchValue"}},[a("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},slot:"suffix"})])],1)],1),t._v(" "),a("p",{directives:[{name:"show",rawName:"v-show",value:t.tipFlag,expression:"tipFlag"}],staticStyle:{position:"absolute",right:"65px",top:"160px","font-size":"13.712px",color:"#ff720c","font-family":"PingFang SC"}},[t._v("\n        暂时没有搜索到您要的游戏\n    ")]),t._v(" "),a("div",{staticClass:"gameAll_wrap spaceStart",staticStyle:{"align-items":"flex-start",padding:"40px 0px 40px 0px"}},t._l(t.gameList,function(e,i){return a("gameItem",{key:i,attrs:{"data-item":e},on:{gamePage:function(a){return t.playPage(e)}}})}),1)],1)},staticRenderFns:[]};var p=a("VU/8")(m,h,!1,function(t){a("lIdY")},"data-v-9a3400be",null);e.default=p.exports},fBct:function(t,e){},lIdY:function(t,e){},quUI:function(t,e){}});