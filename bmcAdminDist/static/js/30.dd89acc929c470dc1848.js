webpackJsonp([30],{F34W:function(e,t){},"SvC/":function(e,t,a){"use strict";var i=a("fZjL"),l=a.n(i),n=a("pFYg"),s=a.n(n),o=a("mvHQ"),r=a.n(o),u=a("woOf"),c=a.n(u),d=a("M4fF"),p=a.n(d),h=a("mRsl"),v=a("s/Rn"),f=a("KhLR"),m=a("3idm"),b=a("ocgh"),g=(a("n97X"),a("UgCr")),y=a("TZVV"),S=a("sl7S"),_=a("5aCZ"),C=a("II7+"),w=a("0xDb"),k={1:"面板属性",2:"打造内功",3:"天赏外观",4:"普通外观",5:"其他物品"},x={albumPics:"",albumPicsJson:"[]",productAttributeValueList:[],brandId:"",productAttributeCategoryId:"",gameAccountQufu:"",description:"",qcsy2:0,pic:"",qcsy3:0,selectProductPics:[],price:"",originalPrice:"",stock:9,gameGoodsFangxin:0,gameGoodsBukuan:0,gameGoodsJiangjia:0,gameGoodsYijia:0,sort:"",publishStatus:0,gameGoodsYishou:0,gameGoodsYuyue:0,gameGoodsSaletype:1,gameCareinfoPhone:"",gameCareinfoTime:"",gameCareinfoVx:"",subjectProductRelationList:[],oldSubTitle:""},I={name:"ProductAdd",components:{SingleUpload:y.a,MultiUpload:S.a,Tinymce:_.a,tedian:C.a},props:{queryId:{type:[Number,String],default:""},cateParentId:{type:[Number,String],default:""},productCategoryId:{type:[Number,String],default:""}},data:function(){return{typeOptions:[],hasImgType:!1,tabValue:"0",active:0,value:c()({},x),hasEditCreated:!1,selectProductCateValue:[],productCateOptions:[],brandOptions:[],productAttributeCategoryOptions:[],selectProductParam:[],addProductAttrValue:"",activeHtmlName:"pc",rules:{},formLabelWidth:"200px",spanCol:12,extList:{ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]},ext5:{needShow:!1,opetionDate:[],detailOptions:[]},ext6:{needShow:!1,opetionDate:[],detailOptions:[]}},subjectTitles:["待选择","已选择"],memberId:"",memberInfo:{}}},computed:{isEdit:function(){return""!==this.queryId},productId:function(){return this.value.id},selectServiceList:{get:function(){var e=[];if(void 0===this.value.serviceIds||null==this.value.serviceIds||""===this.value.serviceIds)return e;for(var t=this.value.serviceIds.split(","),a=0;a<t.length;a++)e.push(Number(t[a]));return e},set:function(e){var t="";if(null!=e&&e.length>0){for(var a=0;a<e.length;a++)t+=e[a]+",";t.endsWith(",")&&(t=t.substr(0,t.length-1)),this.value.serviceIds=t}else this.value.serviceIds=null}},selectProductPics:{get:function(){var e=[];if(void 0===this.value.albumPics||null==this.value.albumPics||""===this.value.albumPics)return e;var t=this.value.albumPics.split(","),a=this.value.albumPicsJson||"[]";a=JSON.parse(a);for(var i=0;i<t.length;i++){var l="";if(a[i])l=a[i].name||"";this.hasImgType?e.push({url:t[i],name:l}):e.push(t[i])}return e},set:function(e){if(null==e||0===e.length)this.value.albumPics="",this.value.albumPicsJson=r()([]);else{var t="",a=[];if(e.length>0){for(var i=0;i<e.length;i++)this.hasImgType?(t+=e[i].url,a.push({url:e[i].url,name:e[i].name})):t+=e[i],i!==e.length-1&&(t+=",");this.value.albumPics=t,this.value.albumPicsJson=r()(a)}}}},selectSubject:{get:function(){var e=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return e;for(var t=0;t<this.value.subjectProductRelationList.length;t++)e.push(this.value.subjectProductRelationList[t].subjectId);return e},set:function(e){this.value.subjectProductRelationList=[];for(var t=0;t<e.length;t++)this.value.subjectProductRelationList.push({subjectId:e[t]})}}},watch:{selectProductCateValue:function(e){null!=e&&2===e.length?(this.value.productCategoryId=e[1],this.value.productCategoryName=this.getCateNameById(this.value.productCategoryId)):(this.value.productCategoryId=null,this.value.productCategoryName=null)},productId:function(e){this.isEdit&&(this.hasEditCreated||void 0!==e&&null!=e&&0!==e&&(this.handleEditCreatedInfo(),this.handleEditCreatedAttr()))}},created:function(){this.getProductCate(),this.getProductCateList(),this.getBrandList(),this.getProductAttrCateList()},methods:{getProductCate:function(){var e=this;Object(h.f)(this.productCategoryId).then(function(t){if(200==t.code){var a=t.data;if(a.custom){var i=JSON.parse(a.custom);i.albumPicsTypeOptions&&i.albumPicsTypeOptions.length&&(e.typeOptions=i.albumPicsTypeOptions,e.typeOptions=e.typeOptions.filter(function(e){return"全部图片"!=e.name}),e.hasImgType=!0)}e.isEdit&&Object(g.j)(e.queryId).then(function(t){e.value=c()({},e.value,t.data),e.oldSubTitle=e.value.subTitle,e.hasImgType&&e.transFormAlbumPicsJson(),e.memberId=e.value.memberId||"",e.saveSkuStockList=p.a.cloneDeep(e.value.skuStockList)})}})},transFormAlbumPicsJson:function(){var e=this.value.albumPicsJson||"[]",t=[];(e=JSON.parse(e)).forEach(function(e){e.hasOwnProperty("name")?t.push(e):(e.name=k[e.type]||e.type||"",t.push(e))}),this.value.albumPicsJson=r()(t)},getGender:function(e){return 1==e?"男":"女"},getConfirm:function(e){return 1===e?"是":"否"},changequfu:function(e){this.value.gameAccountQufu=e},changeTab:function(e,t){"1"===e.index&&this.getMember()},getMember:function(){var e=this;this.memberId&&Object(b.a)(this.memberId).then(function(t){e.memberInfo=t.data})},handleEditCreatedInfo:function(){null!=this.value.productCategoryId&&(this.selectProductCateValue=[],this.selectProductCateValue.push(this.value.cateParentId),this.selectProductCateValue.push(this.value.productCategoryId)),this.hasEditCreated=!0},getProductCateList:function(){var e=this;Object(h.e)().then(function(t){var a=t.data;e.productCateOptions=[];for(var i=0;i<a.length;i++){var l=[];if(null!=a[i].children&&a[i].children.length>0)for(var n=0;n<a[i].children.length;n++)l.push({label:a[i].children[n].name,value:a[i].children[n].id});e.productCateOptions.push({label:a[i].name,value:a[i].id,children:l})}e.cateParentId&&e.productCategoryId&&(e.selectProductCateValue=[],e.selectProductCateValue.push(parseInt(e.cateParentId,10)),e.selectProductCateValue.push(parseInt(e.productCategoryId,10)))})},getBrandList:function(){var e=this;Object(v.c)({pageNum:1,pageSize:200}).then(function(t){e.brandOptions=[];for(var a=t.data.list,i=0;i<a.length;i++)e.brandOptions.push({label:a[i].name,value:a[i].id})})},getCateNameById:function(e){for(var t=null,a=0;a<this.productCateOptions.length;a++)for(var i=0;i<this.productCateOptions[a].children.length;i++)if(this.productCateOptions[a].children[i].value===e)return t=this.productCateOptions[a].children[i].label;return t},handleBrandChange:function(e){for(var t="",a=0;a<this.brandOptions.length;a++)if(this.brandOptions[a].value===e){t=this.brandOptions[a].label;break}this.value.brandName=t},handleEditCreated:function(){var e=this.value.serviceIds.split(",");console.log("handleEditCreated",e);for(var t=0;t<e.length;t++)this.selectServiceList.push(Number(e[t]))},handleRemoveProductLadder:function(e,t){var a=this.value.productLadderList;1===a.length?(a.pop(),a.push({count:0,discount:0,price:0})):a.splice(e,1)},handleAddProductLadder:function(e,t){var a=this.value.productLadderList;a.length<3?a.push({count:0,discount:0,price:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handleRemoveFullReduction:function(e,t){var a=this.value.productFullReductionList;1===a.length?(a.pop(),a.push({fullPrice:0,reducePrice:0})):a.splice(e,1)},handleAddFullReduction:function(e,t){var a=this.value.productFullReductionList;a.length<3?a.push({fullPrice:0,reducePrice:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handleEditCreatedAttr:function(){null!=this.value.productAttributeCategoryId&&this.handleProductAttrChange(this.value.productAttributeCategoryId),this.hasEditCreated=!0},getProductAttrCateList:function(){var e=this;Object(f.c)({pageNum:1,pageSize:999}).then(function(t){e.productAttributeCategoryOptions=[];for(var a=t.data.list,i=0;i<a.length;i++)e.productAttributeCategoryOptions.push({label:a[i].name,value:a[i].id})})},getProductAttrList:function(e,t){var a=this,i={pageNum:1,pageSize:200,type:e};Object(m.c)(t,i).then(function(t){var i=t.data.list;if(0!==e){var l="基础信息扩展";2===e?l="账号信息扩展":3===e&&(l="其他扩展");var n={index:parseInt(e,10),label:l,needShow:i&&i.length>0},s=a.getEditAttrOptions2(i),o=[];n.opetionDate=s;for(var r=0;r<i.length;r++){var u=null;a.isEdit&&(u=a.getEditParamValue2(i[r]))&&o.push(u)}n.detailOptions=o,w.b.async2opetionDate(n.detailOptions,n.opetionDate),a.$set(a.extList,"ext"+e,n)}})},getEditAttrOptions2:function(e){return e.map(function(e){var t=1;if(1===e.inputType&&(1===e.selectType?t=2:2===e.selectType?t=3:3===e.selectType&&(t=4)),1===t)return c()({},e,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:t,iptVal:""});if(2===t)return c()({},e,{tdtype:t,value:"",is_required:0,field_type:2,inputList:e.inputList.split(",")});if(3===t){var a=[];return e.inputList.split(",").forEach(function(e){a.push({icon:"",name:e,checked:!1})}),c()({},e,{childList:a,is_required:0,field_type:2,default_word:"点击可下拉选择物品",tdtype:t,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[]})}var i=JSON.parse(e.inputList);return i.forEach(function(e){e.value=e.parent_name,e.label=e.parent_name;var t=e.childList.map(function(e){return{value:e,label:e}});e.children=t}),c()({},e,{tdtype:t,value:[],is_required:0,field_type:2,options:i})})},getEditParamValue2:function(e){var t=1;1===e.inputType&&(1===e.selectType?t=2:2===e.selectType?t=3:3===e.selectType&&(t=4));for(var a=0;a<this.value.productAttributeValueList.length;a++)if(e.id===this.value.productAttributeValueList[a].productAttributeId){var i=this.value.productAttributeValueList[a];if(1===t)return c()({},e,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:t,iptVal:i.value});if(2===t)return c()({},e,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:t,value:i.value});if(3!==t)return c()({},e,{title:e.name,tdtype:t,value:(i.value||"").split("|"),options:JSON.parse(e.inputList)});var l=function(){var a=[];""!==i.value&&(a=i.value.split(","));var l=[];return a.forEach(function(e){l.push({icon:"",name:e,checked:!0})}),{v:c()({},e,{title:e.name,tdtype:t,value:l})}}();if("object"===(void 0===l?"undefined":s()(l)))return l.v}},handleProductAttrChange:function(e){this.extList={ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]},ext5:{needShow:!1,opetionDate:[],detailOptions:[]},ext6:{needShow:!1,opetionDate:[],detailOptions:[]}},this.getProductAttrList(1,e),this.getProductAttrList(2,e),this.getProductAttrList(3,e),this.getProductAttrList(4,e),this.getProductAttrList(5,e),this.getProductAttrList(6,e)},getParamInputList:function(e){return e.split(",")},submitForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return t.$message({message:"验证失败",type:"error",duration:1e3}),!1;t.finishCommit(t.isEdit)})},filterMethod:function(e,t){return t.label.indexOf(e)>-1},cancel:function(){this.$emit("addsuc")},finishCommit:function(e){var t=this;this.$confirm("是否要提交该产品","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.value.productAttributeValueList=[];for(var a=l()(t.extList),i=0;i<a.length;i++)for(var n=a[i],s=t.extList[n].opetionDate,o=0;o<s.length;o++){var r=s[o],u=r.value||"";3===r.tdtype&&r.choosedList.length?u=r.choosedList.map(function(e){return e.name}).join(","):1===r.tdtype?u=r.iptVal:4===r.tdtype&&(u=r.value.join("|")),t.value.productAttributeValueList.push({productAttributeId:r.id,value:u,attriName:r.name,sort:r.sort,filterType:r.filterType,searchType:r.searchType,type:r.type,searchSort:r.searchSort})}t.hasImgType||(t.value.albumPicsJson="[]"),e?(t.value.subTitle&&(t.value.subTitle=t.value.subTitle.trim()),t.value.subTitle&&t.value.subTitle!==t.oldSubTitle&&"."!=t.value.subTitle[t.value.subTitle.length-1]&&(t.value.subTitle=t.value.subTitle+"."),Object(g.C)(t.queryId,t.value).then(function(e){t.$message({type:"success",message:"提交成功",duration:1e3}),t.$emit("addsuc")})):Object(g.d)(t.value).then(function(e){t.$message({type:"success",message:"提交成功",duration:1e3}),t.$emit("addsuc")})})}}},P={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{on:{"tab-click":e.changeTab},model:{value:e.tabValue,callback:function(t){e.tabValue=t},expression:"tabValue"}},[a("el-tab-pane",{attrs:{label:"基本信息"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"号主信息"}})],1),e._v(" "),a("el-form",{ref:"productForm",staticClass:"form-box",attrs:{model:e.value,rules:e.rules,"label-width":e.formLabelWidth}},[a("div",{directives:[{name:"show",rawName:"v-show",value:"0"===e.tabValue,expression:"tabValue === '0'"}]},[a("el-card",{staticClass:"card-box"},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.value.productSn,expression:"value.productSn"}],attrs:{label:"商品编号：",prop:"productSn"}},[a("el-input",{attrs:{disabled:""},model:{value:e.value.productSn,callback:function(t){e.$set(e.value,"productSn",t)},expression:"value.productSn"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"游戏类型：",prop:"productCategoryId"}},[a("el-cascader",{attrs:{options:e.productCateOptions,disabled:""},model:{value:e.selectProductCateValue,callback:function(t){e.selectProductCateValue=t},expression:"selectProductCateValue"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"游戏品牌：",prop:"brandId"}},[a("el-select",{attrs:{placeholder:"请选择品牌"},on:{change:e.handleBrandChange},model:{value:e.value.brandId,callback:function(t){e.$set(e.value,"brandId",t)},expression:"value.brandId"}},e._l(e.brandOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"区服信息：",prop:"gameAccountQufu"}},[a("el-input",{attrs:{disabled:""},model:{value:e.value.gameAccountQufu,callback:function(t){e.$set(e.value,"gameAccountQufu",t)},expression:"value.gameAccountQufu"}})],1),e._v(" "),e.extList.ext1.needShow?a("div",{staticClass:"ext1",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext1.detailOptions,"opetion-date":e.extList.ext1.opetionDate},on:{changequfu:e.changequfu}})],1):e._e()],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("商品规格")]),e._v(" "),a("el-form-item",{attrs:{label:"售价金额：",prop:"price"}},[a("el-input",{model:{value:e.value.price,callback:function(t){e.$set(e.value,"price",t)},expression:"value.price"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"心理底价：",prop:"originalPrice"}},[a("el-input",{model:{value:e.value.originalPrice,callback:function(t){e.$set(e.value,"originalPrice",t)},expression:"value.originalPrice"}})],1)],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("账号信息")]),e._v(" "),a("el-form-item",{attrs:{label:"权重排序：",prop:"sort"}},[a("el-input",{model:{value:e.value.sort,callback:function(t){e.$set(e.value,"sort",t)},expression:"value.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"访问量：",prop:"gameSysinfoReadcount"}},[a("el-input",{model:{value:e.value.gameSysinfoReadcount,callback:function(t){e.$set(e.value,"gameSysinfoReadcount",t)},expression:"value.gameSysinfoReadcount"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否已售：",prop:"stock"}},[a("el-radio-group",{model:{value:e.value.stock,callback:function(t){e.$set(e.value,"stock",t)},expression:"value.stock"}},[a("el-radio",{attrs:{label:0}},[e._v("已售")]),e._v(" "),a("el-radio",{attrs:{label:9}},[e._v("在售")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("已预订")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否降价：",prop:"gameGoodsJiangjia"}},[a("el-radio-group",{model:{value:e.value.gameGoodsJiangjia,callback:function(t){e.$set(e.value,"gameGoodsJiangjia",t)},expression:"value.gameGoodsJiangjia"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否上架：",prop:"publishStatus"}},[a("el-radio-group",{model:{value:e.value.publishStatus,callback:function(t){e.$set(e.value,"publishStatus",t)},expression:"value.publishStatus"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否一手：",prop:"gameGoodsYishou"}},[a("el-radio-group",{model:{value:e.value.gameGoodsYishou,callback:function(t){e.$set(e.value,"gameGoodsYishou",t)},expression:"value.gameGoodsYishou"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("div"),e._v(" "),a("el-form-item",{staticStyle:{width:"460px"},attrs:{label:"人物图片：",prop:"pic"}},[a("single-upload",{staticClass:"pic-box",attrs:{"is-delet-water-list":e.value.qcsy2},model:{value:e.value.pic,callback:function(t){e.$set(e.value,"pic",t)},expression:"value.pic"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否去除水印：",prop:"qcsy2"}},[a("el-radio-group",{model:{value:e.value.qcsy2,callback:function(t){e.$set(e.value,"qcsy2",t)},expression:"value.qcsy2"}},[a("el-radio",{attrs:{label:1}},[e._v("去除")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("不去除")])],1)],1),e._v(" "),a("div"),e._v(" "),a("el-form-item",{staticClass:"pics-box",attrs:{label:"图片详情："}},[a("span",[e._v("是否去除水印： ")]),e._v(" "),a("el-radio-group",{model:{value:e.value.qcsy3,callback:function(t){e.$set(e.value,"qcsy3",t)},expression:"value.qcsy3"}},[a("el-radio",{attrs:{label:1}},[e._v("去除")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("不去除")])],1),e._v(" "),a("multi-upload",{ref:"muupload",attrs:{options:e.typeOptions,"is-delet-water-list":e.value.qcsy3,hasImgType:e.hasImgType},model:{value:e.selectProductPics,callback:function(t){e.selectProductPics=t},expression:"selectProductPics"}})],1)],1),e._v(" "),e.extList.ext2.needShow?a("el-card",{staticStyle:{"margin-bottom":"20px"}},[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext2.detailOptions,"opetion-date":e.extList.ext2.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e(),e._v(" "),a("el-card",{staticClass:"card-box"},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"账号描述：",prop:"description"}},[a("el-input",{attrs:{autosize:{minRows:2,maxRows:10},type:"textarea"},model:{value:e.value.description,callback:function(t){e.$set(e.value,"description",t)},expression:"value.description"}})],1)],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("\n          保障信息\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"联系手机：",prop:"gameCareinfoPhone"}},[a("el-input",{model:{value:e.value.gameCareinfoPhone,callback:function(t){e.$set(e.value,"gameCareinfoPhone",t)},expression:"value.gameCareinfoPhone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系时间：",prop:"gameCareinfoTime"}},[a("el-input",{model:{value:e.value.gameCareinfoTime,callback:function(t){e.$set(e.value,"gameCareinfoTime",t)},expression:"value.gameCareinfoTime"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系微信：",prop:"gameCareinfoVx"}},[a("el-input",{model:{value:e.value.gameCareinfoVx,callback:function(t){e.$set(e.value,"gameCareinfoVx",t)},expression:"value.gameCareinfoVx"}})],1)],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("\n          商品标题\n        ")]),e._v(" "),a("el-form-item",{staticStyle:{width:"100%"},attrs:{prop:"subTitle"}},[a("el-input",{attrs:{autosize:{minRows:2,maxRows:10},type:"textarea"},model:{value:e.value.subTitle,callback:function(t){e.$set(e.value,"subTitle",t)},expression:"value.subTitle"}})],1)],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.tabValue,expression:"tabValue === '1'"}]},[this.memberId?a("el-card",{staticClass:"card-box"},[a("el-form-item",{attrs:{label:"昵称"}},[e._v("\n          "+e._s(e.memberInfo.nickname)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"性别"}},[e._v("\n          "+e._s(e.getGender(e.memberInfo.gender))+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"手机号"}},[e._v(" "+e._s(e.memberInfo.phone))]),e._v(" "),a("el-form-item",{attrs:{label:"备用手机号"}},[e._v("\n          "+e._s(e.value.gameCareinfoPhone2))]),e._v(" "),a("el-form-item",{attrs:{label:"IM号"}},[e._v(" "+e._s(e.memberInfo.imaccount))]),e._v(" "),a("el-form-item",{attrs:{label:"是否实名认证"}},[e._v("\n          "+e._s(e.getConfirm(e.memberInfo.realNameConfirm)))]),e._v(" "),a("el-form-item",{attrs:{label:"是否实人认证"}},[e._v("\n          "+e._s(e.getConfirm(e.memberInfo.realPersionConfirm)))]),e._v(" "),a("el-form-item",{attrs:{label:"是否包赔认证"}},[e._v("\n          "+e._s(e.getConfirm(e.memberInfo.baopeiConfirm)))])],1):a("el-card",[a("div",[e._v("后台录入，无用户信息")])])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.tabValue,expression:"tabValue === '2'"}]},[e.extList.ext4.needShow?a("el-card",[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext4.detailOptions,"opetion-date":e.extList.ext4.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e()],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===e.tabValue,expression:"tabValue === '3'"}]},[e.extList.ext5.needShow?a("el-card",[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext5.detailOptions,"opetion-date":e.extList.ext5.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e()],1)]),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("productForm")}}},[e._v("确 定")])],1)],1)},staticRenderFns:[]};var L=a("VU/8")(I,P,!1,function(e){a("teiS")},"data-v-3f4c2f33",null);t.a=L.exports},ZKjm:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("Dd8w"),l=a.n(i),n=a("woOf"),s=a.n(n),o=a("PJh5"),r=a.n(o),u=(a("IcnI"),a("UgCr")),c=a("5rT4"),d=a("caBC"),p=a("3idm"),h=a("s/Rn"),v=a("mRsl"),f=a("SvC/"),m=a("0xDb"),b={phone:null,keyword:null,pageNum:1,pageSize:20,publishStatus:null,verifyStatus:null,productSn:null,productCategoryId:null,brandId:null,priceSort:null},g=[{label:"价格从低到高",value:0},{label:"价格从高到低",value:1}],y={name:"ProductList",components:{ProductAdd:f.a},filters:{verifyStatusFilter:function(e){return 1===e?"审核通过":2===e?"未通过":"未审核"}},data:function(){return{priceSortOptions:g,modifyUserId:"",username:"",showModifyUserModel:!1,categoryName:"",listCategory:[],productCategoryId:"",cateParentId:"",verifyValue:{mark:"",ids:[]},queryId:"",showVerifyDetailModal:!1,showAddModel:!1,editSkuInfo:{dialogVisible:!1,productId:null,productSn:"",productAttributeCategoryId:null,stockList:[],productAttr:[],keyword:null},operates:[{label:"商品上架",value:"publishOn"},{label:"商品下架",value:"publishOff"},{label:"设为推荐",value:"recommendOn"},{label:"取消推荐",value:"recommendOff"},{label:"设为新品",value:"newOn"},{label:"取消新品",value:"newOff"},{label:"转移到分类",value:"transferCategory"},{label:"移入回收站",value:"recycle"}],operateType:null,listQuery:s()({},b),list:null,total:null,listLoading:!0,multipleSelection:[],productCateOptions:[],brandOptions:[],publishStatusOptions:[{value:1,label:"显示"},{value:0,label:"不显示"},{value:-2,label:"用户下架"}],verifyStatusOptions:[{value:1,label:"审核通过"},{value:0,label:"未审核"}]}},created:function(){var e=this;Object(v.d)(74,{pageNum:1,pageSize:999}).then(function(t){e.listCategory=t.data.list,m.b.sortCate(e.listCategory),e.categoryName=e.listCategory[0].name,e.productCategoryId=e.listCategory[0].id,e.initList()})},methods:{submitForm:function(){var e=this;this.username?Object(c.e)({id:this.modifyUserId,username:this.username}).then(function(t){200==t.code&&(e.$message.success("更换号主成功"),e.showModifyUserModel=!1)}):this.$message.error("请输入用户手机号")},initList:function(){this.listQuery=s()({},b),this.listQuery.productCategoryId=this.productCategoryId,this.productCategoryId=this.productCategoryId,this.cateParentId=74,this.getList(),this.getBrandList(),this.getProductCateList()},goNext:function(e){this.categoryName=e.name,this.productCategoryId=e.id,this.initList()},getState:function(e){return 1===e?"success":2===e?"error":void 0},cancelVerify:function(){this.showVerifyDetailModal=!1},submitVerify:function(e){var t=this;Object(u.e)({detail:this.verifyValue.mark,ids:this.verifyValue.ids,verifyStatus:e}).then(function(e){t.showVerifyDetailModal=!1,t.getList()})},formatTime:function(e){return e=new Date(e),r()(e).format("YYYY-MM-DD HH:mm:ss")},handleAddSuc:function(){this.showAddModel=!1,this.getList()},handleAdd:function(){this.queryId="",this.showAddModel=!0},getProductSkuSp:function(e,t){var a=JSON.parse(e.spData);return null!=a&&t<a.length?a[t].value:null},getList:function(){var e=this;this.listLoading=!0;var t=l()({},this.listQuery);(t.priceSort||0===t.priceSort)&&(t.sort="price",t.order=1==t.priceSort?"DESC":"ASC"),t.productCategoryId=this.productCategoryId,delete t.priceSort,Object(u.f)(t).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})},getBrandList:function(){var e=this;Object(h.c)({pageNum:1,pageSize:200}).then(function(t){e.brandOptions=[];for(var a=t.data.list,i=0;i<a.length;i++)e.brandOptions.push({label:a[i].name,value:a[i].id})})},getProductCateList:function(){var e=this;Object(v.e)().then(function(t){var a=t.data;e.productCateOptions=[];for(var i=0;i<a.length;i++){var l=[];if(null!=a[i].children&&a[i].children.length>0)for(var n=0;n<a[i].children.length;n++)l.push({label:a[i].children[n].name,value:a[i].children[n].id});e.productCateOptions.push({label:a[i].name,value:a[i].id,children:l})}})},handleShowSkuEditDialog:function(e,t){var a=this;this.editSkuInfo.dialogVisible=!0,this.editSkuInfo.productId=t.id,this.editSkuInfo.productSn=t.productSn,this.editSkuInfo.productAttributeCategoryId=t.productAttributeCategoryId,this.editSkuInfo.keyword=null,Object(d.a)(t.id,{keyword:this.editSkuInfo.keyword}).then(function(e){a.editSkuInfo.stockList=e.data}),null!=t.productAttributeCategoryId&&Object(p.c)(t.productAttributeCategoryId,{type:0}).then(function(e){a.editSkuInfo.productAttr=e.data.list})},handleSearchEditSku:function(){var e=this;Object(d.a)(this.editSkuInfo.productId,{keyword:this.editSkuInfo.keyword}).then(function(t){e.editSkuInfo.stockList=t.data})},handleEditSkuConfirm:function(){var e=this;null==this.editSkuInfo.stockList||this.editSkuInfo.stockList.length<=0?this.$message({message:"暂无sku信息",type:"warning",duration:1e3}):this.$confirm("是否要进行修改","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(d.b)(e.editSkuInfo.productId,e.editSkuInfo.stockList).then(function(t){e.$message({message:"修改成功",type:"success",duration:1e3}),e.editSkuInfo.dialogVisible=!1})})},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleAddProduct:function(){this.$router.push({path:"/pms/addProduct"})},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},handleSelectionChange:function(e){this.multipleSelection=e},handlePublishStatusChange:function(e,t){var a=[];a.push(t.id),this.updatePublishStatus(t.publishStatus,a)},handleNewStatusChange:function(e,t){var a=[];a.push(t.id),this.updateNewStatus(t.newStatus,a)},handleRecommendStatusChange:function(e,t){var a=[];a.push(t.id),this.updateRecommendStatus(t.recommandStatus,a)},handleResetSearch:function(){this.listQuery=s()({},b)},handleDelete:function(e,t){var a=this;this.$confirm("是否要进行删除操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=[];e.push(t.id),a.updateDeleteStatus(1,e)})},handleModifyUser:function(e,t){this.showModifyUserModel=!0,this.modifyUserId=t.id},handleUpdateProduct:function(e,t){this.showAddModel=!0,this.queryId=t.id},handleUpdateProduct2:function(e,t){this.$router.push({path:"/pms/updateProduct",query:{id:t.id}})},handleVerifyDetail:function(e,t){this.verifyValue.ids=[],this.verifyValue.ids.push(t.id),this.verifyValue.mark="",this.showVerifyDetailModal=!0},handleShowProduct:function(e,t){console.log("handleShowProduct",t)},handleShowVerifyDetail:function(e,t){console.log("handleShowVerifyDetail",t)},handleShowLog:function(e,t){console.log("handleShowLog",t)},updatePublishStatus:function(e,t){var a=this,i=new URLSearchParams;i.append("ids",t),i.append("publishStatus",e),Object(u.D)(i).then(function(e){a.$message({message:"修改成功",type:"success",duration:1e3})})},updateNewStatus:function(e,t){var a=this,i=new URLSearchParams;i.append("ids",t),i.append("newStatus",e),Object(u.B)(i).then(function(e){a.$message({message:"修改成功",type:"success",duration:1e3})})},updateRecommendStatus:function(e,t){var a=this,i=new URLSearchParams;i.append("ids",t),i.append("recommendStatus",e),Object(u.E)(i).then(function(e){a.$message({message:"修改成功",type:"success",duration:1e3})})},updateDeleteStatus:function(e,t){var a=this,i=new URLSearchParams;i.append("ids",t),i.append("deleteStatus",e),Object(u.A)(i).then(function(e){a.$message({message:"删除成功",type:"success",duration:1e3})}),this.getList()}}},S={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"flex-box"},e._l(e.listCategory,function(t,i){return a("div",{key:i,staticClass:"flex-item",class:t.id===e.productCategoryId?"active":"",on:{click:function(a){return e.goNext(t)}}},[a("div",[e._v("\n        "+e._s(t.name)+"\n      ")])])}),0),e._v(" "),a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",[a("i",{staticClass:"el-icon-search"}),e._v(" "),a("span",[e._v(e._s(e.categoryName))])]),e._v(" "),a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"手机号："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"手机号"},model:{value:e.listQuery.phone,callback:function(t){e.$set(e.listQuery,"phone",t)},expression:"listQuery.phone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品名称："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品名称"},model:{value:e.listQuery.keyword,callback:function(t){e.$set(e.listQuery,"keyword",t)},expression:"listQuery.keyword"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品编号"},model:{value:e.listQuery.productSn,callback:function(t){e.$set(e.listQuery,"productSn",t)},expression:"listQuery.productSn"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"显示状态："}},[a("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.publishStatus,callback:function(t){e.$set(e.listQuery,"publishStatus",t)},expression:"listQuery.publishStatus"}},e._l(e.publishStatusOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"审核状态："}},[a("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.verifyStatus,callback:function(t){e.$set(e.listQuery,"verifyStatus",t)},expression:"listQuery.verifyStatus"}},e._l(e.verifyStatusOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"价格排序："}},[a("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.priceSort,callback:function(t){e.$set(e.listQuery,"priceSort",t)},expression:"listQuery.priceSort"}},e._l(e.priceSortOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n          查询结果\n        ")]),e._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n          重置\n        ")]),e._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleAdd()}}},[e._v("\n          新建商品\n        ")])],1)],1)]),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":e.listQuery.pageNum,total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"游戏类型",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.productCategoryName))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"商品编号",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.productSn))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"价格",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v("价格：￥"+e._s(t.row.price))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"区服",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.gameAccountQufu))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"标签",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v("\n            是否显示：\n            "),a("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(a){return e.handlePublishStatusChange(t.$index,t.row)}},model:{value:t.row.publishStatus,callback:function(a){e.$set(t.row,"publishStatus",a)},expression:"scope.row.publishStatus"}})],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatTime(t.row.updateTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatTime(t.row.createTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"审核状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",{class:e.getState(t.row.verifyStatus)},[e._v("\n            "+e._s(e._f("verifyStatusFilter")(t.row.verifyStatus))+"\n          ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleUpdateProduct(t.$index,t.row)}}},[e._v("编辑\n          ")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleModifyUser(t.$index,t.row)}}},[e._v("更换号主\n          ")])]}}])})],1)],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":e.listQuery.pageNum,total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{visible:e.editSkuInfo.dialogVisible,title:"编辑货品信息",width:"40%",top:"1vh"},on:{"update:visible":function(t){return e.$set(e.editSkuInfo,"dialogVisible",t)}}},[a("span",[e._v("商品编号：")]),e._v(" "),a("span",[e._v(e._s(e.editSkuInfo.productSn))]),e._v(" "),a("el-input",{staticStyle:{width:"50%","margin-left":"20px"},attrs:{placeholder:"按sku编号搜索",size:"small"},model:{value:e.editSkuInfo.keyword,callback:function(t){e.$set(e.editSkuInfo,"keyword",t)},expression:"editSkuInfo.keyword"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.handleSearchEditSku},slot:"append"})],1),e._v(" "),a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.editSkuInfo.stockList,border:""}},[a("el-table-column",{attrs:{label:"SKU编号",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.skuCode,callback:function(a){e.$set(t.row,"skuCode",a)},expression:"scope.row.skuCode"}})]}}])}),e._v(" "),e._l(e.editSkuInfo.productAttr,function(t,i){return a("el-table-column",{key:t.id,attrs:{label:t.name,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e.getProductSkuSp(t.row,i))+"\n        ")]}}],null,!0)})}),e._v(" "),a("el-table-column",{attrs:{label:"销售价格",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.price,callback:function(a){e.$set(t.row,"price",a)},expression:"scope.row.price"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"商品库存",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.stock,callback:function(a){e.$set(t.row,"stock",a)},expression:"scope.row.stock"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"库存预警值",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.lowStock,callback:function(a){e.$set(t.row,"lowStock",a)},expression:"scope.row.lowStock"}})]}}])})],2),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.editSkuInfo.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.handleEditSkuConfirm}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{visible:e.showAddModel,title:"添加商品",width:"80%",top:"1vh","close-on-click-modal":!1},on:{"update:visible":function(t){e.showAddModel=t}}},[e.showAddModel?a("ProductAdd",{attrs:{"query-id":e.queryId,"cate-parent-id":e.cateParentId,"product-category-id":e.productCategoryId},on:{addsuc:e.handleAddSuc}}):e._e()],1),e._v(" "),a("el-dialog",{attrs:{visible:e.showModifyUserModel,title:"修改号主",width:"30%",top:"6vh","close-on-click-modal":!1},on:{"update:visible":function(t){e.showModifyUserModel=t}}},[a("el-form",[a("el-form-item",{attrs:{label:"绑定用户"}},[a("el-input",{attrs:{placeholder:"用户手机号"},model:{value:e.username,callback:function(t){e.username=t},expression:"username"}})],1)],1),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:function(t){e.showModifyUserModel=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var _=a("VU/8")(y,S,!1,function(e){a("F34W")},"data-v-093a1a4b",null);t.default=_.exports},teiS:function(e,t){}});