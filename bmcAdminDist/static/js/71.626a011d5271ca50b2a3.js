webpackJsonp([71],{"5/u1":function(t,e){},Exax:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("//Fk"),i=a.n(n),l=a("woOf"),s=a.n(l),r=a("mRsl"),o=a("vLgD");function c(t,e){return Object(o.a)({url:"/gameCharacters/update/"+t,method:"post",data:e})}a("STSY");var u=a("xT6B"),d={pageNum:1,pageSize:20,keyword:null},m={categoryId:null,categoryName:null,characterName:null,server:null,guild:null,totalScore:null,status:null},f={name:"",data:function(){return{listQuery:s()({},d),list:null,total:null,listLoading:!1,dialogVisible:!1,admin:s()({},m),isEdit:!1,gameList:[]}},created:function(){this.getList(),this.getAllRoleList()},filters:{formatDateTime:function(t){if(null==t||""===t)return"N/A";var e=new Date(t);return Object(u.a)(e,"YYYY-MM-DD HH:mm:ss")}},methods:{cateChange:function(t){this.admin.categoryName=this.gameList.find(function(e){return e.id==t}).name},createIm:function(t){var e=this;initIM({id:t.id}).then(function(t){e.getList()})},handleResetSearch:function(){this.listQuery=s()({},d)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleAdd:function(){this.dialogVisible=!0,this.isEdit=!1,this.admin=s()({},m)},handleStatusChange:function(t,e){var a=this;this.$confirm("是否要修改该状态?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){c(e.id,e).then(function(t){a.$message({message:"修改成功！",type:"success"})})}).catch(function(){a.$message({type:"info",message:"取消修改"}),a.getList()})},handleDelete:function(t,e){var a=this;this.$confirm("是否要删除该用户?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){deleteAdmin(e.id).then(function(t){a.$message({type:"success",message:"删除成功!"}),a.getList()})})},handleUpdate:function(t,e){this.dialogVisible=!0,this.isEdit=!0,this.admin=s()({},e)},handleDialogConfirm:function(){var t=this;this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e;t.isEdit?c(t.admin.id,t.admin).then(function(e){t.$message({message:"修改成功！",type:"success"}),t.dialogVisible=!1,t.getList()}):(e=t.admin,Object(o.a)({url:"/gameCharacters/create",method:"post",data:e})).then(function(e){t.$message({message:"添加成功！",type:"success"}),t.dialogVisible=!1,t.getList()})})},getList:function(){var t,e=this;this.listLoading=!0,(t=this.listQuery,Object(o.a)({url:"/gameCharacters/list",method:"get",params:t})).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})},getAllRoleList:function(){var t=this;i.a.all([Object(r.d)(74,{pageNum:1,pageSize:999})]).then(function(e){t.gameList=[],e.forEach(function(e){t.gameList=t.gameList.concat(e.data.list)})})}}},g={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"输入搜索："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入名称",clearable:""},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询搜索\n          ")]),t._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),a("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[a("i",{staticClass:"el-icon-tickets"}),t._v(" "),a("span",[t._v("数据列表")]),t._v(" "),a("el-button",{staticClass:"btn-add",staticStyle:{"margin-left":"20px"},attrs:{size:"mini"},on:{click:function(e){return t.handleAdd()}}},[t._v("添加")])],1),t._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"adminTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[a("el-table-column",{attrs:{label:"游戏分类",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.categoryName))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"角色名称",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.characterName))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"区服",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.server))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"帮会",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.guild))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"总评分",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.totalScore))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(1===e.row.status?"启用":"未启用"))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"更新时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatDateTime")(e.row.updateTime)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"是否启用",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(a){return t.handleStatusChange(e.$index,e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleUpdate(e.$index,e.row)}}},[t._v("\n            编辑\n          ")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleDelete(e.$index,e.row)}}},[t._v("删除\n          ")])]}}])})],1)],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":t.listQuery.pageNum,"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)}}})],1),t._v(" "),a("el-dialog",{attrs:{title:t.isEdit?"编辑":"添加",visible:t.dialogVisible,width:"40%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{ref:"adminForm",attrs:{model:t.admin,"label-width":"150px",size:"small"}},[a("el-form-item",{attrs:{label:"所属游戏："}},[a("el-select",{staticStyle:{width:"248px"},on:{change:t.cateChange},model:{value:t.admin.categoryId,callback:function(e){t.$set(t.admin,"categoryId",e)},expression:"admin.categoryId"}},t._l(t.gameList,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"角色名称："}},[a("el-input",{staticStyle:{width:"250px"},model:{value:t.admin.characterName,callback:function(e){t.$set(t.admin,"characterName",e)},expression:"admin.characterName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"区服："}},[a("el-input",{staticStyle:{width:"250px"},model:{value:t.admin.server,callback:function(e){t.$set(t.admin,"server",e)},expression:"admin.server"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"帮会："}},[a("el-input",{staticStyle:{width:"250px"},model:{value:t.admin.guild,callback:function(e){t.$set(t.admin,"guild",e)},expression:"admin.guild"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"总评分："}},[a("el-input",{staticStyle:{width:"250px"},model:{value:t.admin.totalScore,callback:function(e){t.$set(t.admin,"totalScore",e)},expression:"admin.totalScore"}})],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleDialogConfirm()}}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var h=a("VU/8")(f,g,!1,function(t){a("5/u1")},null,null);e.default=h.exports}});