webpackJsonp([18],{"2z9k":function(t,e,a){"use strict";var i=a("fZjL"),o=a.n(i),s=a("Xxa5"),l=a.n(s),r=a("exGp"),n=a.n(r),u=a("//Fk"),c=a.n(u),d=a("TrAZ"),p=a.n(d),m=a("0xDb"),h=a("Lfj9"),f=a("mtWM"),v=a.n(f),g={props:{urlPic:{type:Array,default:function(){return[]}},nameKey:{type:String,default:""}},data:function(){return{dialogVisible:!1,dialogImageUrl:"",dataObj:{policy:"",signature:"",key:"",ossaccessKeyId:"",dir:"",host:""},isUploading:!1,useOss:!0,ossUploadUrl:"https://images2.kkzhw.com",minioUploadUrl:"http://*************:8201/mall-admin/minio/upload"}},computed:{imageUrl:function(){return this.value},imageName:function(){return null!=this.value&&""!==this.value?this.value.substr(this.value.lastIndexOf("/")+1):null},fileList:function(){return[{name:this.imageName,url:this.imageUrl}]},showFileList:{get:function(){return null!==this.value&&""!==this.value&&void 0!==this.value},set:function(t){}}},methods:{handlePreview:function(){this.dialogVisible=!0},beforeUpload2:function(t){var e=this;return new c.a(function(a,i){var o;Object(m.c)(t,(o=n()(l.a.mark(function i(o){var s,r,n,u;return l.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,e.imgToCanvas(o);case 2:s=i.sent,r=e.addWatermark(s,"卖号通"),n=e.convasToImg(r),u=Object(m.a)(n.src,t.name),a(u);case 7:case"end":return i.stop()}},i,e)})),function(t){return o.apply(this,arguments)}))})},imgToCanvas:function(t){var e=this;return n()(l.a.mark(function a(){var i,o;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return(i=document.createElement("img")).setAttribute("src",t),e.next=4,new c.a(function(t){return i.onload=t});case 4:return(o=document.createElement("canvas")).width=i.width,o.height=i.height,o.getContext("2d").drawImage(i,0,0),e.abrupt("return",o);case 9:case"end":return e.stop()}},a,e)}))()},addWatermark:function(t,e){var a=t.getContext("2d"),i=a.createPattern(this.$refs.waterImg,"repeat");return a.fillStyle=i,a.fillRect(0,0,t.width,t.height),t},convasToImg:function(t){var e=new Image;return e.src=t.toDataURL("image/png"),e},deletPic:function(t){this.$emit("deletPicList",t,this.nameKey)},handleUploadSuccess:function(t,e,a){var i=this;a.every(function(t){return"success"===t.status})&&a.filter(function(t){return 100===t.percentage}).map(function(t){delete t.percentage;var e=t.uid,a=i.dataObj[e].host+"/"+i.dataObj[e].dir+"/"+t.name;i.fileList.push({name:t.name,url:a}),i.$emit("upSuccsessList",a,i.nameKey),i.isUploading=!1})},rename:function(t,e){var a=new File([t],e,{type:t.type});a.uid=t.uid;var i=this.$refs.upload.uploadFiles.findIndex(function(e){return e.uid===t.uid});return this.$refs.upload.uploadFiles[i].raw=a,this.$refs.upload.uploadFiles[i].name=a.name,this.$refs.upload.uploadFiles[i].url=URL.createObjectURL(a),a},customUpload:function(t){var e=new FormData,a=this.dataObj[t.file.uid];o()(a).forEach(function(t){"fileName"!==t&&e.append(t,a[t])}),e.append("success_action_status","200"),e.append("file",t.file,t.file.name),v.a.post(this.ossUploadUrl,e,{headers:{"Content-Type":"multipart/form-data"}}).then(function(e){200===e.status&&(t.onProgress&&t.onProgress({percent:100}),t.onSuccess&&t.onSuccess(e))})},beforeUpload:function(t){var e=this;return n()(l.a.mark(function a(){var i,o,s,r;return l.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return i=e,a.next=3,Object(h._22)().then(function(a){var o=t.name.split(".").pop(),s=t.uid,l=a.data.fileName;return i.dataObj[s]={},i.dataObj[s].fileName=l,i.dataObj[s].policy=a.data.policy,i.dataObj[s].signature=a.data.signature,i.dataObj[s].ossaccessKeyId=a.data.accessKeyId,i.dataObj[s].key=a.data.dir+"/"+l+"."+o,i.dataObj[s].dir=a.data.dir,i.dataObj[s].host=a.data.host,e.rename(t,l+"."+o)}).catch(function(t){return console.log(t),!1});case 3:if(o=a.sent){a.next=6;break}return a.abrupt("return",!1);case 6:return e.isUploading=!0,a.next=9,e.beforeUpload2(o);case 9:return s=a.sent,a.next=12,e.compressionImage(s);case 12:return r=a.sent,a.abrupt("return",new c.a(function(t,a){e.useOss,t(r)}));case 14:case"end":return a.stop()}},a,e)}))()},compressionImage:function(t){return new c.a(function(e,a){new p.a({file:t,quality:.4,convertSize:1e5,redressOrientation:!1,beforeCompress:function(t){},success:function(t){var a=new File([t],t.name,{type:t.type});e(a)},error:function(t){a(t)}})})}},beforeRouteLeave:function(t,e,a){a()}},b={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t._l(t.urlPic,function(e,o){return t.urlPic&&t.urlPic.length?i("div",{key:o,staticClass:"picUpload_wrapSmall picUpload_wrapSmall_img_list"},[i("div",{staticClass:"delet_item",on:{click:function(e){return t.deletPic(o)}}},[i("img",{staticStyle:{width:"25px",height:"25px",position:"absolute",top:"0px",right:"0px"},attrs:{src:a("bItU"),alt:""}}),t._v(" "),i("i",{staticClass:"el-icon-delete del_icon"})]),t._v(" "),i("el-image",{key:o,staticClass:"picUpload_wrapSmall_el-img",staticStyle:{width:"100%",height:"100%",cursor:"pointer"},attrs:{"preview-src-list":t.urlPic,src:e,fit:"cover"}})],1):t._e()}),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.isUploading,expression:"!isUploading"}],staticClass:"picUpload_wrapSmall"},[i("el-upload",{ref:"upload",staticClass:"upload-input",attrs:{action:t.useOss?t.ossUploadUrl:t.minioUploadUrl,"file-list":t.fileList,"before-upload":t.beforeUpload,"on-success":t.handleUploadSuccess,"http-request":t.customUpload,multiple:"","list-type":"picture",accept:"image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"}},[i("el-button",{attrs:{size:"small",type:"primary"}},[t._v("+")])],1),t._v(" "),i("i",{staticClass:"el-icon-plus cover-uploader-icon"})],1),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.isUploading,expression:"isUploading"}],staticClass:"picUpload_wrapSmall",staticStyle:{"font-size":"14px"}},[i("p",[t._v("正在上传中~")])]),t._v(" "),i("img",{ref:"waterImg",staticStyle:{width:"0",height:"0"},attrs:{src:a("AdlW"),crossorigin:"Anonymous"}})],2)},staticRenderFns:[]};var y=a("VU/8")(g,b,!1,function(t){a("Zg3R")},null,null);e.a=y.exports},Zg3R:function(t,e){},bItU:function(t,e){t.exports="data:image/png;base64,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"},qtEN:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("woOf"),o=a.n(i),s=a("mvHQ"),l=a.n(s),r=a("bOdI"),n=a.n(r),u=a("so1O"),c=a("0xDb"),d=a("Lfj9"),p={components:{MyTable:u.a},props:{dialogVisible:{type:Boolean,default:!1}},data:function(){return{util:c.b,defaultListQuery:{productSn:null},rowObj:{},searchData:{productStatus:"ON_SHELF",currentRow:""},operationList:[{name:"",width:"100",value:"radio",slotName:"radio"},{name:"商品编号",width:"150",value:"productSn"},{name:"分类名称",width:"150",value:"productCategoryName"},{name:"商品图",value:"name",slotName:"goodsPic",width:"100"},{name:"单价",width:"100",value:"price"},{name:"底价",width:"100",value:"originalPrice"},{name:"商品标题",value:"detailTitle",slotName:"detailTitle"},{name:"发布时间",value:"createTime",width:"150",slotName:"createTime"}]}},methods:{getProductList:d.E,handleResetSearch:function(){this.defaultListQuery={productSn:null},this.searchData={pageNum:1,pageSize:20,productStatus:"ON_SHELF",productSn:null}},submit:function(){this.$emit("shelfSubmit",this.rowObj)},handleSearchList:function(){this.searchData=o()({},this.defaultListQuery,{pageNum:1,pageSize:20,productStatus:"ON_SHELF"})},handleClose:function(){this.$emit("shelfClose")},selectRowChange:function(t){console.log(t,89898989),this.rowObj=t}}},m={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{attrs:{title:"选择商品",visible:t.dialogVisible,width:"1000px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("div",[a("el-form",{attrs:{inline:!0,model:t.defaultListQuery,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入商品编号",clearable:""},model:{value:t.defaultListQuery.productSn,callback:function(e){t.$set(t.defaultListQuery,"productSn",e)},expression:"defaultListQuery.productSn"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")]),t._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询搜索\n          ")])],1)],1),t._v(" "),a("MyTable",{ref:"childRef",attrs:{listApi:t.getProductList,operationList:t.operationList,searchObj:t.searchData,isBtn:!1,maxheight:"350",isSelectRow:!0},on:{selectRowChange:t.selectRowChange},scopedSlots:t._u([{key:"radio",fn:function(e){var i=e.row;return[a("el-radio-group",{model:{value:i.isChecked,callback:function(e){t.$set(i,"isChecked",e)},expression:"row.isChecked"}},[a("el-radio",{attrs:{label:i.id}},[t._v(t._s(""))])],1)]}},{key:"goodsPic",fn:function(t){var e=t.row;return[a("img",{staticStyle:{width:"60px"},attrs:{src:e.pic,alt:""}})]}},{key:"detailTitle",fn:function(e){var i=e.row;return[a("div",{staticClass:"text_linTwo",attrs:{title:i.detailTitle}},[t._v("\n            "+t._s(i.detailTitle)+"\n          ")])]}},{key:"createTime",fn:function(e){var i=e.row;return[a("span",[t._v(t._s(t.util.timeFormatDD(i.createTime)))])]}}])})],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.handleClose}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary",disabled:0===Object.keys(t.rowObj).length},on:{click:t.submit}},[t._v("确 定")])],1)])],1)},staticRenderFns:[]},h=a("VU/8")(p,m,!1,null,null,null).exports,f=(a("Sbbi"),a("1NCi")),v=a("M2Lz"),g=a("2z9k"),b={components:{MyTable:u.a,orderDetail:f.a,shelfProduct:h,uploadSingle:v.a,uploadList:g.a},data:function(){return{util:c.b,orderDetailDrawerFlag:!1,orderDetailDrawer:!1,shelfVisible:!1,addGoodsVisible:!1,isAddGoods:!1,isOneServe:!1,orderId:"",activeName:"",options:[{value:"",label:"查看全部"},{value:"WAIT_PAY",label:"待付款"},{value:"BOOKED",label:"已预定"},{value:"REFUND",label:"已退款"},{value:"COMPLETED",label:"已完成"},{value:"CANCELED",label:"已取消"}],goodsStatusType:{NEW:"新建",DRAFT:"草稿",PENDING:"待发布",ON_SALE:"销售中",DOWN_SHELF:"已下架"},shopAllList:[],searchData:{platform:"XY",thirdShopId:""},defaultListQuery:{productSn:null,status:null,platform:"XY",outProductId:null,thirdShopId:null},platformListType:[{label:"闲鱼",value:"XY"},{label:"淘宝",value:"TB"},{label:"京东",value:"JD"},{label:"拼多多",value:"PDD"}],platformType:{XY:"闲鱼",TB:"淘宝",JD:"京东",PDD:"拼多多"},goodsForm:{productSn:"",subTitle:"",description:"",platform:"",originalPrice:"",price:"",pic:"",albumPics:[],thirdShopId:"",productCategoryName:""},operationList:[{name:"商品编号",width:"150",value:"productSn"},{name:"闲鱼商品ID",width:"150",value:"outProductId"},{name:"商品标题",value:"subTitle",slotName:"subTitle",width:"200"},{name:"商品描述",value:"description",width:"200"},{name:"上架平台",value:" platform",slotName:"platform",width:"100"},{name:"原价",width:"100",value:"originalPrice"},{name:"现价",width:"100",value:"price"},n()({name:"封面图",width:"100",value:"pic",slotName:"pic"},"width","120"),{name:"第三方店铺ID",value:"thirdShopId",width:"150"},{name:"状态",width:"150",value:"status",slotName:"status"},{name:"商品分类名称",width:"100",value:"productCategoryName"},{name:"上架时间",width:"100",value:"publishTime",slotName:"publishTime"},{name:"更新时间",width:"100",value:"updatedAt",slotName:"updatedAt"}]}},mounted:function(){this.getShopList()},methods:{addSubmitClick:function(){var t=this,e=JSON.parse(l()(this.goodsForm));e.albumPics=e.albumPics?e.albumPics.join(","):"",console.log(this.goodsForm,e),this.isAddGoods?Object(d.d)(e).then(function(e){200===e.code&&(t.addGoodsVisible=!1,t.$message.success("添加成功"),t.searchData=o()({},t.searchData))}):Object(d._26)(e).then(function(e){200===e.code&&(t.addGoodsVisible=!1,t.$message.success("更新成功"),t.searchData=o()({},t.searchData))})},getShopList:function(){var t=this;Object(d.v)({pageNum:1,pageSize:100}).then(function(e){t.defaultListQuery.thirdShopId=e.data.list[0].id+"",t.goodsForm.thirdShopId=e.data.list[0].id+"",t.shopAllList=e.data.list,t.isOneServe=!0,t.searchData.thirdShopId=e.data.list[0].id+"",t.searchData=o()({},t.searchData)})},addGoods:function(){this.goodsForm={productSn:"",subTitle:"",description:"",platform:"",originalPrice:"",price:"",pic:"",albumPics:[],thirdShopId:this.defaultListQuery.thirdShopId,productCategoryName:""},this.isAddGoods=!0,this.addGoodsVisible=!0},updateGoodsClick:function(t){var e=JSON.parse(l()(t));e.albumPics&&(e.albumPics=e.albumPics.split(",")),this.isAddGoods=!1,this.goodsForm=e,console.log(this.goodsForm),this.addGoodsVisible=!0},selectGoodsClick:function(){this.shelfVisible=!0},shelfSubmit:function(t){var e=this;Object(d.D)({productSn:t.productSn}).then(function(a){if(200==a.code){var i=t.pic,s=void 0===i?"":i,l=t.productSn,r=void 0===l?"":l,n=t.productCategoryName,u=void 0===n?"":n,c=t.price,d=void 0===c?"":c,p=t.originalPrice,m=void 0===p?"":p,h=t.subTitle,f=void 0===h?"":h;o()(e.goodsForm,{pic:s,description:a.data.product.description,productSn:r,productCategoryName:u,price:d,originalPrice:m,subTitle:f,albumPics:a.data.product.albumPics.split(",")}),e.shelfVisible=!1}})},shelfClose:function(){this.shelfVisible=!1},handleSearchList:function(){this.searchData=o()({},this.defaultListQuery,{pageNum:1,pageSize:20})},handleClick:function(t){console.log(t),this.defaultListQuery.status=t.name?this.activeName:null;this.searchData=o()({},this.defaultListQuery,{pageNum:1,pageSize:20})},shopClick:function(t){console.log(t,22222),this.defaultListQuery.thirdShopId=t.name,this.goodsForm.thirdShopId=t.name;this.searchData=o()({},this.defaultListQuery,{pageNum:1,pageSize:20})},handleResetSearch:function(){var t={status:"0"!=this.activeName?this.activeName:null,productSn:null,platform:"XY",outProductId:null};this.defaultListQuery=o()({},this.defaultListQuery,t),this.searchData={pageNum:1,pageSize:20,status:"0"!=this.activeName?this.activeName:null,platform:"XY",thirdShopId:this.defaultListQuery.thirdShopId},console.log(this.searchData,8989)},canDel:function(t){return[4,5,12].includes(t.status)},publishBtn:function(t){return"DRAFT"==t.status||"PENDING"==t.status||"DOWN_SHELF"==t.status},getIdlefishGoodsList:d.u,deleteGoodsClick:function(t){var e=this;this.$confirm("您确定要删除吗？删除后不可恢复?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(d.f)({id:t.id}).then(function(t){200==t.code&&(e.$message.success("删除成功"),e.searchData=o()({},e.searchData))})})},downGoodsClick:function(t){var e=this;this.$confirm("您确定要下架?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(d.n)({id:t.id}).then(function(t){200==t.code&&(e.$message.success("下架成功"),e.searchData=o()({},e.searchData))})})},publishGoodsClick:function(t){var e=this;this.$confirm("您确定要上架?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(d._8)({id:t.id}).then(function(t){200==t.code&&(e.$message.success("上架成功"),e.searchData=o()({},e.searchData))})})},handleClose:function(){var t=this;this.orderDetailDrawer=!1,setTimeout(function(){t.orderDetailDrawerFlag=!1},100)},handleRemove:function(){this.goodsForm.pic=""},picUpLoadSuc:function(t,e){this.goodsForm[e]=t},deletPic:function(t,e){this.goodsForm[e].splice(t,1)},picUpLoadListSuc:function(t,e){this.goodsForm[e].push(t)}}},y={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"20px","margin-bottom":"100px"}},[a("el-tabs",{on:{"tab-click":t.shopClick},model:{value:t.defaultListQuery.thirdShopId,callback:function(e){t.$set(t.defaultListQuery,"thirdShopId",e)},expression:"defaultListQuery.thirdShopId"}},t._l(t.shopAllList,function(t,e){return a("el-tab-pane",{key:e,attrs:{label:t.shopName,name:t.id+""}})}),1),t._v(" "),a("div",{staticStyle:{"margin-top":"15px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"查看全部",name:""}}),t._v(" "),a("el-tab-pane",{attrs:{label:"新建",name:"NEW"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"待发布",name:"PENDING"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"销售中",name:"ON_SALE"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"已下架",name:" DOWN_SHELF"}})],1),t._v(" "),a("div",{staticClass:"spaceBetweenNoAi"},[a("el-form",{attrs:{inline:!0,model:t.defaultListQuery,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"上架平台："}},[a("el-select",{staticStyle:{width:"100px"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.defaultListQuery.platform,callback:function(e){t.$set(t.defaultListQuery,"platform",e)},expression:"defaultListQuery.platform"}},t._l(t.platformListType,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticClass:"input-width",staticStyle:{width:"160px"},attrs:{placeholder:"请输入商品编号",clearable:""},model:{value:t.defaultListQuery.productSn,callback:function(e){t.$set(t.defaultListQuery,"productSn",e)},expression:"defaultListQuery.productSn"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"闲鱼商品ID："}},[a("el-input",{staticClass:"input-width",staticStyle:{width:"160px"},attrs:{placeholder:"请输入闲鱼商品ID",clearable:""},model:{value:t.defaultListQuery.outProductId,callback:function(e){t.$set(t.defaultListQuery,"outProductId",e)},expression:"defaultListQuery.outProductId"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")]),t._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询搜索\n          ")])],1)],1),t._v(" "),a("div",[a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.addGoods()}}},[t._v("\n          添加\n        ")])],1)],1)],1),t._v(" "),a("MyTable",{ref:"childRef",attrs:{listApi:t.getIdlefishGoodsList,operationList:t.operationList,searchObj:t.searchData,isOneServe:t.isOneServe},scopedSlots:t._u([{key:"pic",fn:function(t){var e=t.row;return[a("img",{staticStyle:{width:"100px"},attrs:{src:e.pic,alt:""}})]}},{key:"status",fn:function(e){var i=e.row;return[a("span",[t._v("\n        "+t._s(t.goodsStatusType[i.status])+"\n      ")])]}},{key:"publishTime",fn:function(e){var i=e.row;return[a("span",[t._v(t._s(t.util.timeFormatDD(i.publishTime)))])]}},{key:"updatedAt",fn:function(e){var i=e.row;return[a("span",[t._v(t._s(t.util.timeFormatDD(i.updatedAt)))])]}},{key:"subTitle",fn:function(e){var i=e.row;return[a("div",{staticClass:"text_linTwo",attrs:{title:i.title}},[t._v("\n        "+t._s(i.subTitle)+"\n      ")])]}},{key:"platform",fn:function(e){var i=e.row;return[a("div",[t._v("\n        "+t._s(t.platformType[i.platform])+"\n      ")])]}},{key:"btns",fn:function(e){var i=e.row;return[a("div",["ON_SALE"!=i.status&&"DOWN_SHELF"!=i.status?a("el-button",{attrs:{type:"text"},on:{click:function(e){return e.stopPropagation(),t.deleteGoodsClick(i)}}},[t._v("删除商品")]):t._e(),t._v(" "),"ON_SALE"==i.status?a("el-button",{attrs:{type:"text"},on:{click:function(e){return e.stopPropagation(),t.downGoodsClick(i)}}},[t._v("下架")]):t._e(),t._v(" "),t.publishBtn(i)?a("el-button",{attrs:{type:"text"},on:{click:function(e){return e.stopPropagation(),t.publishGoodsClick(i)}}},[t._v("上架")]):t._e(),t._v(" "),"DELETED"!=i.status?a("el-button",{attrs:{type:"text"},on:{click:function(e){return e.stopPropagation(),t.updateGoodsClick(i)}}},[t._v("更新")]):t._e()],1)]}}])}),t._v(" "),a("shelfProduct",{attrs:{dialogVisible:t.shelfVisible},on:{shelfClose:t.shelfClose,shelfSubmit:t.shelfSubmit}}),t._v(" "),a("el-dialog",{attrs:{title:t.isAddGoods?"添加商品":"更新商品",visible:t.addGoodsVisible,width:"1000px","before-close":t.handleClose},on:{"update:visible":function(e){t.addGoodsVisible=e}}},[a("div",[a("el-form",{ref:"goodsForm",staticClass:"demo-ruleForm",attrs:{model:t.goodsForm,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"选择商品"}},[a("div",[a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.selectGoodsClick()}}},[t._v("\n              选择商品 ")]),t._v("选择商品带入选中商品信息\n          ")],1)]),t._v(" "),a("el-form-item",{attrs:{label:"商品编号",prop:"productSn"}},[a("el-input",{model:{value:t.goodsForm.productSn,callback:function(e){t.$set(t.goodsForm,"productSn",e)},expression:"goodsForm.productSn"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品标题",prop:"productSn"}},[a("el-input",{model:{value:t.goodsForm.subTitle,callback:function(e){t.$set(t.goodsForm,"subTitle",e)},expression:"goodsForm.subTitle"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品描述",prop:"productSn"}},[a("el-input",{model:{value:t.goodsForm.description,callback:function(e){t.$set(t.goodsForm,"description",e)},expression:"goodsForm.description"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品分类名称",prop:"productCategoryName"}},[a("el-input",{model:{value:t.goodsForm.productCategoryName,callback:function(e){t.$set(t.goodsForm,"productCategoryName",e)},expression:"goodsForm.productCategoryName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"上架平台",prop:"platform"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.goodsForm.platform,callback:function(e){t.$set(t.goodsForm,"platform",e)},expression:"goodsForm.platform"}},t._l(t.platformListType,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"原价",prop:"originalPrice"}},[a("el-input",{model:{value:t.goodsForm.originalPrice,callback:function(e){t.$set(t.goodsForm,"originalPrice",e)},expression:"goodsForm.originalPrice"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"现价",prop:"price"}},[a("el-input",{model:{value:t.goodsForm.price,callback:function(e){t.$set(t.goodsForm,"price",e)},expression:"goodsForm.price"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"封面图",prop:"pic"}},[a("uploadSingle",{staticClass:"goods_cover_upload goods_upload_single",class:t.goodsForm.pic?"goods_cover_upload1":"goods_cover_upload",staticStyle:{display:"flex"},attrs:{"url-pic":t.goodsForm.pic,"name-key":"pic"},on:{handleRemove:t.handleRemove,upSuccsessSingle:t.picUpLoadSuc}})],1),t._v(" "),a("el-form-item",{attrs:{label:"描述图片",prop:"albumPics"}},[a("uploadList",{staticClass:"goods_details_upload",attrs:{"url-pic":t.goodsForm.albumPics,"name-key":"albumPics"},on:{upSuccsessList:t.picUpLoadListSuc,deletPicList:t.deletPic}})],1)],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.addGoodsVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.addSubmitClick}},[t._v("确 定")])],1)])],1)},staticRenderFns:[]};var S=a("VU/8")(b,y,!1,function(t){a("t4oS")},null,null);e.default=S.exports},t4oS:function(t,e){}});