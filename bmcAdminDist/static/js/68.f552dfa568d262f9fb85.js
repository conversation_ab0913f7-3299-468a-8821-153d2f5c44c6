webpackJsonp([68],{c7S7:function(e,t){},u5in:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=l("woOf"),r=l.n(i),a=l("c9K3"),s=l("FWz8"),n=l("xT6B"),o=l("g5t5"),u=l("0xDb"),c={productSn:null,pageNum:1,pageSize:20,orderSn:null,receiverKeyword:null,status:null,orderType:null,sourceType:null,createTime:null},d={name:"orderList",components:{LogisticsDialog:o.a,orderDetail:a.default},data:function(){return{util:u.b,listQuery:r()({},c),listLoading:!0,list:null,total:null,operateType:null,multipleSelection:[],closeOrder:{dialogVisible:!1,content:null,orderIds:[]},statusOptions:[{label:"待付款",value:0},{label:"已预订",value:1},{label:"换绑中",value:2},{label:"换绑完成",value:3},{label:"已关闭",value:4},{label:"无效订单",value:5},{label:"已退款",value:6},{label:"待汇款",value:7},{label:"汇款中",value:8},{label:"代发待审核",value:9},{label:"代发失败",value:10},{label:"汇款成功",value:11},{label:"结单",value:12}],orderTypeOptions:[{label:"正常订单",value:0},{label:"秒杀订单",value:1}],sourceTypeOptions:[{label:"PC订单",value:0},{label:"APP订单",value:1}],operateOptions:[{label:"批量发货",value:1},{label:"关闭订单",value:2},{label:"删除订单",value:3}],logisticsDialogVisible:!1,showOrderDetail:!1,orderId:""}},created:function(){this.getList()},filters:{formatCreateTime:function(e){var t=new Date(e);return Object(n.a)(t,"YYYY-MM-DD HH:mm:ss")},formatPayType:function(e){return 1===e?"支付宝":2===e?"微信":"未支付"},formatSourceType:function(e){return 1===e?"APP订单":"PC订单"}},methods:{getSN:function(e){return e.orderItemList&&e.orderItemList[0]?e.orderItemList[0].productSn:""},handleResetSearch:function(){this.listQuery=r()({},c)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSelectionChange:function(e){this.multipleSelection=e},handleViewOrder:function(e,t){this.showOrderDetail=!0,this.orderId=t.id},handleCloseOrder:function(e,t){this.closeOrder.dialogVisible=!0,this.closeOrder.orderIds=[t.id]},handleDeliveryOrder:function(e,t){var l=this.covertOrder(t);this.$router.push({path:"/oms/deliverOrderList",query:{list:[l]}})},handleViewLogistics:function(e,t){this.logisticsDialogVisible=!0},handleDeleteOrder:function(e,t){var l=[];l.push(t.id),this.deleteOrder(l)},handleBatchOperate:function(){if(null==this.multipleSelection||this.multipleSelection.length<1)this.$message({message:"请选择要操作的订单",type:"warning",duration:1e3});else if(1===this.operateType){for(var e=[],t=0;t<this.multipleSelection.length;t++)1===this.multipleSelection[t].status&&e.push(this.covertOrder(this.multipleSelection[t]));if(0===e.length)return void this.$message({message:"选中订单中没有可以发货的订单",type:"warning",duration:1e3});this.$router.push({path:"/oms/deliverOrderList",query:{list:e}})}else if(2===this.operateType){this.closeOrder.orderIds=[];for(var l=0;l<this.multipleSelection.length;l++)this.closeOrder.orderIds.push(this.multipleSelection[l].id);this.closeOrder.dialogVisible=!0}else if(3===this.operateType){for(var i=[],r=0;r<this.multipleSelection.length;r++)i.push(this.multipleSelection[r].id);this.deleteOrder(i)}},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},handleCloseOrderConfirm:function(){var e=this;if(null!=this.closeOrder.content&&""!==this.closeOrder.content){var t=new URLSearchParams;t.append("ids",this.closeOrder.orderIds),t.append("note",this.closeOrder.content),Object(s.a)(t).then(function(t){e.closeOrder.orderIds=[],e.closeOrder.dialogVisible=!1,e.getList(),e.$message({message:"修改成功",type:"success",duration:1e3})})}else this.$message({message:"操作备注不能为空",type:"warning",duration:1e3})},getList:function(){var e=this;this.listLoading=!0,Object(s.d)(this.listQuery).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})},deleteOrder:function(e){var t=this;this.$confirm("是否要进行该删除操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var l=new URLSearchParams;l.append("ids",e),Object(s.b)(l).then(function(e){t.$message({message:"删除成功！",type:"success",duration:1e3}),t.getList()})})},covertOrder:function(e){var t=e.receiverProvince+e.receiverCity+e.receiverRegion+e.receiverDetailAddress;return{orderId:e.id,orderSn:e.orderSn,receiverName:e.receiverName,receiverPhone:e.receiverPhone,receiverPostCode:e.receiverPostCode,address:t,deliveryCompany:null,deliverySn:null}}}},h={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[l("div",{staticStyle:{"margin-top":"15px"}},[l("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[l("el-form-item",{attrs:{label:"订单编号："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"订单编号"},model:{value:e.listQuery.orderSn,callback:function(t){e.$set(e.listQuery,"orderSn",t)},expression:"listQuery.orderSn"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"商品编号："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"商品编号"},model:{value:e.listQuery.productSn,callback:function(t){e.$set(e.listQuery,"productSn",t)},expression:"listQuery.productSn"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"收货人："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"收货人姓名/手机号码"},model:{value:e.listQuery.receiverKeyword,callback:function(t){e.$set(e.listQuery,"receiverKeyword",t)},expression:"listQuery.receiverKeyword"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"提交时间："}},[l("el-date-picker",{staticClass:"input-width",attrs:{"value-format":"yyyy-MM-dd",type:"date",placeholder:"请选择时间"},model:{value:e.listQuery.createTime,callback:function(t){e.$set(e.listQuery,"createTime",t)},expression:"listQuery.createTime"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"订单状态："}},[l("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.statusOptions,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",[l("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询搜索\n          ")]),e._v(" "),l("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),l("div",{staticClass:"table-container"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"orderTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""},on:{"selection-change":e.handleSelectionChange}},[l("el-table-column",{attrs:{label:"商品编号",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.getSN(t.row)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"订单编号",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.orderSn))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"商品类型",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.productCategoryName))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"提交时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatCreateTime")(t.row.createTime)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"用户账号",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.memberUsername))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"订单金额",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("￥"+e._s(t.row.totalAmount))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"已退款金额",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("￥"+e._s(t.row.returnAmount||0))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"订单状态",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.util.getStatus(t.row.status)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"支付时间",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.util.timeFormat(t.row.finishTime)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{attrs:{size:"mini"},on:{click:function(l){return e.handleViewOrder(t.$index,t.row)}}},[e._v("查看订单")])]}}])})],1)],1),e._v(" "),l("div",{staticClass:"pagination-container"},[l("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)}}})],1),e._v(" "),l("el-dialog",{attrs:{title:"关闭订单",visible:e.closeOrder.dialogVisible,width:"30%"},on:{"update:visible":function(t){return e.$set(e.closeOrder,"dialogVisible",t)}}},[l("span",{staticStyle:{"vertical-align":"top"}},[e._v("操作备注：")]),e._v(" "),l("el-input",{staticStyle:{width:"80%"},attrs:{type:"textarea",rows:5,placeholder:"请输入内容"},model:{value:e.closeOrder.content,callback:function(t){e.$set(e.closeOrder,"content",t)},expression:"closeOrder.content"}}),e._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.closeOrder.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),l("el-button",{attrs:{type:"primary"},on:{click:e.handleCloseOrderConfirm}},[e._v("确 定")])],1)],1),e._v(" "),l("logistics-dialog",{model:{value:e.logisticsDialogVisible,callback:function(t){e.logisticsDialogVisible=t},expression:"logisticsDialogVisible"}}),e._v(" "),l("el-dialog",{attrs:{width:"70%",visible:e.showOrderDetail},on:{"update:visible":function(t){e.showOrderDetail=t}}},[e.showOrderDetail?l("orderDetail",{attrs:{id:e.orderId}}):e._e(),e._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.showOrderDetail=!1}}},[e._v("取 消")]),e._v(" "),l("el-button",{attrs:{type:"primary"},on:{click:function(t){e.showOrderDetail=!1}}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var p=l("VU/8")(d,h,!1,function(e){l("c7S7")},"data-v-65972d4f",null);t.default=p.exports}});