webpackJsonp([14],{"6R4F":function(t,e){},HUo0:function(t,e){},"Yga/":function(t,e){},f7Og:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("Dd8w"),n=a.n(i),s=a("woOf"),o=a.n(s),r=a("PJh5"),l=a.n(r),u=a("fZjL"),c=a.n(u),d=a("d7EF"),p=a.n(d),v=a("pFYg"),m=a.n(v),h=a("mvHQ"),f=a.n(h),g=a("f9/G"),b=a("rtXg"),y=a("M4fF"),_=a.n(y),x=a("mRsl"),C=a("KhLR"),L=a("3idm"),w=a("ocgh"),I=a("II7+"),S=a("0xDb"),k=a("0Dnf"),O=a("nYtf"),A={albumPics:"",albumPicsJson:"[]",productAttributeValueList:[],brandId:"",productAttributeCategoryId:"",gameAccountQufu:"",description:"",qcsy2:0,pic:"",qcsy3:0,selectProductPics:[],price:"",originalPrice:"",stock:9,gameGoodsFangxin:0,gameGoodsBukuan:0,gameGoodsJiangjia:0,gameGoodsYijia:0,sort:"",publishStatus:0,gameGoodsYishou:0,gameGoodsYuyue:0,gameGoodsSaletype:1,gameCareinfoPhone:"",gameCareinfoTime:"",gameCareinfoVx:"",subjectProductRelationList:[]},P={1:"面板属性",2:"打造内功",3:"天赏外观",4:"普通外观",5:"其他物品"},T=["区服","职业","账号类型","性别","游戏账号","游戏密码","确认密码","账号来源","已使用天赏石"],j={"游戏":"productCategoryId","登录方式":"loginType","区服":"gameAccountQufu","预期价格":"price","心里底价":"originalPrice","是否是一手号":"gameGoodsYishou","是否支持议价":"gameGoodsYijia","用户微信号":"gameCareinfoVx","用户手机号":"username"},$={components:{tedian:I.a,myProgress:g.a,IntervalTask:O.a},props:{deviceInfo:{type:Object,default:function(){}}},data:function(){return{textarea:"游戏：逆水寒手游\n登录方式（扫码/短信/账密）：\n区服：满江红|玄机喵算\n职业：铁衣\n已使用天赏石：26\n账号类型（手机或邮箱）：手机\n游戏账号（必填）：*********\n游戏密码：\n预期价格：5500\n是否是一手号：是\n是否支持议价：是\n用户微信号：7333773\n用户手机号（必填）：185*******",LH_LOGIN_TYPE:k.f,verifyStatus:"",typeOptions:[],hasImgType:!1,detail:"",tabValue:"0",active:0,value:o()({},A),hasEditCreated:!1,selectProductCateValue:[],productCateOptions:[],brandOptions:[],productAttributeCategoryOptions:[],selectProductParam:[],addProductAttrValue:"",activeHtmlName:"pc",rules:{},formLabelWidth:"200px",spanCol:12,extList:{ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]},ext5:{needShow:!1,opetionDate:[],detailOptions:[]},ext6:{needShow:!1,opetionDate:[],detailOptions:[]}},subjectList:[],subjectTitles:["待选择","已选择"],memberId:"",memberInfo:{},taskId:""}},computed:{isEdit:function(){return""!==this.queryId},selectServiceList:{get:function(){var t=[];if(void 0===this.value.serviceIds||null==this.value.serviceIds||""===this.value.serviceIds)return t;for(var e=this.value.serviceIds.split(","),a=0;a<e.length;a++)t.push(Number(e[a]));return t},set:function(t){var e="";if(null!=t&&t.length>0){for(var a=0;a<t.length;a++)e+=t[a]+",";e.endsWith(",")&&(e=e.substr(0,e.length-1)),this.value.serviceIds=e}else this.value.serviceIds=null}}},watch:{},beforeDestroy:function(){this.stl&&(this.stl=clearInterval(this.stl))},created:function(){this.getProductCateList(),this.getProductAttrCateList()},methods:{handelEnd:function(){this.taskId=null,this.$emit("addsuc")},transFormAlbumPicsJson:function(){var t=this.value.albumPicsJson||"[]",e=[];(t=JSON.parse(t)).forEach(function(t){t.hasOwnProperty("name")?e.push(t):(t.name=P[t.type]||t.type||"",e.push(t))}),this.value.albumPicsJson=f()(e)},getGender:function(t){return 1==t?"男":"女"},getConfirm:function(t){return 1===t?"是":"否"},changequfu:function(t){this.value.gameAccountQufu=t},changeTab:function(t,e){"2"===t.index&&this.getMember()},getMember:function(){var t=this;this.memberId&&Object(w.a)(this.memberId).then(function(e){t.memberInfo=e.data})},handleBrandChange:function(t){for(var e="",a=0;a<this.brandOptions.length;a++)if(this.brandOptions[a].value===t){e=this.brandOptions[a].label;break}this.value.brandName=e},handleRemoveProductLadder:function(t,e){var a=this.value.productLadderList;1===a.length?(a.pop(),a.push({count:0,discount:0,price:0})):a.splice(t,1)},getProductCateList:function(){var t=this;Object(x.e)().then(function(e){var a=e.data;t.productCateOptions=[];for(var i=0;i<a.length;i++){var n=[];if(null!=a[i].children&&a[i].children.length>0)for(var s=0;s<a[i].children.length;s++)n.push({label:a[i].children[s].name,value:a[i].children[s].id});"游戏"==a[i].name&&(t.productCateOptions=n)}})},handleAddProductLadder:function(t,e){var a=this.value.productLadderList;a.length<3?a.push({count:0,discount:0,price:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handleRemoveFullReduction:function(t,e){var a=this.value.productFullReductionList;1===a.length?(a.pop(),a.push({fullPrice:0,reducePrice:0})):a.splice(t,1)},handleAddFullReduction:function(t,e){var a=this.value.productFullReductionList;a.length<3?a.push({fullPrice:0,reducePrice:0}):this.$message({message:"最多只能添加三条",type:"warning"})},getProductAttrCateList:function(){var t=this;Object(C.c)({pageNum:1,pageSize:999}).then(function(e){t.productAttributeCategoryOptions=[];for(var a=e.data.list,i=0;i<a.length;i++)t.productAttributeCategoryOptions.push({label:a[i].name,value:a[i].id})})},getProductAttrList:function(t,e){var a=this,i={pageNum:1,pageSize:200,type:t};Object(L.c)(e,i).then(function(e){var i=e.data.list;if(0!==t){var n="基础信息扩展";2===t?n="账号信息扩展":3===t&&(n="其他扩展");var s={index:parseInt(t,10),label:n,needShow:i&&i.length>0},o=a.getEditAttrOptions2(i);console.log(o.map(function(t){return t.name}));var r=[];s.opetionDate=o;for(var l=0;l<i.length;l++){var u=null;a.isEdit&&(u=a.getEditParamValue2(i[l]))&&r.push(u)}s.detailOptions=r,S.b.async2opetionDate(s.detailOptions,s.opetionDate),s.opetionDate=s.opetionDate.filter(function(t){return T.includes(t.name)}),a.$set(a.extList,"ext"+t,s)}})},getEditAttrOptions2:function(t){return t.map(function(t){var e=1;if(1===t.inputType&&(1===t.selectType?e=2:2===t.selectType?e=3:3===t.selectType&&(e=4)),1===e)return o()({},t,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:e,iptVal:""});if(2===e)return o()({},t,{tdtype:e,value:"",is_required:0,field_type:2,inputList:t.inputList.split(",")});if(3===e){var a=[];return t.inputList.split(",").forEach(function(t){a.push({icon:"",name:t,checked:!1})}),o()({},t,{childList:a,is_required:0,field_type:2,default_word:"点击可下拉选择物品",tdtype:e,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[]})}var i=JSON.parse(t.inputList);return i.forEach(function(t){t.value=t.parent_name,t.label=t.parent_name;var e=t.childList.map(function(t){return{value:t,label:t}});t.children=e}),o()({},t,{tdtype:e,value:[],is_required:0,field_type:2,options:i})})},getEditParamValue2:function(t){var e=1;1===t.inputType&&(1===t.selectType?e=2:2===t.selectType?e=3:3===t.selectType&&(e=4));for(var a=0;a<this.value.productAttributeValueList.length;a++)if(t.id===this.value.productAttributeValueList[a].productAttributeId){var i=this.value.productAttributeValueList[a];if(1===e)return o()({},t,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:e,iptVal:i.value});if(2===e)return o()({},t,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:e,value:i.value});if(3!==e)return o()({},t,{title:t.name,tdtype:e,value:(i.value||"").split("|"),options:JSON.parse(t.inputList)});var n=function(){var a=[];""!==i.value&&(a=i.value.split(","));var n=[];return a.forEach(function(t){n.push({icon:"",name:t,checked:!0})}),{v:o()({},t,{title:t.name,tdtype:e,value:n})}}();if("object"===(void 0===n?"undefined":m()(n)))return n.v}},handleProductAttrChange:function(t){this.extList={ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]},ext5:{needShow:!1,opetionDate:[],detailOptions:[]},ext6:{needShow:!1,opetionDate:[],detailOptions:[]}},this.getProductAttrList(1,t),this.getProductAttrList(2,t),this.getProductAttrList(3,t),this.getProductAttrList(4,t),this.getProductAttrList(5,t),this.getProductAttrList(6,t)},getParamInputList:function(t){return t.split(",")},submitForm:function(t){var e=this;this.$refs[t].validate(function(t){if(!t)return!1;e.finishCommit()})},submitFormByQuick:function(){var t=this,e={albumPics:"",albumPicsJson:"[]",productAttributeValueList:[],brandId:"",productAttributeCategoryId:"",gameAccountQufu:"",description:"",qcsy2:0,pic:"",qcsy3:0,selectProductPics:[],price:"",originalPrice:"",stock:9,gameGoodsFangxin:0,gameGoodsBukuan:0,gameGoodsJiangjia:0,gameGoodsYijia:0,sort:"",publishStatus:0,gameGoodsYishou:1,gameGoodsYuyue:0,gameGoodsSaletype:1,gameCareinfoPhone:"",gameCareinfoTime:"",gameCareinfoVx:"",subjectProductRelationList:[],loginType:"",productCategoryId:"",username:"",productCategoryName:"",deviceId:this.deviceInfo&&this.deviceInfo.id},a=this.textarea.split(/[(\r\n)\r\n]+/),i=this.productAttributeCategoryOptions.filter(function(t){return"逆水寒手游SKU"===t.label})[0];if(i){e.productAttributeCategoryId=i.value,this.handleProductAttrChange(e.productAttributeCategoryId);var n=[];(this.textarea.includes("【")||this.textarea.includes("】"))&&n.push("含有中括号，请修改中括号内容或去除中括号"),setTimeout(function(){console.log(t.extList);var i={};if(a.forEach(function(a){var s=a.split("："),o=p()(s,2),r=o[0],l=o[1];if(r=r.split("（")[0],"是"===(l=l.trim())?l=1:"否"===l&&(l=0),i[r]=l,"游戏账号"!==r||l||n.push("游戏账必填"),j[r]){if(e[j[r]]=l,"productCategoryId"===j[r]){var u=t.productCateOptions.filter(function(t){return t.label===e.productCategoryId})[0];if(!u)return void n.push("游戏类型不符合");e.productCategoryId=u.value,e.productCategoryName=l}if("loginType"===j[r]){var c=k.f.filter(function(t){return t.alias===l})[0];if(!c)return void n.push("登录方式不符合");e.loginType=c.value}}}),n.length)t.$message.error(n.join(","));else{e.productAttributeValueList=[];for(var s=c()(t.extList),o=0;o<s.length;o++)for(var r=s[o],l=t.extList[r].opetionDate,u=0;u<l.length;u++){var d=l[u];e.productAttributeValueList.push({productAttributeId:d.id,value:i[d.name]||"",attriName:d.name,sort:d.sort,filterType:d.filterType,searchType:d.searchType,type:d.type,searchSort:d.searchSort})}console.log(e);for(var v=[],m=0;m<a.length;m++){var h=a[m].split("："),f=p()(h,2),g=f[0],y=f[1];v.push("<div><span>"+g.split("（")[0]+"：</span><span>"+y+"</span><div>")}t.$alert("<div>"+v.join("")+"</div>","二次确认",{confirmButtonText:"确认并提交",dangerouslyUseHTMLString:!0,callback:function(a){"cancel"!==a&&Object(b.a)(e).then(function(e){200==e.code&&(t.taskId=e.data)})}})}},1e3)}},filterMethod:function(t,e){return e.label.indexOf(t)>-1},cancel:function(){this.$emit("addsuc")},showStateImg:function(t){this.stateImg=t},finishCommit:function(){var t=this;this.$confirm("是否要提交","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.value.productAttributeValueList=[];for(var e=c()(t.extList),a=0;a<e.length;a++)for(var i=e[a],n=t.extList[i].opetionDate,s=0;s<n.length;s++){var o=n[s],r=o.value||"";3===o.tdtype&&o.choosedList.length?r=o.choosedList.map(function(t){return t.name}).join(","):1===o.tdtype?r=o.iptVal:4===o.tdtype&&(r=o.value.join("|")),t.value.productAttributeValueList.push({productAttributeId:o.id,value:r,attriName:o.name,sort:o.sort,filterType:o.filterType,searchType:o.searchType,type:o.type,searchSort:o.searchSort})}var l=_.a.cloneDeep(t.value),u=t.productCateOptions.find(function(t){return t.value==l.productCategoryId});l.productCategoryName=u.label,l.deviceId=t.deviceInfo&&t.deviceInfo.id,Object(b.a)(l).then(function(e){200==e.code&&(t.taskId=e.data)})})}}},D={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-tabs",{on:{"tab-click":t.changeTab},model:{value:t.tabValue,callback:function(e){t.tabValue=e},expression:"tabValue"}},[a("el-tab-pane",{attrs:{label:"基本信息"}},[a("el-form",{ref:"productForm",staticClass:"form-box",attrs:{model:t.value,rules:t.rules,"label-width":t.formLabelWidth}},[a("div",{directives:[{name:"show",rawName:"v-show",value:"0"===t.tabValue,expression:"tabValue === '0'"}]},[t.deviceInfo&&t.deviceInfo.id?a("el-form-item",{attrs:{label:"设备信息："}},[t._v("\n          "+t._s(t.deviceInfo.deviceName)+"\n        ")]):t._e(),t._v(" "),a("el-card",{staticClass:"card-box"},[a("el-form-item",{attrs:{label:"登录方式：",prop:"loginType"}},[a("el-select",{attrs:{placeholder:"登录类型："},model:{value:t.value.loginType,callback:function(e){t.$set(t.value,"loginType",e)},expression:"value.loginType"}},t._l(t.LH_LOGIN_TYPE,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"游戏类型：",prop:"productCategoryId"}},[a("el-select",{attrs:{placeholder:"游戏类型"},model:{value:t.value.productCategoryId,callback:function(e){t.$set(t.value,"productCategoryId",e)},expression:"value.productCategoryId"}},t._l(t.productCateOptions,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"游戏SKU："}},[a("el-select",{attrs:{placeholder:"请选择属性类型"},on:{change:t.handleProductAttrChange},model:{value:t.value.productAttributeCategoryId,callback:function(e){t.$set(t.value,"productAttributeCategoryId",e)},expression:"value.productAttributeCategoryId"}},t._l(t.productAttributeCategoryOptions,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"区服信息：",prop:"gameAccountQufu"}},[a("el-input",{attrs:{disabled:""},model:{value:t.value.gameAccountQufu,callback:function(e){t.$set(t.value,"gameAccountQufu",e)},expression:"value.gameAccountQufu"}})],1),t._v(" "),t.extList.ext1.needShow?a("div",{staticClass:"ext1",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":t.extList.ext1.detailOptions,"opetion-date":t.extList.ext1.opetionDate},on:{changequfu:t.changequfu}})],1):t._e()],1),t._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[t._v("商品规格")]),t._v(" "),a("el-form-item",{attrs:{label:"售价金额：",prop:"price"}},[a("el-input",{model:{value:t.value.price,callback:function(e){t.$set(t.value,"price",e)},expression:"value.price"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"心理底价：",prop:"originalPrice"}},[a("el-input",{model:{value:t.value.originalPrice,callback:function(e){t.$set(t.value,"originalPrice",e)},expression:"value.originalPrice"}})],1)],1),t._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[t._v("账号信息")]),t._v(" "),a("el-form-item",{attrs:{label:"可否议价：",prop:"gameGoodsYijia"}},[a("el-radio-group",{model:{value:t.value.gameGoodsYijia,callback:function(e){t.$set(t.value,"gameGoodsYijia",e)},expression:"value.gameGoodsYijia"}},[a("el-radio",{attrs:{label:1}},[t._v("是")]),t._v(" "),a("el-radio",{attrs:{label:0}},[t._v("否")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"是否一手：",prop:"gameGoodsYishou"}},[a("el-radio-group",{model:{value:t.value.gameGoodsYishou,callback:function(e){t.$set(t.value,"gameGoodsYishou",e)},expression:"value.gameGoodsYishou"}},[a("el-radio",{attrs:{label:1}},[t._v("是")]),t._v(" "),a("el-radio",{attrs:{label:0}},[t._v("否")])],1)],1)],1),t._v(" "),t.extList.ext2.needShow?a("el-card",{staticStyle:{"margin-bottom":"20px"}},[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":t.extList.ext2.detailOptions,"opetion-date":t.extList.ext2.opetionDate},on:{changequfu:t.changequfu}})],1)])],1):t._e(),t._v(" "),a("el-card",{staticClass:"card-box"},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"账号描述：",prop:"description"}},[a("el-input",{attrs:{type:"textarea"},model:{value:t.value.description,callback:function(e){t.$set(t.value,"description",e)},expression:"value.description"}})],1)],1),t._v(" "),a("el-card",{staticClass:"card-box"},[a("el-form-item",{attrs:{rules:[{required:!0,message:"请输入联系手机",trigger:"blur"}],label:"联系手机：",prop:"username"}},[a("el-input",{model:{value:t.value.username,callback:function(e){t.$set(t.value,"username",e)},expression:"value.username"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"联系微信：",prop:"gameCareinfoVx"}},[a("el-input",{model:{value:t.value.gameCareinfoVx,callback:function(e){t.$set(t.value,"gameCareinfoVx",e)},expression:"value.gameCareinfoVx"}})],1)],1),t._v(" "),t.extList.ext3.needShow?a("el-card",{staticStyle:{display:"none"}},[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":t.extList.ext3.detailOptions,"opetion-date":t.extList.ext3.opetionDate},on:{changequfu:t.changequfu}})],1)])],1):t._e(),t._v(" "),t.extList.ext4.needShow?a("el-card",{staticStyle:{display:"none"}},[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":t.extList.ext4.detailOptions,"opetion-date":t.extList.ext4.opetionDate},on:{changequfu:t.changequfu}})],1)])],1):t._e(),t._v(" "),t.extList.ext5.needShow?a("el-card",[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":t.extList.ext5.detailOptions,"opetion-date":t.extList.ext5.opetionDate},on:{changequfu:t.changequfu}})],1)])],1):t._e()],1),t._v(" "),a("div",{staticClass:"m-footer"},[a("div",{staticClass:"spaceBetween"},[a("div",[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("productForm")}}},[t._v("确认")])],1)])])])],1),t._v(" "),a("el-tab-pane",{attrs:{label:"快捷录入"}},[a("el-input",{attrs:{type:"textarea",rows:15,placeholder:"请输入内容"},model:{value:t.textarea,callback:function(e){t.textarea=e},expression:"textarea"}}),t._v(" "),a("div",{staticClass:"m-footer"},[a("div",{staticClass:"spaceBetween"},[a("div",[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.submitFormByQuick}},[t._v("确认")])],1)])])],1)],1),t._v(" "),t.taskId?a("IntervalTask",{attrs:{taskId:t.taskId},on:{finish:t.handelEnd}}):t._e()],1)},staticRenderFns:[]};var N={pageNum:1,pageSize:20},F={components:{ProductAddNew:a("VU/8")($,D,!1,function(t){a("HUo0")},"data-v-d1e5046c",null).exports},data:function(){return{deviceForm:{address:"",deviceName:""},deviceInfo:null,showAddProduct:!1,listQuery:o()({},N),list:null,total:null,listLoading:!0,multipleSelection:[],id:"",showAddDeviceModal:!1}},created:function(){this.getList()},methods:{handleStop:function(t,e){var a=this;this.$confirm("是否要停止?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(b.e)(e.id).then(function(t){200==t.code&&a.$message.success(t.message)}).finally(function(){a.getList()})})},handleRestart:function(t,e){var a=this;this.$confirm("是否要重启?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(b.d)(e.id).then(function(t){200==t.code&&a.$message.success(t.message)}).finally(function(){a.getList()})})},getType:function(t){var e=t.status;return k.e[e]||{}},cancelAddDevice:function(){this.showAddDeviceModal=!1},doAddDevice:function(){var t=this;this.$refs.deviceForm.validate(function(e){if(e){var a=n()({},t.deviceForm);Object(b.b)(a).then(function(e){200==e.code&&(t.showAddDeviceModal=!1,t.$message.success(e.message),t.getList())})}})},handleAddDevice:function(){this.showAddDeviceModal=!0},formatTime:function(t){return t=new Date(t),l()(t).format("YYYY-MM-DD HH:mm:ss")},handleAddSucNew:function(){this.showAddProduct=!1,this.getList()},getList:function(){var t=this;this.listLoading=!0;var e=n()({},this.listQuery);Object(b.c)(e).then(function(e){200==e.code&&(t.list=e.data.list,t.total=e.data.total)}).finally(function(){t.listLoading=!1})},handleAddProduct:function(t){this.deviceInfo=t,this.smcCodeInputTime=0,this.showAddProduct=!0},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleSelectionChange:function(t){this.multipleSelection=t}}},V={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[a("el-button",{staticStyle:{float:"right","margin-left":"20px"},attrs:{type:"primary",size:"small"},on:{click:t.handleAddDevice}},[t._v("\n          添加设备\n        ")]),t._v(" "),a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleAddProduct(null)}}},[t._v("\n          官方录号\n        ")])],1)],1)]),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.id))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"设备名称",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.deviceName))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"设备地址",width:"auto",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.address))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{effect:"dark",type:t.getType(e.row).color}},[t._v(t._s(t.getType(e.row).label))])]}}])}),t._v(" "),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"280",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(a){return t.handleAddProduct(e.row)}}},[t._v("录号")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return t.handleStop(e.$index,e.row)}}},[t._v("停止")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.handleRestart(e.$index,e.row)}}},[t._v("重启")])]}}])})],1)],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("el-dialog",{attrs:{width:"30%",visible:t.showAddDeviceModal},on:{"update:visible":function(e){t.showAddDeviceModal=e}}},[a("el-form",{ref:"deviceForm",attrs:{model:t.deviceForm}},[a("el-form-item",{attrs:{label:"设备地址"}},[a("el-input",{attrs:{placeholder:"请输入设备地址"},model:{value:t.deviceForm.address,callback:function(e){t.$set(t.deviceForm,"address",e)},expression:"deviceForm.address"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"设备名称"}},[a("el-input",{attrs:{placeholder:"请输入设备名称"},model:{value:t.deviceForm.deviceName,callback:function(e){t.$set(t.deviceForm,"deviceName",e)},expression:"deviceForm.deviceName"}})],1),t._v(" "),a("div",{staticClass:"spaceEnd"},[a("div",[a("el-button",{on:{click:t.cancelAddDevice}},[t._v("取消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.doAddDevice}},[t._v("确认")])],1)])],1)],1),t._v(" "),a("el-dialog",{attrs:{width:"70%",visible:t.showAddProduct},on:{"update:visible":function(e){t.showAddProduct=e}}},[t.showAddProduct?a("ProductAddNew",{attrs:{deviceInfo:t.deviceInfo},on:{addsuc:t.handleAddSucNew}}):t._e()],1)],1)},staticRenderFns:[]};var G=a("VU/8")(F,V,!1,function(t){a("j0+K")},"data-v-210199ea",null);e.default=G.exports},"f9/G":function(t,e,a){"use strict";var i={data:function(){return{progressLoading:!1}},mounted:function(){this.start()},methods:{start:function(){var t=this;this.$nextTick(function(){t.progressLoading=!0})},end:function(){var t=this;this.percentage=100,clearInterval(this.timeStart),setTimeout(function(){t.progressLoading=!1,t.$emit("progressEnd")},300)}}},n={render:function(){var t=this.$createElement,e=this._self._c||t;return this.progressLoading?e("div",{staticClass:"loadingModal"},[this._m(0)]):this._e()},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"loadingBox"},[e("div",{staticClass:"tit"},[this._v("\n      正在上号中，请勿关闭此页面\n    ")]),this._v(" "),e("div",[e("i",{staticClass:"el-icon-loading",staticStyle:{"font-size":"30px"}})])])}]};var s=a("VU/8")(i,n,!1,function(t){a("lkmL")},"data-v-b1cde2da",null);e.a=s.exports},"j0+K":function(t,e){},lkmL:function(t,e){},nYtf:function(t,e,a){"use strict";var i=a("mvHQ"),n=a.n(i),s=a("rtXg"),o=a("f9/G"),r={props:{id:{type:[String,Number],default:""},smcCodeInputTime:{type:Number}},data:function(){return{formLabelWidth:"200px",value:{smsCode:""},rules:{smsCode:[{required:!0,message:"请输入短信验证码",trigger:"blur"}]}}},methods:{cancel:function(){this.value.smsCode="",this.$emit("cancel")},submitForm:function(t){var e=this;this.$refs[t].validate(function(t){if(t){var a={id:e.id,smsCode:e.value.smsCode};e.value.smsCode="",Object(s.i)(a).then(function(t){200==t.code&&(e.$message.success("开始录号"),e.$emit("startTask"))})}})}}},l={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.smcCodeInputTime>0?a("el-tag",{staticStyle:{margin:"0 auto",display:"block","margin-bottom":"10px"},attrs:{type:"danger"}},[t._v("\n    验证码超时或输入错误，请重输\n  ")]):t._e(),t._v(" "),a("el-form",{ref:"smsForm",staticClass:"form-box",attrs:{model:t.value,rules:t.rules,"label-width":t.formLabelWidth}},[a("el-form-item",{attrs:{label:"录号短信验证码",prop:"smsCode"}},[a("el-input",{attrs:{placeholder:"请输入短信验证码"},model:{value:t.value.smsCode,callback:function(e){t.$set(t.value,"smsCode",e)},expression:"value.smsCode"}})],1),t._v(" "),a("div",{staticClass:"m-footer"},[a("div",{staticClass:"spaceEnd"},[a("div",[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("smsForm")}}},[t._v("确认")])],1)])])],1)],1)},staticRenderFns:[]};var u=a("VU/8")(r,l,!1,function(t){a("Yga/")},"data-v-521d5f79",null).exports,c={components:{myProgress:o.a,SMS:u},props:{taskId:{type:Number}},data:function(){return{showSmsModal:!1,smcCodeInputTime:0,stateImg:"",showProgress:!1}},methods:{handleNext:function(){var t=this;Object(s.m)(this.upSmsObj).then(function(e){200==e.code&&(t.stateImg="")})},showStateImg:function(t){this.stateImg=t},startTask:function(){this.showSmsModal=!1,this.smcCodeInputTime=this.smcCodeInputTime+1},startCheckState:function(t){var e=this;this.showProgress=!0,this.stl=setInterval(function(){Object(s.h)(t).then(function(t){if(200==t.code&&t.data&&t.data.propertyBag){var a=JSON.parse(t.data.propertyBag);a.state.includes("need_show_")?(a.state=a.state+"_done",e.upSmsObj=t.data,e.upSmsObj.propertyBag=n()(a),e.showStateImg(a.state_img)):"need_sms_code"===a.state&&e.smcCodeInputTime<1?e.showSmsModal=!0:"need_sms_code2"===a.state&&e.smcCodeInputTime<2?e.showSmsModal=!0:"login_success"!==a.state&&"login_timeout"!==a.state&&"login_fail"!==a.state||(e.showProgress=!1,e.stl=clearInterval(e.stl),e.$message.success("开始录号"),e.value=e.emptyFormData,e.smcCodeInputTime=0,e.$emit("finish"))}})},5e3)}},mounted:function(){this.startCheckState(this.taskId)}},d={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.showProgress?a("my-progress",{on:{progressEnd:function(e){t.showProgress=!1}}}):t._e(),t._v(" "),t.stateImg?a("div",{staticClass:"stateImgBox"},[a("div",{staticClass:"tit"},[t._v("请根据图片引导用户进行操作")]),t._v(" "),a("el-image",{staticClass:"stateImg",attrs:{src:t.stateImg,fit:"contain"}}),t._v(" "),a("div",{staticClass:"spaceEnd"},[a("el-button",{staticClass:"btn",attrs:{type:"primary"},on:{click:t.handleNext}},[t._v("用户已完成操作")])],1)],1):t._e(),t._v(" "),t.showSmsModal?a("div",{staticClass:"stateImgBox"},[a("SMS",{attrs:{id:t.taskId,smcCodeInputTime:t.smcCodeInputTime},on:{cancel:function(e){t.showSmsModal=!1},startTask:t.startTask}})],1):t._e()],1)},staticRenderFns:[]};var p=a("VU/8")(c,d,!1,function(t){a("6R4F")},"data-v-02137a5b",null);e.a=p.exports},rtXg:function(t,e,a){"use strict";e.c=function(t){return Object(i.a)({url:"/record/device/list",method:"get",params:t})},e.b=function(t){return Object(i.a)({url:"/record/device/add",method:"post",data:t})},e.e=function(t){return Object(i.a)({url:"/record/device/stop/"+t,method:"post"})},e.d=function(t){return Object(i.a)({url:"/record/device/restart/"+t,method:"post"})},e.a=function(t){return Object(i.a)({url:"/product/createLuhaoV2",method:"post",data:t})},e.h=function(t){return Object(i.a)({url:n+"/record/full_task/"+t,method:"get"})},e.m=function(t){return Object(i.a)({url:n+"/record/task/update",method:"post",data:t})},e.k=function(t){return Object(i.a)({url:n+"/record/task/cancel",method:"post",data:t})},e.i=function(t){return Object(i.a)({url:"/product/recordTask/smsCode",method:"post",data:t})},e.l=function(t){return Object(i.a)({url:"/record/task/list",method:"get",params:t})},e.j=function(t){return Object(i.a)({url:"/record/resumeTask",method:"post",data:t})},e.f=function(t){return Object(i.a)({url:"/record/task/stageList?taskId="+t,method:"get"})},e.g=function(){return Object(i.a)({url:"/record/task/stats",method:"get"})};var i=a("vLgD"),n="https://api2.kkzhw.com/mall-portal"}});