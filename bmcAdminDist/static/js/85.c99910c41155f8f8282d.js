webpackJsonp([85],{"6NYT":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("//Fk"),i=a.n(n),l=a("woOf"),s=a.n(l),r=a("tUEa"),o=a("UgCr"),u=a("0QkR"),c=a("xT6B"),d=a("TZVV"),p=a("mRsl"),f=a("II7+"),v=a("0xDb"),h=0,m={pageNum:1,pageSize:20,brandId:64},b={name:"Kefuguan<PERSON>",components:{SingleUpload:d.a,tedian:f.a},filters:{formatCreateTime:function(e){var t=new Date(e);return Object(c.a)(t,"YYYY-MM-DD HH:mm:ss")},verifyStatusFilter:function(e){return e?"启用":"禁用"},verifyStatusFilter2:function(e){return e?"禁用":"启用"},typekefuFilter:function(e){switch(e){case 1:return"游戏";case 2:return"仅供查询";case 3:return"投诉/售后";case 4:return"账号担保";default:return""}}},data:function(){return{detailOptions:[],opetionDate:[],subjectList:[],subjectTitles:["待选择","已选择"],prefrenceAreaList:[],prefrenceAreaTitles:["待选择","已选择"],productCategoryOptions:[],kefuOptions:[{value:1,name:"游戏"},{value:2,name:"仅供查询"},{value:3,name:"投诉/售后"},{value:4,name:"账号担保"}],listLoading:!0,list:[],total:0,showAddModel:!1,listQuery:s()({},m),value:{name:"",gameCareinfoVx:"",description:"",pic:"",productCategoryId:"",publishStatus:"",subjectProductRelationList:[]},rules:{},formLabelWidth:"140px"}},computed:{selectSubject:{get:function(){var e=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return e;for(var t=0;t<this.value.subjectProductRelationList.length;t++)e.push(this.value.subjectProductRelationList[t].subjectId);return e},set:function(e){this.value.subjectProductRelationList=[];for(var t=0;t<e.length;t++)this.value.subjectProductRelationList.push({subjectId:e[t]})}}},created:function(){this.getList()},methods:{getGameCate:function(){var e=this;return new i.a(function(t,a){i.a.all([Object(p.d)(74,{pageNum:1,pageSize:999}),Object(p.d)(73,{pageNum:1,pageSize:999})]).then(function(a){var n=[];n.push({icon:"",name:"手游",splitTitle:1,checked:!1});var i=a[0].data.list.map(function(e){return{icon:"",name:e.name,checked:!1}});(n=n.concat(i)).push({icon:"",name:"端游",splitTitle:1,checked:!1});var l=a[1].data.list.map(function(e){return{icon:"",name:e.name,checked:!1}}),s={childList:n=n.concat(l),name:"关联游戏",is_required:0,field_type:2,default_word:"点击可下拉选择",tdtype:3,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[],id:+new Date};e.opetionDate=[s],t()})})},getSubjectList:function(){var e=this;Object(u.d)().then(function(t){e.subjectList=[];for(var a=t.data,n=0;n<a.length;n++)5===a[n].categoryId&&e.subjectList.push({label:a[n].title,key:a[n].id})})},handleDelete:function(e,t){var a=this;this.$confirm("是否要进行删除操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=[];e.push(t.id),a.productZBDelete(1,e)})},productZBDelete:function(e,t){var a=this,n=new URLSearchParams;n.append("ids",t),n.append("deleteStatus",e),Object(o.v)(n).then(function(e){a.$message({message:"删除成功",type:"success",duration:1e3})}),this.getList()},filterMethod:function(e,t){return t.label.indexOf(e)>-1},getButtonType:function(e){return e?"danger":"success"},toggleState:function(e,t){var a=this,n=t.id,i=s()({},t);i.publishStatus=t.publishStatus?0:1,Object(o.w)(n,i).then(function(){a.getList()})},getList:function(){var e=this;this.listLoading=!0,Object(p.e)().then(function(t){var a=t.data;e.productCategoryOptions=a.find(function(e){return"主播管理"===e.name}).children,Object(r.e)(e.listQuery).then(function(t){e.listLoading=!1;var a=t.data.list;e.list=a,e.total=t.data.total})})},handleUpdateProduct:function(e,t){var a=this,n=t.id;this.getGameCate().then(function(e){a.getSubjectList(),Object(r.h)(n).then(function(e){if(a.value=s()({},e.data),a.id=n,a.isEdit=!0,a.detailOptions=[],a.showAddModel=!0,a.value.subTitle){var t=[];a.value.subTitle.split(",").forEach(function(e){t.push({icon:"",name:e,checked:!0})}),a.detailOptions=[{title:"关联游戏",tdtype:3,value:t,id:h++}]}else a.detailOptions=[];v.b.async2opetionDate(a.detailOptions,a.opetionDate)})})},createHelp:function(){var e=this;this.getGameCate().then(function(t){e.getSubjectList(),e.clearValue(),e.id="",e.isEdit=!1,e.showAddModel=!0})},clearValue:function(){this.detailOptions=[],this.value={name:"",gameCareinfoVx:"",description:"",pic:"",productCategoryId:"",publishStatus:"",subjectProductRelationList:[]}},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},cancel:function(){this.showAddModel=!1},submitForm:function(){var e=this;this.showAddModel=!1;var t=this.opetionDate[0].choosedList.map(function(e){return e.name});this.value.subTitle=t.join(",");var a=this.productCategoryOptions.find(function(t){return e.value.productCategoryId===t.id});if(this.value.productCategoryName=a.name,this.isEdit)Object(o.w)(this.id,this.value).then(function(){e.getList()});else{var n=s()({brandId:64},this.value);Object(o.u)(n).then(function(){e.getList()})}}}},g={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"table-container"},[a("el-button",{staticClass:"add-help",attrs:{type:"primary"},on:{click:e.createHelp}},[e._v("新建主播")]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[a("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"主播名称",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.name))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"主播封面图",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("img",{staticClass:"zb-pic",attrs:{src:e.row.pic}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"主播直播地址",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.gameCareinfoVx))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"主播类型",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.productCategoryName))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"所属游戏",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.subTitle))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"主播状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(e._f("verifyStatusFilter")(t.row.publishStatus)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatCreateTime")(t.row.createTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleUpdateProduct(t.$index,t.row)}}},[e._v("详情/编辑\n            ")]),e._v(" "),a("el-button",{attrs:{type:e.getButtonType(t.row.publishStatus),size:"mini"},on:{click:function(a){return e.toggleState(t.$index,t.row)}}},[e._v("\n              "+e._s(e._f("verifyStatusFilter2")(t.row.publishStatus))+"\n            ")]),e._v(" "),a("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(a){return e.handleDelete(t.$index,t.row)}}},[e._v("\n              删除\n            ")])],1)]}}])})],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),e.showAddModel?a("el-dialog",{attrs:{visible:e.showAddModel,width:"80%",top:"1vh"},on:{"update:visible":function(t){e.showAddModel=t}}},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("编辑主播信息")])]),e._v(" "),a("el-form",{ref:"helpForm",staticClass:"form-box",attrs:{model:e.value,rules:e.rules,"label-width":e.formLabelWidth}},[a("el-form-item",{attrs:{label:"主播名称"}},[a("el-input",{model:{value:e.value.name,callback:function(t){e.$set(e.value,"name",t)},expression:"value.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"主播链接"}},[a("el-input",{model:{value:e.value.gameCareinfoVx,callback:function(t){e.$set(e.value,"gameCareinfoVx",t)},expression:"value.gameCareinfoVx"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"排序"}},[a("el-input",{model:{value:e.value.sort,callback:function(t){e.$set(e.value,"sort",t)},expression:"value.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"主播类型"}},[a("el-select",{model:{value:e.value.productCategoryId,callback:function(t){e.$set(e.value,"productCategoryId",t)},expression:"value.productCategoryId"}},e._l(e.productCategoryOptions,function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"启用状态"}},[a("el-radio-group",{model:{value:e.value.publishStatus,callback:function(t){e.$set(e.value,"publishStatus",t)},expression:"value.publishStatus"}},[a("el-radio",{attrs:{label:1}},[e._v("启用")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("禁用")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"主播头像"}},[a("single-upload",{model:{value:e.value.pic,callback:function(t){e.$set(e.value,"pic",t)},expression:"value.pic"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{model:{value:e.value.gameCareinfoTime,callback:function(t){e.$set(e.value,"gameCareinfoTime",t)},expression:"value.gameCareinfoTime"}})],1),e._v(" "),a("el-form-item",{attrs:{label:""}},[a("tedian",{attrs:{"detail-options":e.detailOptions,"opetion-date":e.opetionDate}})],1)],1),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("helpForm")}}},[e._v("确 定")])],1)],1)],1):e._e()],1)])},staticRenderFns:[]};var _=a("VU/8")(b,g,!1,function(e){a("dGLa")},"data-v-44638dff",null);t.default=_.exports},dGLa:function(e,t){}});