webpackJsonp([80],{"8zfe":function(e,t){},vjbg:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r("woOf"),n=r.n(i),a=r("RRo+"),o=r.n(a),l=r("vLgD");var s={id:null,flashOrderOvertime:0,normalOrderOvertime:0,confirmOvertime:0,finishOvertime:0,commentOvertime:0},m=function(e,t,r){if(!t)return r(new Error("时间不能为空"));console.log("checkTime",t);var i=parseInt(t);if(!o()(i))return r(new Error("请输入数字值"));r()},d={name:"orderSetting",data:function(){return{orderSetting:n()({},s),rules:{flashOrderOvertime:{validator:m,trigger:"blur"},normalOrderOvertime:{validator:m,trigger:"blur"},confirmOvertime:{validator:m,trigger:"blur"},finishOvertime:{validator:m,trigger:"blur"},commentOvertime:{validator:m,trigger:"blur"}}}},created:function(){this.getDetail()},methods:{confirm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return t.$message({message:"提交参数不合法",type:"warning"}),!1;t.$confirm("是否要提交修改?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e,r;(e=1,r=t.orderSetting,Object(l.a)({url:"/orderSetting/update/"+e,method:"post",data:r})).then(function(e){t.$message({type:"success",message:"提交成功!",duration:1e3})})})})},getDetail:function(){var e,t=this;(e=1,Object(l.a)({url:"/orderSetting/"+e,method:"get"})).then(function(e){t.orderSetting=e.data})}}},c={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-card",{staticClass:"form-container",attrs:{shadow:"never"}},[r("el-form",{ref:"orderSettingForm",attrs:{model:e.orderSetting,rules:e.rules,"label-width":"150px"}},[r("el-form-item",{attrs:{label:"秒杀订单超过：",prop:"flashOrderOvertime"}},[r("el-input",{staticClass:"input-width",model:{value:e.orderSetting.flashOrderOvertime,callback:function(t){e.$set(e.orderSetting,"flashOrderOvertime",t)},expression:"orderSetting.flashOrderOvertime"}},[r("template",{slot:"append"},[e._v("分")])],2),e._v(" "),r("span",{staticClass:"note-margin"},[e._v("未付款，订单自动关闭")])],1),e._v(" "),r("el-form-item",{attrs:{label:"正常订单超过：",prop:"normalOrderOvertime"}},[r("el-input",{staticClass:"input-width",model:{value:e.orderSetting.normalOrderOvertime,callback:function(t){e.$set(e.orderSetting,"normalOrderOvertime",t)},expression:"orderSetting.normalOrderOvertime"}},[r("template",{slot:"append"},[e._v("分")])],2),e._v(" "),r("span",{staticClass:"note-margin"},[e._v("未付款，订单自动关闭")])],1),e._v(" "),r("el-form-item",{attrs:{label:"发货超过：",prop:"confirmOvertime"}},[r("el-input",{staticClass:"input-width",model:{value:e.orderSetting.confirmOvertime,callback:function(t){e.$set(e.orderSetting,"confirmOvertime",t)},expression:"orderSetting.confirmOvertime"}},[r("template",{slot:"append"},[e._v("天")])],2),e._v(" "),r("span",{staticClass:"note-margin"},[e._v("未收货，订单自动完成")])],1),e._v(" "),r("el-form-item",{attrs:{label:"订单完成超过：",prop:"finishOvertime"}},[r("el-input",{staticClass:"input-width",model:{value:e.orderSetting.finishOvertime,callback:function(t){e.$set(e.orderSetting,"finishOvertime",t)},expression:"orderSetting.finishOvertime"}},[r("template",{slot:"append"},[e._v("天")])],2),e._v(" "),r("span",{staticClass:"note-margin"},[e._v("自动结束交易，不能申请售后")])],1),e._v(" "),r("el-form-item",{attrs:{label:"订单完成超过：",prop:"commentOvertime"}},[r("el-input",{staticClass:"input-width",model:{value:e.orderSetting.commentOvertime,callback:function(t){e.$set(e.orderSetting,"commentOvertime",t)},expression:"orderSetting.commentOvertime"}},[r("template",{slot:"append"},[e._v("天")])],2),e._v(" "),r("span",{staticClass:"note-margin"},[e._v("自动五星好评")])],1),e._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.confirm("orderSettingForm")}}},[e._v("提交")])],1)],1)],1)},staticRenderFns:[]};var v=r("VU/8")(d,c,!1,function(e){r("8zfe")},"data-v-4a9bb782",null);t.default=v.exports}});