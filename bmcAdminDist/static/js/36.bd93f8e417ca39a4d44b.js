webpackJsonp([36],{AOVd:function(e,t){},P8ef:function(e,t){},gEzv:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("woOf"),i=a.n(l),n=a("PJh5"),s=a.n(n),r=(a("IcnI"),a("UgCr")),o=a("caBC"),u=a("3idm"),c=a("s/Rn"),d=a("mRsl"),p=a("fZjL"),v=a.n(p),h=a("pFYg"),f=a.n(h),m=a("c/Tr"),b=a.n(m),g=a("lHA8"),S=a.n(g),y=a("mvHQ"),k=a.n(y),_=a("M4fF"),w=a.n(_),C=a("KhLR"),x=(a("n97X"),a("0QkR")),L=a("TZVV"),P=a("sl7S"),I=a("5aCZ"),A=a("II7+"),O={productAttributeValueList:[],brandId:"",productAttributeCategoryId:"",gameAccountQufu:"",description:"",description2:0,pic:"",description3:0,selectProductPics:[],price:"",originalPrice:"",stock:0,gameGoodsFangxin:0,gameGoodsBukuan:0,gameGoodsJiangjia:0,gameGoodsYijia:0,sort:"",publishStatus:0,gameGoodsYishou:0,gameGoodsYuyue:0,gameGoodsSaletype:1,gameCareinfoPhone:"",gameCareinfoTime:"",gameCareinfoVx:"",subjectProductRelationList:[]},$=0,V={name:"ProductAdd",components:{SingleUpload:L.a,MultiUpload:P.a,Tinymce:I.a,tedian:A.a},props:{queryId:{type:[Number,String],default:""}},data:function(){return{tabValue:"0",active:0,value:i()({},O),hasEditCreated:!1,selectProductCateValue:[],productCateOptions:[],brandOptions:[],productAttributeCategoryOptions:[],selectProductAttr:[],selectProductParam:[],selectProductAttrPics:[],addProductAttrValue:"",activeHtmlName:"pc",rules:{},formLabelWidth:"200px",spanCol:12,extList:{ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]}},subjectList:[],subjectTitles:["待选择","已选择"]}},computed:{isEdit:function(){return""!==this.queryId},productId:function(){return this.value.id},selectServiceList:{get:function(){var e=[];if(void 0===this.value.serviceIds||null==this.value.serviceIds||""===this.value.serviceIds)return e;for(var t=this.value.serviceIds.split(","),a=0;a<t.length;a++)e.push(Number(t[a]));return e},set:function(e){var t="";if(null!=e&&e.length>0){for(var a=0;a<e.length;a++)t+=e[a]+",";t.endsWith(",")&&(t=t.substr(0,t.length-1)),this.value.serviceIds=t}else this.value.serviceIds=null}},selectProductPics:{get:function(){var e=[];if(void 0===this.value.albumPics||null==this.value.albumPics||""===this.value.albumPics)return e;for(var t=this.value.albumPics.split(","),a=0;a<t.length;a++)e.push(t[a]);return e},set:function(e){if(null==e||0===e.length)this.value.albumPics=null;else if(this.value.albumPics="",e.length>0)for(var t=0;t<e.length;t++)this.value.albumPics+=e[t],t!==e.length-1&&(this.value.albumPics+=",")}},selectSubject:{get:function(){var e=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return e;for(var t=0;t<this.value.subjectProductRelationList.length;t++)e.push(this.value.subjectProductRelationList[t].subjectId);return e},set:function(e){this.value.subjectProductRelationList=[];for(var t=0;t<e.length;t++)this.value.subjectProductRelationList.push({subjectId:e[t]})}}},watch:{selectProductCateValue:function(e){null!=e&&2===e.length?(this.value.productCategoryId=e[1],this.value.productCategoryName=this.getCateNameById(this.value.productCategoryId)):(this.value.productCategoryId=null,this.value.productCategoryName=null)},productId:function(e){this.isEdit&&(this.hasEditCreated||void 0!==e&&null!=e&&0!==e&&(this.handleEditCreatedInfo(),this.handleEditCreatedAttr()))}},created:function(){var e=this;this.isEdit&&Object(r.j)(this.queryId).then(function(t){e.value=i()({},e.value,t.data),e.saveSkuStockList=w.a.cloneDeep(e.value.skuStockList)}),this.getProductCateList(),this.getBrandList(),this.isEdit,this.getProductAttrCateList(),this.getSubjectList()},methods:{handleRemoveProductSku:function(e,t){var a=this.value.skuStockList;1===a.length?a.pop():a.splice(e,1);var l=[];a.forEach(function(e){var t=JSON.parse(e.spData)[0].value;l.push(t)}),this.$set(this.selectProductAttr[0],"values",l)},changequfu:function(e){this.value.gameAccountQufu=e},changeTab:function(e,t){},handleEditCreatedInfo:function(){null!=this.value.productCategoryId&&(this.selectProductCateValue=[],this.selectProductCateValue.push(this.value.cateParentId),this.selectProductCateValue.push(this.value.productCategoryId)),this.hasEditCreated=!0},getProductCateList:function(){var e=this;Object(d.e)().then(function(t){var a=t.data;e.productCateOptions=[];for(var l=0;l<a.length;l++){var i=[];if(null!=a[l].children&&a[l].children.length>0)for(var n=0;n<a[l].children.length;n++)i.push({label:a[l].children[n].name,value:a[l].children[n].id});e.productCateOptions.push({label:a[l].name,value:a[l].id,children:i})}})},getBrandList:function(){var e=this;Object(c.c)({pageNum:1,pageSize:200}).then(function(t){e.brandOptions=[];for(var a=t.data.list,l=0;l<a.length;l++)e.brandOptions.push({label:a[l].name,value:a[l].id})})},getCateNameById:function(e){for(var t=null,a=0;a<this.productCateOptions.length;a++)for(var l=0;l<this.productCateOptions[a].children.length;l++)if(this.productCateOptions[a].children[l].value===e)return t=this.productCateOptions[a].children[l].label;return t},handleBrandChange:function(e){for(var t="",a=0;a<this.brandOptions.length;a++)if(this.brandOptions[a].value===e){t=this.brandOptions[a].label;break}this.value.brandName=t},handleEditCreated:function(){var e=this.value.serviceIds.split(",");console.log("handleEditCreated",e);for(var t=0;t<e.length;t++)this.selectServiceList.push(Number(e[t]))},handleRemoveProductLadder:function(e,t){var a=this.value.productLadderList;1===a.length?(a.pop(),a.push({count:0,discount:0,price:0})):a.splice(e,1)},handleAddProductLadder:function(e,t){var a=this.value.productLadderList;a.length<3?a.push({count:0,discount:0,price:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handleRemoveFullReduction:function(e,t){var a=this.value.productFullReductionList;1===a.length?(a.pop(),a.push({fullPrice:0,reducePrice:0})):a.splice(e,1)},handleAddFullReduction:function(e,t){var a=this.value.productFullReductionList;a.length<3?a.push({fullPrice:0,reducePrice:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handleEditCreatedAttr:function(){null!=this.value.productAttributeCategoryId&&this.handleProductAttrChange(this.value.productAttributeCategoryId),this.hasEditCreated=!0},getProductAttrCateList:function(){var e=this;Object(C.c)({pageNum:1,pageSize:200}).then(function(t){e.productAttributeCategoryOptions=[];for(var a=t.data.list,l=0;l<a.length;l++)e.productAttributeCategoryOptions.push({label:a[l].name,value:a[l].id})})},getProductAttrList:function(e,t){var a=this,l={pageNum:1,pageSize:200,type:e};Object(u.c)(t,l).then(function(t){var l=t.data.list;if(0!==e){var i="基础信息扩展";2===e?i="账号信息扩展":3===e&&(i="其他扩展");var n={index:parseInt(e,10),label:i,needShow:l&&l.length>0},s=a.getEditAttrOptions2(l),r=[];n.opetionDate=s;for(var o=0;o<l.length;o++){var u=null;a.isEdit&&(u=a.getEditParamValue2(l[o]),r.push(u))}n.detailOptions=r,a.$set(a.extList,"ext"+e,n)}else{a.isEdit||(a.selectProductAttr=[]);for(var c=0;c<l.length;c++){var d=[];a.isEdit&&(d=a.getEditAttrValues(c)),a.selectProductAttr.push({id:l[c].id,name:l[c].name,handAddStatus:l[c].handAddStatus,inputList:l[c].inputList,values:d,options:[]})}}})},getProductSkuSp:function(e,t){var a=JSON.parse(e.spData);return null!=a&&t<a.length?a[t].value:null},refreshProductSkuList:function(e){this.value.skuStockList=this.value.skuStockList||[];var t=this.value.skuStockList;if(1===this.selectProductAttr.length)if(e.length>t.length){var a=e[e.length-1],l={spData:k()([{key:a,value:a}])};if(this.isEdit){var i=this.saveSkuStockList.find(function(e){return JSON.parse(e.spData)[0].value===a});i&&i.skuCode&&(l.skuCode=i.skuCode)}t.push(l)}else{var n=t.findIndex(function(t){var a=JSON.parse(t.spData)[0].value;if(!e.find(function(e){return a===e}))return!0});t.splice(n,1)}this.$forceUpdate()},getInputListArr:function(e){return e.split(",")},selectProductAttrChange:function(e){this.refreshProductSkuList(e)},getOptionStr:function(e){for(var t="",a=0;a<e.length;a++)t+=e[a],a!=e.length-1&&(t+=",");return t},getEditAttrOptions2:function(e){return e.map(function(e){var t=1;if(1===e.inputType&&(1===e.selectType?t=2:2===e.selectType?t=3:3===e.selectType&&(t=4)),1===t)return{childList:[],name:e.name,is_required:0,field_type:2,default_word:"请输入",type:t,iptVal:"",id:e.id};if(2===t)return{name:e.name,type:t,value:"",is_required:0,field_type:2,inputList:e.inputList.split(","),id:e.id};if(3===t){var a=[];return e.inputList.split(",").forEach(function(e){a.push({icon:"",name:e,checked:!1})}),{childList:a,name:e.name,is_required:0,field_type:2,default_word:"点击可下拉选择物品",type:t,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[],id:e.id}}var l=JSON.parse(e.inputList);return l.forEach(function(e){e.value=e.parent_name,e.label=e.parent_name;var t=e.childList.map(function(e){return{value:e,label:e}});e.children=t}),{name:e.name,type:t,value:[],is_required:0,field_type:2,options:l,id:e.id}})},getEditAttrValues:function(e){var t=new S.a;if(0===e)for(var a=0;a<this.value.skuStockList.length;a++){var l=this.value.skuStockList[a],i=JSON.parse(l.spData);null!=i&&i.length>=1&&t.add(i[0].value)}else if(1===e)for(var n=0;n<this.value.skuStockList.length;n++){var s=this.value.skuStockList[n],r=JSON.parse(s.spData);null!=r&&r.length>=2&&t.add(r[1].value)}else for(var o=0;o<this.value.skuStockList.length;o++){var u=this.value.skuStockList[o],c=JSON.parse(u.spData);null!=c&&c.length>=3&&t.add(c[2].value)}return b()(t)},getEditParamValue2:function(e){var t=1;1===e.inputType&&(1===e.selectType?t=2:2===e.selectType?t=3:3===e.selectType&&(t=4));for(var a=0;a<this.value.productAttributeValueList.length;a++)if(e.id===this.value.productAttributeValueList[a].productAttributeId){var l=this.value.productAttributeValueList[a];if(1===t)return{childList:[],name:e.name,is_required:0,field_type:2,default_word:"请输入",type:t,iptVal:l.value,id:$++};if(2===t)return{childList:[],name:e.name,is_required:0,field_type:2,default_word:"请输入",type:t,value:l.value,id:$++};if(3!==t)return{title:e.name,type:t,value:(l.value||"").split(","),options:JSON.parse(e.inputList),id:$++};var i=function(){var a=l.value.split(",");""===l.value&&(a=[]);var i=[];return a.forEach(function(e){i.push({icon:"",name:e,checked:!0})}),{v:{title:e.name,type:t,value:i,id:$++}}}();if("object"===(void 0===i?"undefined":f()(i)))return i.v}},handleProductAttrChange:function(e){this.extList={ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]}},this.getProductAttrList(0,e),this.getProductAttrList(1,e),this.getProductAttrList(2,e),this.getProductAttrList(3,e),this.getProductAttrList(4,e)},getParamInputList:function(e){return e.split(",")},submitForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return t.$message({message:"验证失败",type:"error",duration:1e3}),!1;t.finishCommit(t.isEdit)})},filterMethod:function(e,t){return t.label.indexOf(e)>-1},getSubjectList:function(){var e=this;Object(x.d)().then(function(t){for(var a=t.data,l=0;l<a.length;l++)4!==a[l].categoryId&&5!==a[l].categoryId&&e.subjectList.push({label:a[l].title,key:a[l].id})})},cancel:function(){this.$emit("addsuc")},finishCommit:function(e){var t=this;this.$confirm("是否要提交该产品","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.value.productAttributeValueList=[];for(var a=v()(t.extList),l=0;l<a.length;l++)for(var i=a[l],n=t.extList[i].opetionDate,s=0;s<n.length;s++){var o=n[s],u=o.value||"";3===o.type&&o.choosedList.length?u=o.choosedList.map(function(e){return e.name}).join(","):1===o.type?u=o.iptVal:4===o.type&&(u=o.value.join(",")),t.value.productAttributeValueList.push({productAttributeId:o.id,value:u,attriName:o.name})}t.value.albumPics="";var c=t.$refs.muupload.fileList;if(c&&c.length){var d=t.$refs.muupload.fileList.map(function(e){return e.url});if(d.length>0)for(var p=0;p<d.length;p++)t.value.albumPics+=d[p],p!==d.length-1&&(t.value.albumPics+=",")}e?Object(r.C)(t.queryId,t.value).then(function(e){t.$message({type:"success",message:"提交成功",duration:1e3}),t.$emit("addsuc")}):Object(r.d)(t.value).then(function(e){t.$message({type:"success",message:"提交成功",duration:1e3}),location.reload()})})}}},j={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{on:{"tab-click":e.changeTab},model:{value:e.tabValue,callback:function(t){e.tabValue=t},expression:"tabValue"}},[a("el-tab-pane",{attrs:{label:"基本信息"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"营销信息"}})],1),e._v(" "),a("el-form",{ref:"productForm",staticClass:"form-box",attrs:{model:e.value,rules:e.rules,"label-width":e.formLabelWidth}},[a("div",{directives:[{name:"show",rawName:"v-show",value:"0"===e.tabValue,expression:"tabValue === '0'"}]},[a("el-card",{staticClass:"card-box"},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.value.productSn,expression:"value.productSn"}],attrs:{label:"商品编号：",prop:"productSn"}},[a("el-input",{attrs:{disabled:""},model:{value:e.value.productSn,callback:function(t){e.$set(e.value,"productSn",t)},expression:"value.productSn"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"游戏类型：",prop:"productCategoryId"}},[a("el-cascader",{attrs:{options:e.productCateOptions},model:{value:e.selectProductCateValue,callback:function(t){e.selectProductCateValue=t},expression:"selectProductCateValue"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"游戏品牌：",prop:"brandId"}},[a("el-select",{attrs:{placeholder:"请选择品牌"},on:{change:e.handleBrandChange},model:{value:e.value.brandId,callback:function(t){e.$set(e.value,"brandId",t)},expression:"value.brandId"}},e._l(e.brandOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"游戏SKU："}},[a("el-select",{attrs:{placeholder:"请选择属性类型"},on:{change:e.handleProductAttrChange},model:{value:e.value.productAttributeCategoryId,callback:function(t){e.$set(e.value,"productAttributeCategoryId",t)},expression:"value.productAttributeCategoryId"}},e._l(e.productAttributeCategoryOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"区服信息：",prop:"gameAccountQufu"}},[a("el-input",{attrs:{disabled:""},model:{value:e.value.gameAccountQufu,callback:function(t){e.$set(e.value,"gameAccountQufu",t)},expression:"value.gameAccountQufu"}})],1),e._v(" "),e.extList.ext1.needShow?a("div",{staticClass:"ext1",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext1.detailOptions,"opetion-date":e.extList.ext1.opetionDate},on:{changequfu:e.changequfu}})],1):e._e()],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("商品规格")]),e._v(" "),a("el-form-item",{attrs:{label:"售价金额：",prop:"price"}},[a("el-input",{model:{value:e.value.price,callback:function(t){e.$set(e.value,"price",t)},expression:"value.price"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"心理底价：",prop:"originalPrice"}},[a("el-input",{model:{value:e.value.originalPrice,callback:function(t){e.$set(e.value,"originalPrice",t)},expression:"value.originalPrice"}})],1),e._v(" "),a("el-form-item",{staticStyle:{width:"90%"},attrs:{label:"商品规格："}},[a("el-card",{staticClass:"cardBg",attrs:{shadow:"never"}},e._l(e.selectProductAttr,function(t,l){return a("div",{key:l},[e._v("\n              "+e._s(t.name)+"：\n              "),0===t.handAddStatus?a("el-checkbox-group",{on:{change:e.selectProductAttrChange},model:{value:e.selectProductAttr[l].values,callback:function(t){e.$set(e.selectProductAttr[l],"values",t)},expression:"selectProductAttr[idx].values"}},e._l(e.getInputListArr(t.inputList),function(e){return a("el-checkbox",{key:e,staticClass:"littleMarginLeft",attrs:{label:e}})}),1):e._e()],1)}),0),e._v(" "),a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.value.skuStockList,border:""}},[e._l(e.selectProductAttr,function(t,l){return a("el-table-column",{key:t.id,attrs:{label:t.name,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                "+e._s(e.getProductSkuSp(t.row,l))+"\n              ")]}}],null,!0)})}),e._v(" "),a("el-table-column",{attrs:{label:"销售价格",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.price,callback:function(a){e.$set(t.row,"price",a)},expression:"scope.row.price"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"促销价格",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.promotionPrice,callback:function(a){e.$set(t.row,"promotionPrice",a)},expression:"scope.row.promotionPrice"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"商品库存",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.stock,callback:function(a){e.$set(t.row,"stock",a)},expression:"scope.row.stock"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"SKU编号",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{disabled:""},model:{value:t.row.skuCode,callback:function(a){e.$set(t.row,"skuCode",a)},expression:"scope.row.skuCode"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleRemoveProductSku(t.$index,t.row)}}},[e._v("删除\n                ")])]}}])})],2)],1)],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("账号信息")]),e._v(" "),a("el-form-item",{attrs:{label:"权重排序：",prop:"sort"}},[a("el-input",{model:{value:e.value.sort,callback:function(t){e.$set(e.value,"sort",t)},expression:"value.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"访问量：",prop:"gameSysinfoReadcount"}},[a("el-input",{model:{value:e.value.gameSysinfoReadcount,callback:function(t){e.$set(e.value,"gameSysinfoReadcount",t)},expression:"value.gameSysinfoReadcount"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否已售：",prop:"stock"}},[a("el-radio-group",{model:{value:e.value.stock,callback:function(t){e.$set(e.value,"stock",t)},expression:"value.stock"}},[a("el-radio",{attrs:{label:0}},[e._v("已售")]),e._v(" "),a("el-radio",{attrs:{label:9}},[e._v("在售")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("已预订")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否放心账号：",prop:"gameGoodsFangxin"}},[a("el-radio-group",{model:{value:e.value.gameGoodsFangxin,callback:function(t){e.$set(e.value,"gameGoodsFangxin",t)},expression:"value.gameGoodsFangxin"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否补款商品：",prop:"gameGoodsBukuan"}},[a("el-radio-group",{model:{value:e.value.gameGoodsBukuan,callback:function(t){e.$set(e.value,"gameGoodsBukuan",t)},expression:"value.gameGoodsBukuan"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否降价：",prop:"gameGoodsJiangjia"}},[a("el-radio-group",{model:{value:e.value.gameGoodsJiangjia,callback:function(t){e.$set(e.value,"gameGoodsJiangjia",t)},expression:"value.gameGoodsJiangjia"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"可否议价：",prop:"gameGoodsYijia"}},[a("el-radio-group",{model:{value:e.value.gameGoodsYijia,callback:function(t){e.$set(e.value,"gameGoodsYijia",t)},expression:"value.gameGoodsYijia"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否上架：",prop:"publishStatus"}},[a("el-radio-group",{model:{value:e.value.publishStatus,callback:function(t){e.$set(e.value,"publishStatus",t)},expression:"value.publishStatus"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否一手：",prop:"gameGoodsYishou"}},[a("el-radio-group",{model:{value:e.value.gameGoodsYishou,callback:function(t){e.$set(e.value,"gameGoodsYishou",t)},expression:"value.gameGoodsYishou"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"出售方式：",prop:"gameGoodsSaletype"}},[a("el-radio-group",{model:{value:e.value.gameGoodsSaletype,callback:function(t){e.$set(e.value,"gameGoodsSaletype",t)},expression:"value.gameGoodsSaletype"}},[a("el-radio",{attrs:{label:1}},[e._v("平台代售")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("合作号商回收")])],1)],1),e._v(" "),a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"账号描述：",prop:"description"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.value.description,callback:function(t){e.$set(e.value,"description",t)},expression:"value.description"}})],1),e._v(" "),a("div"),e._v(" "),a("el-form-item",{staticStyle:{width:"460px"},attrs:{label:"人物图片：",prop:"pic"}},[a("single-upload",{staticClass:"pic-box",model:{value:e.value.pic,callback:function(t){e.$set(e.value,"pic",t)},expression:"value.pic"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否去除水印：",prop:"description2"}},[a("el-radio-group",{model:{value:e.value.description2,callback:function(t){e.$set(e.value,"description2",t)},expression:"value.description2"}},[a("el-radio",{attrs:{label:1}},[e._v("去除")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("不去除")])],1)],1),e._v(" "),a("div"),e._v(" "),a("el-form-item",{staticClass:"pics-box",attrs:{label:"图片详情："}},[a("span",[e._v("是否去除水印： ")]),e._v(" "),a("el-radio-group",{model:{value:e.value.description3,callback:function(t){e.$set(e.value,"description3",t)},expression:"value.description3"}},[a("el-radio",{attrs:{label:1}},[e._v("去除")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("不去除")])],1),e._v(" "),a("multi-upload",{ref:"muupload",model:{value:e.selectProductPics,callback:function(t){e.selectProductPics=t},expression:"selectProductPics"}})],1)],1),e._v(" "),e.extList.ext2.needShow?a("el-card",{staticStyle:{"margin-bottom":"20px"}},[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext2.detailOptions,"opetion-date":e.extList.ext2.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e(),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("\n          保障信息\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"联系手机：",prop:"gameCareinfoPhone"}},[a("el-input",{model:{value:e.value.gameCareinfoPhone,callback:function(t){e.$set(e.value,"gameCareinfoPhone",t)},expression:"value.gameCareinfoPhone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系时间：",prop:"gameCareinfoTime"}},[a("el-input",{model:{value:e.value.gameCareinfoTime,callback:function(t){e.$set(e.value,"gameCareinfoTime",t)},expression:"value.gameCareinfoTime"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系微信：",prop:"gameCareinfoVx"}},[a("el-input",{model:{value:e.value.gameCareinfoVx,callback:function(t){e.$set(e.value,"gameCareinfoVx",t)},expression:"value.gameCareinfoVx"}})],1)],1),e._v(" "),e.extList.ext3.needShow?a("el-card",[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext3.detailOptions,"opetion-date":e.extList.ext3.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e(),e._v(" "),e.extList.ext4.needShow?a("el-card",[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext4.detailOptions,"opetion-date":e.extList.ext4.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e()],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.tabValue,expression:"tabValue === '1'"}]},[a("el-form-item",{attrs:{label:"首页推荐：",prop:"recommandStatus"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.value.recommandStatus,callback:function(t){e.$set(e.value,"recommandStatus",t)},expression:"value.recommandStatus"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"关联专区："}},[a("el-transfer",{staticStyle:{display:"inline-block"},attrs:{"filter-method":e.filterMethod,titles:e.subjectTitles,data:e.subjectList,filterable:"","filter-placeholder":"请输入专题名称"},model:{value:e.selectSubject,callback:function(t){e.selectSubject=t},expression:"selectSubject"}})],1)],1)]),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("productForm")}}},[e._v("确 定")])],1)],1)},staticRenderFns:[]};var D={keyword:null,pageNum:1,pageSize:20,publishStatus:null,verifyStatus:null,productSn:null,productCategoryId:null,brandId:null},N={name:"ProductList",components:{ProductAdd:a("VU/8")(V,j,!1,function(e){a("P8ef")},"data-v-7bd79ede",null).exports},filters:{verifyStatusFilter:function(e){return 1===e?"审核通过":2===e?"未通过":"未审核"}},data:function(){return{verifyValue:{mark:"",ids:[]},queryId:"",showVerifyDetailModal:!1,showAddModel:!1,editSkuInfo:{dialogVisible:!1,productId:null,productSn:"",productAttributeCategoryId:null,stockList:[],productAttr:[],keyword:null},operates:[{label:"商品上架",value:"publishOn"},{label:"商品下架",value:"publishOff"},{label:"设为推荐",value:"recommendOn"},{label:"取消推荐",value:"recommendOff"},{label:"设为新品",value:"newOn"},{label:"取消新品",value:"newOff"},{label:"转移到分类",value:"transferCategory"},{label:"移入回收站",value:"recycle"}],operateType:null,listQuery:i()({},D),list:null,total:null,listLoading:!0,selectProductCateValue:null,multipleSelection:[],productCateOptions:[],brandOptions:[],publishStatusOptions:[{value:1,label:"上架"},{value:0,label:"下架"}],verifyStatusOptions:[{value:1,label:"审核通过"},{value:0,label:"未审核"}]}},watch:{selectProductCateValue:function(e){null!=e&&2==e.length?this.listQuery.productCategoryId=e[1]:this.listQuery.productCategoryId=null}},created:function(){this.getList(),this.getBrandList(),this.getProductCateList()},methods:{getState:function(e){return 1===e?"success":2===e?"error":void 0},cancelVerify:function(){this.showVerifyDetailModal=!1},submitVerify:function(e){var t=this;Object(r.e)({detail:this.verifyValue.mark,ids:this.verifyValue.ids,verifyStatus:e}).then(function(e){t.showVerifyDetailModal=!1,t.getList()})},formatTime:function(e){return e=new Date(e),s()(e).format("YYYY-MM-DD HH:mm:ss")},handleAddSuc:function(){this.showAddModel=!1,this.getList()},handleAdd:function(){this.queryId="",this.showAddModel=!0},getProductSkuSp:function(e,t){var a=JSON.parse(e.spData);return null!=a&&t<a.length?a[t].value:null},getList:function(){var e=this;this.listLoading=!0,Object(r.f)(this.listQuery).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})},getBrandList:function(){var e=this;Object(c.c)({pageNum:1,pageSize:200}).then(function(t){e.brandOptions=[];for(var a=t.data.list,l=0;l<a.length;l++)e.brandOptions.push({label:a[l].name,value:a[l].id})})},getProductCateList:function(){var e=this;Object(d.e)().then(function(t){var a=t.data;e.productCateOptions=[];for(var l=0;l<a.length;l++){var i=[];if(null!=a[l].children&&a[l].children.length>0)for(var n=0;n<a[l].children.length;n++)i.push({label:a[l].children[n].name,value:a[l].children[n].id});e.productCateOptions.push({label:a[l].name,value:a[l].id,children:i})}})},handleShowSkuEditDialog:function(e,t){var a=this;this.editSkuInfo.dialogVisible=!0,this.editSkuInfo.productId=t.id,this.editSkuInfo.productSn=t.productSn,this.editSkuInfo.productAttributeCategoryId=t.productAttributeCategoryId,this.editSkuInfo.keyword=null,Object(o.a)(t.id,{keyword:this.editSkuInfo.keyword}).then(function(e){a.editSkuInfo.stockList=e.data}),null!=t.productAttributeCategoryId&&Object(u.c)(t.productAttributeCategoryId,{type:0}).then(function(e){a.editSkuInfo.productAttr=e.data.list})},handleSearchEditSku:function(){var e=this;Object(o.a)(this.editSkuInfo.productId,{keyword:this.editSkuInfo.keyword}).then(function(t){e.editSkuInfo.stockList=t.data})},handleEditSkuConfirm:function(){var e=this;null==this.editSkuInfo.stockList||this.editSkuInfo.stockList.length<=0?this.$message({message:"暂无sku信息",type:"warning",duration:1e3}):this.$confirm("是否要进行修改","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o.b)(e.editSkuInfo.productId,e.editSkuInfo.stockList).then(function(t){e.$message({message:"修改成功",type:"success",duration:1e3}),e.editSkuInfo.dialogVisible=!1})})},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleAddProduct:function(){this.$router.push({path:"/pms/addProduct"})},handleBatchOperate:function(){var e=this;null!=this.operateType?null==this.multipleSelection||this.multipleSelection.length<1?this.$message({message:"请选择要操作的商品",type:"warning",duration:1e3}):this.$confirm("是否要进行该批量操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){for(var t=[],a=0;a<e.multipleSelection.length;a++)t.push(e.multipleSelection[a].id);switch(e.operateType){case e.operates[0].value:e.updatePublishStatus(1,t);break;case e.operates[1].value:e.updatePublishStatus(0,t);break;case e.operates[2].value:e.updateRecommendStatus(1,t);break;case e.operates[3].value:e.updateRecommendStatus(0,t);break;case e.operates[4].value:e.updateNewStatus(1,t);break;case e.operates[5].value:e.updateNewStatus(0,t);break;case e.operates[6].value:break;case e.operates[7].value:e.updateDeleteStatus(1,t)}e.getList()}):this.$message({message:"请选择操作类型",type:"warning",duration:1e3})},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},handleSelectionChange:function(e){this.multipleSelection=e},handlePublishStatusChange:function(e,t){var a=[];a.push(t.id),this.updatePublishStatus(t.publishStatus,a)},handleNewStatusChange:function(e,t){var a=[];a.push(t.id),this.updateNewStatus(t.newStatus,a)},handleRecommendStatusChange:function(e,t){var a=[];a.push(t.id),this.updateRecommendStatus(t.recommandStatus,a)},handleResetSearch:function(){this.selectProductCateValue=[],this.listQuery=i()({},D)},handleDelete:function(e,t){var a=this;this.$confirm("是否要进行删除操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=[];e.push(t.id),a.updateDeleteStatus(1,e)})},handleUpdateProduct:function(e,t){this.showAddModel=!0,this.queryId=t.id},handleUpdateProduct2:function(e,t){this.$router.push({path:"/pms/updateProduct",query:{id:t.id}})},handleVerifyDetail:function(e,t){this.verifyValue.ids=[],this.verifyValue.ids.push(t.id),this.verifyValue.mark="",this.showVerifyDetailModal=!0},handleShowProduct:function(e,t){console.log("handleShowProduct",t)},handleShowVerifyDetail:function(e,t){console.log("handleShowVerifyDetail",t)},handleShowLog:function(e,t){console.log("handleShowLog",t)},updatePublishStatus:function(e,t){var a=this,l=new URLSearchParams;l.append("ids",t),l.append("publishStatus",e),Object(r.D)(l).then(function(e){a.$message({message:"修改成功",type:"success",duration:1e3})})},updateNewStatus:function(e,t){var a=this,l=new URLSearchParams;l.append("ids",t),l.append("newStatus",e),Object(r.B)(l).then(function(e){a.$message({message:"修改成功",type:"success",duration:1e3})})},updateRecommendStatus:function(e,t){var a=this,l=new URLSearchParams;l.append("ids",t),l.append("recommendStatus",e),Object(r.E)(l).then(function(e){a.$message({message:"修改成功",type:"success",duration:1e3})})},updateDeleteStatus:function(e,t){var a=this,l=new URLSearchParams;l.append("ids",t),l.append("deleteStatus",e),Object(r.A)(l).then(function(e){a.$message({message:"删除成功",type:"success",duration:1e3})}),this.getList()}}},T={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"输入搜索："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品名称"},model:{value:e.listQuery.keyword,callback:function(t){e.$set(e.listQuery,"keyword",t)},expression:"listQuery.keyword"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品编号"},model:{value:e.listQuery.productSn,callback:function(t){e.$set(e.listQuery,"productSn",t)},expression:"listQuery.productSn"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品分类："}},[a("el-cascader",{attrs:{options:e.productCateOptions,clearable:""},model:{value:e.selectProductCateValue,callback:function(t){e.selectProductCateValue=t},expression:"selectProductCateValue"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"游戏品牌："}},[a("el-select",{attrs:{placeholder:"请选择品牌",clearable:""},model:{value:e.listQuery.brandId,callback:function(t){e.$set(e.listQuery,"brandId",t)},expression:"listQuery.brandId"}},e._l(e.brandOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"上架状态："}},[a("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.publishStatus,callback:function(t){e.$set(e.listQuery,"publishStatus",t)},expression:"listQuery.publishStatus"}},e._l(e.publishStatusOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"审核状态："}},[a("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.verifyStatus,callback:function(t){e.$set(e.listQuery,"verifyStatus",t)},expression:"listQuery.verifyStatus"}},e._l(e.verifyStatusOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询结果\n          ")]),e._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")]),e._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleAdd()}}},[e._v("\n            新建商品\n          ")])],1)],1)],1)]),e._v(" "),a("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "),a("span",[e._v("数据列表")]),e._v(" "),a("el-button",{staticClass:"btn-add",attrs:{size:"mini"},on:{click:function(t){return e.handleAddProduct()}}},[e._v("\n      添加\n    ")])],1),e._v(" "),a("div",{staticClass:"batch-operate-container"},[a("el-select",{attrs:{size:"small",placeholder:"批量操作"},model:{value:e.operateType,callback:function(t){e.operateType=t},expression:"operateType"}},e._l(e.operates,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),e._v(" "),a("el-button",{staticClass:"search-button",staticStyle:{"margin-left":"20px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleBatchOperate()}}},[e._v("\n      确定\n    ")])],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":e.listQuery.pageNum,total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"60",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"商品编号",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.productSn))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"价格",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v("价格：￥"+e._s(t.row.price))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"区服",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.gameAccountQufu))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"标签",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v("\n            上架：\n            "),a("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(a){return e.handlePublishStatusChange(t.$index,t.row)}},model:{value:t.row.publishStatus,callback:function(a){e.$set(t.row,"publishStatus",a)},expression:"scope.row.publishStatus"}})],1),e._v(" "),a("p",[e._v("\n            新品：\n            "),a("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(a){return e.handleNewStatusChange(t.$index,t.row)}},model:{value:t.row.newStatus,callback:function(a){e.$set(t.row,"newStatus",a)},expression:"scope.row.newStatus"}})],1),e._v(" "),a("p",[e._v("\n            推荐：\n            "),a("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(a){return e.handleRecommendStatusChange(t.$index,t.row)}},model:{value:t.row.recommandStatus,callback:function(a){e.$set(t.row,"recommandStatus",a)},expression:"scope.row.recommandStatus"}})],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatTime(t.row.updateTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"提交时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatTime(t.row.createTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"审核状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",{class:e.getState(t.row.verifyStatus)},[e._v("\n            "+e._s(e._f("verifyStatusFilter")(t.row.verifyStatus))+"\n          ")]),e._v(" "),a("p",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleShowVerifyDetail(t.$index,t.row)}}},[e._v("审核详情\n            ")])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleVerifyDetail(t.$index,t.row)}}},[e._v("审核\n            ")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleShowProduct(t.$index,t.row)}}},[e._v("查看\n            ")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleUpdateProduct(t.$index,t.row)}}},[e._v("编辑\n            ")])],1),e._v(" "),a("p",[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleShowLog(t.$index,t.row)}}},[e._v("日志\n            ")]),e._v(" "),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.$index,t.row)}}},[e._v("删除\n            ")])],1)]}}])})],1)],1),e._v(" "),a("div",{staticClass:"batch-operate-container"},[a("el-select",{attrs:{size:"small",placeholder:"批量操作"},model:{value:e.operateType,callback:function(t){e.operateType=t},expression:"operateType"}},e._l(e.operates,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),e._v(" "),a("el-button",{staticClass:"search-button",staticStyle:{"margin-left":"20px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleBatchOperate()}}},[e._v("\n      确定\n    ")])],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":e.listQuery.pageNum,total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{visible:e.editSkuInfo.dialogVisible,title:"编辑货品信息",width:"40%"},on:{"update:visible":function(t){return e.$set(e.editSkuInfo,"dialogVisible",t)}}},[a("span",[e._v("商品编号：")]),e._v(" "),a("span",[e._v(e._s(e.editSkuInfo.productSn))]),e._v(" "),a("el-input",{staticStyle:{width:"50%","margin-left":"20px"},attrs:{placeholder:"按sku编号搜索",size:"small"},model:{value:e.editSkuInfo.keyword,callback:function(t){e.$set(e.editSkuInfo,"keyword",t)},expression:"editSkuInfo.keyword"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.handleSearchEditSku},slot:"append"})],1),e._v(" "),a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.editSkuInfo.stockList,border:""}},[a("el-table-column",{attrs:{label:"SKU编号",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.skuCode,callback:function(a){e.$set(t.row,"skuCode",a)},expression:"scope.row.skuCode"}})]}}])}),e._v(" "),e._l(e.editSkuInfo.productAttr,function(t,l){return a("el-table-column",{key:t.id,attrs:{label:t.name,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e.getProductSkuSp(t.row,l))+"\n        ")]}}],null,!0)})}),e._v(" "),a("el-table-column",{attrs:{label:"销售价格",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.price,callback:function(a){e.$set(t.row,"price",a)},expression:"scope.row.price"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"商品库存",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.stock,callback:function(a){e.$set(t.row,"stock",a)},expression:"scope.row.stock"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"库存预警值",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.lowStock,callback:function(a){e.$set(t.row,"lowStock",a)},expression:"scope.row.lowStock"}})]}}])})],2),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.editSkuInfo.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.handleEditSkuConfirm}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{visible:e.showVerifyDetailModal,title:"审核",width:"50%",top:"1vh"},on:{"update:visible":function(t){e.showVerifyDetailModal=t}}},[a("el-form",{ref:"verifyForm",staticStyle:{width:"80%"},attrs:{model:e.verifyValue}},[a("el-form-item",{attrs:{label:"审核备注：",prop:"mark"}},[a("el-input",{model:{value:e.verifyValue.mark,callback:function(t){e.$set(e.verifyValue,"mark",t)},expression:"verifyValue.mark"}})],1)],1),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:e.cancelVerify}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitVerify(2)}}},[e._v("拒 绝")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitVerify(1)}}},[e._v("通 过")])],1)],1),e._v(" "),a("el-dialog",{attrs:{visible:e.showAddModel,title:"添加商品",width:"80%",top:"1vh"},on:{"update:visible":function(t){e.showAddModel=t}}},[e.showAddModel?a("ProductAdd",{attrs:{"query-id":e.queryId},on:{addsuc:e.handleAddSuc}}):e._e()],1)],1)},staticRenderFns:[]};var Q=a("VU/8")(N,T,!1,function(e){a("AOVd")},"data-v-54c2c93e",null);t.default=Q.exports}});