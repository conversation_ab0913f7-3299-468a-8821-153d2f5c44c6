webpackJsonp([37],{"4Egj":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("RRo+"),s=a.n(i),l=a("woOf"),o=a.n(l),r=a("so1O"),n=a("Lfj9"),u=a("0xDb"),c=a("SvC/"),d={components:{MyTable:r.a,ProductAdd:c.a},data:function(){return{shopStatusId:"",priceForm:{price:"",originPrice:"",id:""},shopStatusRadio:"",cateParentId:74,productCategoryId:null,queryId:"",activeName:"",showAddModel:!1,util:u.b,priceFormDialog:!1,getShopStateType:{0:"仅上架看看",1:"看看、店铺都上架",2:"仅上架店铺"},updateShopVisible:!1,shopShowStatusType:[{label:"仅上架看看",value:0},{label:"看看、店铺都上架",value:1},{label:"仅上架店铺",value:2}],options:[{value:"",label:"查看全部"},{value:"ON_SHELF",label:"在售"},{value:"BOOKED",label:"已预定"},{value:"OFF_SHELF",label:"已下架"},{value:"TO_REVIEW",label:"待审核"}],searchData:{},defaultListQuery:{productSn:null,productStatus:""},operationList:[{name:"商品编号",width:"150",value:"productSn"},{name:"分类名称",width:"150",value:"productCategoryName"},{name:"商品图",value:"name",slotName:"goodsPic",width:"120"},{name:"单价",width:"100",value:"price"},{name:"底价",width:"100",value:"originalPrice"},{name:"商品状态",width:"120",value:"status",slotName:"status"},{name:"店铺显示状态",width:"150",value:"shopShowStatus",slotName:"shopShowStatus"},{name:"商品标题",value:"detailTitle",slotName:"detailTitle"},{name:"发布时间",value:"createTime",width:"150",slotName:"createTime"}]}},mounted:function(){},methods:{shopStatusRadioSubmit:function(){var e=this;console.log(this.shopStatusRadio,11111),""!=this.shopStatusRadio||0==this.shopStatusRadio?Object(n._30)(this.shopStatusId,{shopShowStatus:this.shopStatusRadio}).then(function(t){200==t.code&&(e.$message.success(t.data),e.updateShopVisible=!1,e.searchData=o()({},e.searchData))}):this.$message.error("请选择要变更的状态")},updateShopStatus:function(e){this.shopStatusId=e.id,this.shopStatusRadio=e.shopShowStatus,this.updateShopVisible=!0},validatePositiveInteger:function(e,t,a){if(!t)return a(new Error("请输入账号价格"));var i=Number(t);s()(i)&&i>0?a():a(new Error("请输入一个有效的正整数"))},handleSearchList:function(){this.searchData=o()({},this.defaultListQuery,{pageNum:1,pageSize:20})},handleClick:function(e){this.defaultListQuery.productStatus=e.name;this.searchData=o()({},this.defaultListQuery,{pageNum:1,pageSize:20})},handleResetSearch:function(){this.defaultListQuery={productStatus:"0"!=this.activeName?this.activeName:"",productSn:null},this.searchData={pageNum:1,pageSize:20,productStatus:"0"!=this.activeName?this.activeName:""}},canRefoundBack:function(e){return-1==e.status||0==e.status||2==e.status||5==e.status},canEdit:function(e){return 0==e.deleteStatus&&9==e.stock&&-2!=e.publishStatus},canDown:function(e){return 0==e.deleteStatus&&1==e.publishStatus&&9==e.stock},canUp:function(e){return!1},uploadIng:function(e){return 1==e.pushType&&1==e.pushStatus},canDel:function(e){return 0==e.deleteStatus&&9==e.stock},canCancel:function(e){return 1==e.status||2==e.status&&e.sellerOfferPrice>0},getState:function(e){return 2===e.deleteStatus?"已删除":1===e.deleteStatus?"管理员删除":0===e.stock?"已出售":1===e.stock?"已预订":1===e.publishStatus?"在售":-2===e.publishStatus?"已下架":0===e.verifyStatus?"待审核":2===e.verifyStatus?"审核失败":void 0},getProductList:n.E,deleteGoods:function(e){var t=this;this.$confirm("您确定要删除吗？删除后不可恢复?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(n.j)(e.id).then(function(e){200==e.code&&(t.$message.success("删除成功"),t.searchData=o()({},t.searchData))})})},editGoods:function(e){this.productCategoryId=e.productCategoryId,this.showAddModel=!0,this.queryId=e.id},handleAddSuc:function(){this.showAddModel=!1,this.searchData=o()({},this.searchData)},delePushFun:function(e){var t=this;this.$confirm("您确定下架当前商品？下架后不可恢复","提示",{closeOnClickModal:!1,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.listLoading=!0,Object(n._7)({id:e.id}).then(function(e){t.listLoading=!1,200==e.code&&(t.$message.success("下架成功"),t.searchData=o()({},t.searchData))})})},priceFormSubmit:function(){var e=this;this.$refs.priceForm.validate(function(t){if(t){var a=e.priceForm.price,i=e.priceForm.originPrice;console.log(a,i,e.priceForm.id),Object(n._28)(e.priceForm.id,{newPrice:a,newOriginPrice:i}).then(function(t){200==t.code&&(e.$message.success("修改成功"),e.priceFormDialog=!1,e.searchData=o()({},e.searchData),e.$refs.priceForm.resetFields())})}})},updatePrice:function(e){this.priceForm.id=e.id,this.priceFormDialog=!0},handleClose:function(){this.priceFormDialog=!1},editAccUpload:function(e){var t=this;this.$confirm("您确定上架当前商品？","提示",{closeOnClickModal:!1,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.listLoading=!0,Object(n._9)({id:e.id}).then(function(e){t.listLoading=!1,200==e.code&&(t.$message.success("上架成功"),t.searchData=o()({},t.searchData))})})}}},p={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"20px","margin-bottom":"100px"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"查看全部",name:""}}),e._v(" "),a("el-tab-pane",{attrs:{label:"在售",name:"ON_SHELF"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"已预定",name:"BOOKED"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"已下架",name:"OFF_SHELF"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"待审核",name:"TO_REVIEW"}})],1),e._v(" "),a("el-form",{attrs:{inline:!0,model:e.defaultListQuery,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入商品编号",clearable:""},model:{value:e.defaultListQuery.productSn,callback:function(t){e.$set(e.defaultListQuery,"productSn",t)},expression:"defaultListQuery.productSn"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n          重置\n        ")]),e._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n          查询搜索\n        ")])],1)],1)],1),e._v(" "),a("MyTable",{ref:"childRef",attrs:{isShopBtn:!0,listApi:e.getProductList,operationList:e.operationList,searchObj:e.searchData},scopedSlots:e._u([{key:"goodsPic",fn:function(e){var t=e.row;return[a("img",{staticStyle:{width:"100px"},attrs:{src:t.pic,alt:""}})]}},{key:"status",fn:function(t){var i=t.row;return[a("span",[e._v("\n        "+e._s(e.getState(i))+"\n        "),"审核失败"==e.getState(i)?a("el-popover",{attrs:{placement:"top-start",trigger:"hover",content:i.verifyDetail}},[a("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})]):e._e()],1)]}},{key:"shopShowStatus",fn:function(t){var i=t.row;return[a("span",[e._v("\n        "+e._s(e.getShopStateType[i.shopShowStatus])+"\n      ")])]}},{key:"detailTitle",fn:function(t){var i=t.row;return[a("div",{staticClass:"text_linTwo",attrs:{title:i.detailTitle}},[e._v("\n        "+e._s(i.detailTitle)+"\n      ")])]}},{key:"createTime",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(e.util.timeFormatDD(i.createTime)))])]}},{key:"shopBtns",fn:function(t){var i=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.updateShopStatus(i)}}},[e._v("上架渠道")])]}},{key:"btns",fn:function(t){var i=t.row;return[a("div",[e.canDel(i)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteGoods(i)}}},[e._v("删除商品")]):e._e(),e._v(" "),e.canEdit(i)&&!e.uploadIng(i)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.updatePrice(i)}}},[e._v("改价")]):e._e(),e._v(" "),e.canDown(i)&&!e.uploadIng(i)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.delePushFun(i)}}},[e._v("下架")]):e._e(),e._v(" "),e.canUp(i)&&!e.uploadIng(i)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editAccUpload(i)}}},[e._v("上架")]):e._e(),e._v(" "),1!==i.pushType&&(["OFF_SHELF"].includes(i.productStatus)||"ON_SHELF"===i.productStatus&&3===i.gameGoodsSaletype)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editGoods(i)}}},[e._v("编辑")]):e._e()],1)]}}])}),e._v(" "),a("el-dialog",{attrs:{title:"改价",visible:e.priceFormDialog,width:"600px","before-close":e.handleClose},on:{"update:visible":function(t){e.priceFormDialog=t}}},[a("div",[a("div",{staticStyle:{"text-align":"center","margin-bottom":"20px"}},[e._v("\n        改价比例不能超过50%，如超过50%将重新审核\n      ")]),e._v(" "),a("el-form",{ref:"priceForm",staticClass:"demo-ruleForm",attrs:{model:e.priceForm,"label-width":"100px"}},[a("el-form-item",{attrs:{rules:[{required:!0,message:"请输入账号价格",trigger:"blur"},{validator:e.validatePositiveInteger,trigger:"blur"}],label:"账号价格",prop:"price"}},[a("el-input",{staticClass:"editNum",attrs:{type:"number",placeholder:"请输入账号价格",clearable:""},model:{value:e.priceForm.price,callback:function(t){e.$set(e.priceForm,"price",t)},expression:"priceForm.price"}})],1),e._v(" "),a("el-form-item",{attrs:{rules:[{required:!0,message:"请输入心理底价",trigger:"blur"},{validator:e.validatePositiveInteger,trigger:"blur"}],label:"心理底价",prop:"originPrice"}},[a("el-input",{staticClass:"editNum",attrs:{type:"number",placeholder:"请输入心理底价",clearable:""},model:{value:e.priceForm.originPrice,callback:function(t){e.$set(e.priceForm,"originPrice",t)},expression:"priceForm.originPrice"}})],1)],1),e._v(" "),a("div",{staticStyle:{"text-align":"center","font-size":"13px"}},[e._v("\n        当有买家咨询最低价时系统将自动发送此价格，请谨慎填写。\n      ")])],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.priceFormDialog=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.priceFormSubmit}},[e._v("确 定")])],1)]),e._v(" "),a("el-dialog",{attrs:{visible:e.showAddModel,title:"编辑",width:"80%",top:"1vh"},on:{"update:visible":function(t){e.showAddModel=t}}},[e.showAddModel?a("ProductAdd",{attrs:{"query-id":e.queryId,"cate-parent-id":e.cateParentId,"product-category-id":e.productCategoryId},on:{addsuc:e.handleAddSuc}}):e._e()],1),e._v(" "),a("el-dialog",{staticClass:"dialogSm",attrs:{"close-on-click-modal":!1,"show-close":!1,visible:e.updateShopVisible,width:"500px",title:"上架渠道"},on:{"update:visible":function(t){e.updateShopVisible=t}}},[a("div",{staticClass:"codeBgbox"},[a("el-radio-group",{model:{value:e.shopStatusRadio,callback:function(t){e.shopStatusRadio=t},expression:"shopStatusRadio"}},e._l(e.shopShowStatusType,function(t,i){return a("el-radio",{attrs:{label:t.value}},[e._v(e._s(t.label))])}),1)],1),e._v(" "),a("div",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.updateShopVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.shopStatusRadioSubmit}},[e._v("确定")])],1)])],1)},staticRenderFns:[]},v=a("VU/8")(d,p,!1,null,null,null);t.default=v.exports},"SvC/":function(e,t,a){"use strict";var i=a("fZjL"),s=a.n(i),l=a("pFYg"),o=a.n(l),r=a("mvHQ"),n=a.n(r),u=a("woOf"),c=a.n(u),d=a("M4fF"),p=a.n(d),v=a("mRsl"),h=a("s/Rn"),m=a("KhLR"),f=a("3idm"),b=a("ocgh"),g=(a("n97X"),a("UgCr")),_=a("TZVV"),y=a("sl7S"),S=a("5aCZ"),x=a("II7+"),C=a("0xDb"),P={1:"面板属性",2:"打造内功",3:"天赏外观",4:"普通外观",5:"其他物品"},w={albumPics:"",albumPicsJson:"[]",productAttributeValueList:[],brandId:"",productAttributeCategoryId:"",gameAccountQufu:"",description:"",qcsy2:0,pic:"",qcsy3:0,selectProductPics:[],price:"",originalPrice:"",stock:9,gameGoodsFangxin:0,gameGoodsBukuan:0,gameGoodsJiangjia:0,gameGoodsYijia:0,sort:"",publishStatus:0,gameGoodsYishou:0,gameGoodsYuyue:0,gameGoodsSaletype:1,gameCareinfoPhone:"",gameCareinfoTime:"",gameCareinfoVx:"",subjectProductRelationList:[],oldSubTitle:""},L={name:"ProductAdd",components:{SingleUpload:_.a,MultiUpload:y.a,Tinymce:S.a,tedian:x.a},props:{queryId:{type:[Number,String],default:""},cateParentId:{type:[Number,String],default:""},productCategoryId:{type:[Number,String],default:""}},data:function(){return{typeOptions:[],hasImgType:!1,tabValue:"0",active:0,value:c()({},w),hasEditCreated:!1,selectProductCateValue:[],productCateOptions:[],brandOptions:[],productAttributeCategoryOptions:[],selectProductParam:[],addProductAttrValue:"",activeHtmlName:"pc",rules:{},formLabelWidth:"200px",spanCol:12,extList:{ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]},ext5:{needShow:!1,opetionDate:[],detailOptions:[]},ext6:{needShow:!1,opetionDate:[],detailOptions:[]}},subjectTitles:["待选择","已选择"],memberId:"",memberInfo:{}}},computed:{isEdit:function(){return""!==this.queryId},productId:function(){return this.value.id},selectServiceList:{get:function(){var e=[];if(void 0===this.value.serviceIds||null==this.value.serviceIds||""===this.value.serviceIds)return e;for(var t=this.value.serviceIds.split(","),a=0;a<t.length;a++)e.push(Number(t[a]));return e},set:function(e){var t="";if(null!=e&&e.length>0){for(var a=0;a<e.length;a++)t+=e[a]+",";t.endsWith(",")&&(t=t.substr(0,t.length-1)),this.value.serviceIds=t}else this.value.serviceIds=null}},selectProductPics:{get:function(){var e=[];if(void 0===this.value.albumPics||null==this.value.albumPics||""===this.value.albumPics)return e;var t=this.value.albumPics.split(","),a=this.value.albumPicsJson||"[]";a=JSON.parse(a);for(var i=0;i<t.length;i++){var s="";if(a[i])s=a[i].name||"";this.hasImgType?e.push({url:t[i],name:s}):e.push(t[i])}return e},set:function(e){if(null==e||0===e.length)this.value.albumPics="",this.value.albumPicsJson=n()([]);else{var t="",a=[];if(e.length>0){for(var i=0;i<e.length;i++)this.hasImgType?(t+=e[i].url,a.push({url:e[i].url,name:e[i].name})):t+=e[i],i!==e.length-1&&(t+=",");this.value.albumPics=t,this.value.albumPicsJson=n()(a)}}}},selectSubject:{get:function(){var e=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return e;for(var t=0;t<this.value.subjectProductRelationList.length;t++)e.push(this.value.subjectProductRelationList[t].subjectId);return e},set:function(e){this.value.subjectProductRelationList=[];for(var t=0;t<e.length;t++)this.value.subjectProductRelationList.push({subjectId:e[t]})}}},watch:{selectProductCateValue:function(e){null!=e&&2===e.length?(this.value.productCategoryId=e[1],this.value.productCategoryName=this.getCateNameById(this.value.productCategoryId)):(this.value.productCategoryId=null,this.value.productCategoryName=null)},productId:function(e){this.isEdit&&(this.hasEditCreated||void 0!==e&&null!=e&&0!==e&&(this.handleEditCreatedInfo(),this.handleEditCreatedAttr()))}},created:function(){this.getProductCate(),this.getProductCateList(),this.getBrandList(),this.getProductAttrCateList()},methods:{getProductCate:function(){var e=this;Object(v.f)(this.productCategoryId).then(function(t){if(200==t.code){var a=t.data;if(a.custom){var i=JSON.parse(a.custom);i.albumPicsTypeOptions&&i.albumPicsTypeOptions.length&&(e.typeOptions=i.albumPicsTypeOptions,e.typeOptions=e.typeOptions.filter(function(e){return"全部图片"!=e.name}),e.hasImgType=!0)}e.isEdit&&Object(g.j)(e.queryId).then(function(t){e.value=c()({},e.value,t.data),e.oldSubTitle=e.value.subTitle,e.hasImgType&&e.transFormAlbumPicsJson(),e.memberId=e.value.memberId||"",e.saveSkuStockList=p.a.cloneDeep(e.value.skuStockList)})}})},transFormAlbumPicsJson:function(){var e=this.value.albumPicsJson||"[]",t=[];(e=JSON.parse(e)).forEach(function(e){e.hasOwnProperty("name")?t.push(e):(e.name=P[e.type]||e.type||"",t.push(e))}),this.value.albumPicsJson=n()(t)},getGender:function(e){return 1==e?"男":"女"},getConfirm:function(e){return 1===e?"是":"否"},changequfu:function(e){this.value.gameAccountQufu=e},changeTab:function(e,t){"1"===e.index&&this.getMember()},getMember:function(){var e=this;this.memberId&&Object(b.a)(this.memberId).then(function(t){e.memberInfo=t.data})},handleEditCreatedInfo:function(){null!=this.value.productCategoryId&&(this.selectProductCateValue=[],this.selectProductCateValue.push(this.value.cateParentId),this.selectProductCateValue.push(this.value.productCategoryId)),this.hasEditCreated=!0},getProductCateList:function(){var e=this;Object(v.e)().then(function(t){var a=t.data;e.productCateOptions=[];for(var i=0;i<a.length;i++){var s=[];if(null!=a[i].children&&a[i].children.length>0)for(var l=0;l<a[i].children.length;l++)s.push({label:a[i].children[l].name,value:a[i].children[l].id});e.productCateOptions.push({label:a[i].name,value:a[i].id,children:s})}e.cateParentId&&e.productCategoryId&&(e.selectProductCateValue=[],e.selectProductCateValue.push(parseInt(e.cateParentId,10)),e.selectProductCateValue.push(parseInt(e.productCategoryId,10)))})},getBrandList:function(){var e=this;Object(h.c)({pageNum:1,pageSize:200}).then(function(t){e.brandOptions=[];for(var a=t.data.list,i=0;i<a.length;i++)e.brandOptions.push({label:a[i].name,value:a[i].id})})},getCateNameById:function(e){for(var t=null,a=0;a<this.productCateOptions.length;a++)for(var i=0;i<this.productCateOptions[a].children.length;i++)if(this.productCateOptions[a].children[i].value===e)return t=this.productCateOptions[a].children[i].label;return t},handleBrandChange:function(e){for(var t="",a=0;a<this.brandOptions.length;a++)if(this.brandOptions[a].value===e){t=this.brandOptions[a].label;break}this.value.brandName=t},handleEditCreated:function(){var e=this.value.serviceIds.split(",");console.log("handleEditCreated",e);for(var t=0;t<e.length;t++)this.selectServiceList.push(Number(e[t]))},handleRemoveProductLadder:function(e,t){var a=this.value.productLadderList;1===a.length?(a.pop(),a.push({count:0,discount:0,price:0})):a.splice(e,1)},handleAddProductLadder:function(e,t){var a=this.value.productLadderList;a.length<3?a.push({count:0,discount:0,price:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handleRemoveFullReduction:function(e,t){var a=this.value.productFullReductionList;1===a.length?(a.pop(),a.push({fullPrice:0,reducePrice:0})):a.splice(e,1)},handleAddFullReduction:function(e,t){var a=this.value.productFullReductionList;a.length<3?a.push({fullPrice:0,reducePrice:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handleEditCreatedAttr:function(){null!=this.value.productAttributeCategoryId&&this.handleProductAttrChange(this.value.productAttributeCategoryId),this.hasEditCreated=!0},getProductAttrCateList:function(){var e=this;Object(m.c)({pageNum:1,pageSize:999}).then(function(t){e.productAttributeCategoryOptions=[];for(var a=t.data.list,i=0;i<a.length;i++)e.productAttributeCategoryOptions.push({label:a[i].name,value:a[i].id})})},getProductAttrList:function(e,t){var a=this,i={pageNum:1,pageSize:200,type:e};Object(f.c)(t,i).then(function(t){var i=t.data.list;if(0!==e){var s="基础信息扩展";2===e?s="账号信息扩展":3===e&&(s="其他扩展");var l={index:parseInt(e,10),label:s,needShow:i&&i.length>0},o=a.getEditAttrOptions2(i),r=[];l.opetionDate=o;for(var n=0;n<i.length;n++){var u=null;a.isEdit&&(u=a.getEditParamValue2(i[n]))&&r.push(u)}l.detailOptions=r,C.b.async2opetionDate(l.detailOptions,l.opetionDate),a.$set(a.extList,"ext"+e,l)}})},getEditAttrOptions2:function(e){return e.map(function(e){var t=1;if(1===e.inputType&&(1===e.selectType?t=2:2===e.selectType?t=3:3===e.selectType&&(t=4)),1===t)return c()({},e,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:t,iptVal:""});if(2===t)return c()({},e,{tdtype:t,value:"",is_required:0,field_type:2,inputList:e.inputList.split(",")});if(3===t){var a=[];return e.inputList.split(",").forEach(function(e){a.push({icon:"",name:e,checked:!1})}),c()({},e,{childList:a,is_required:0,field_type:2,default_word:"点击可下拉选择物品",tdtype:t,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[]})}var i=JSON.parse(e.inputList);return i.forEach(function(e){e.value=e.parent_name,e.label=e.parent_name;var t=e.childList.map(function(e){return{value:e,label:e}});e.children=t}),c()({},e,{tdtype:t,value:[],is_required:0,field_type:2,options:i})})},getEditParamValue2:function(e){var t=1;1===e.inputType&&(1===e.selectType?t=2:2===e.selectType?t=3:3===e.selectType&&(t=4));for(var a=0;a<this.value.productAttributeValueList.length;a++)if(e.id===this.value.productAttributeValueList[a].productAttributeId){var i=this.value.productAttributeValueList[a];if(1===t)return c()({},e,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:t,iptVal:i.value});if(2===t)return c()({},e,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:t,value:i.value});if(3!==t)return c()({},e,{title:e.name,tdtype:t,value:(i.value||"").split("|"),options:JSON.parse(e.inputList)});var s=function(){var a=[];""!==i.value&&(a=i.value.split(","));var s=[];return a.forEach(function(e){s.push({icon:"",name:e,checked:!0})}),{v:c()({},e,{title:e.name,tdtype:t,value:s})}}();if("object"===(void 0===s?"undefined":o()(s)))return s.v}},handleProductAttrChange:function(e){this.extList={ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]},ext5:{needShow:!1,opetionDate:[],detailOptions:[]},ext6:{needShow:!1,opetionDate:[],detailOptions:[]}},this.getProductAttrList(1,e),this.getProductAttrList(2,e),this.getProductAttrList(3,e),this.getProductAttrList(4,e),this.getProductAttrList(5,e),this.getProductAttrList(6,e)},getParamInputList:function(e){return e.split(",")},submitForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return t.$message({message:"验证失败",type:"error",duration:1e3}),!1;t.finishCommit(t.isEdit)})},filterMethod:function(e,t){return t.label.indexOf(e)>-1},cancel:function(){this.$emit("addsuc")},finishCommit:function(e){var t=this;this.$confirm("是否要提交该产品","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.value.productAttributeValueList=[];for(var a=s()(t.extList),i=0;i<a.length;i++)for(var l=a[i],o=t.extList[l].opetionDate,r=0;r<o.length;r++){var n=o[r],u=n.value||"";3===n.tdtype&&n.choosedList.length?u=n.choosedList.map(function(e){return e.name}).join(","):1===n.tdtype?u=n.iptVal:4===n.tdtype&&(u=n.value.join("|")),t.value.productAttributeValueList.push({productAttributeId:n.id,value:u,attriName:n.name,sort:n.sort,filterType:n.filterType,searchType:n.searchType,type:n.type,searchSort:n.searchSort})}t.hasImgType||(t.value.albumPicsJson="[]"),e?(t.value.subTitle&&(t.value.subTitle=t.value.subTitle.trim()),t.value.subTitle&&t.value.subTitle!==t.oldSubTitle&&"."!=t.value.subTitle[t.value.subTitle.length-1]&&(t.value.subTitle=t.value.subTitle+"."),Object(g.C)(t.queryId,t.value).then(function(e){t.$message({type:"success",message:"提交成功",duration:1e3}),t.$emit("addsuc")})):Object(g.d)(t.value).then(function(e){t.$message({type:"success",message:"提交成功",duration:1e3}),t.$emit("addsuc")})})}}},I={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{on:{"tab-click":e.changeTab},model:{value:e.tabValue,callback:function(t){e.tabValue=t},expression:"tabValue"}},[a("el-tab-pane",{attrs:{label:"基本信息"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"号主信息"}})],1),e._v(" "),a("el-form",{ref:"productForm",staticClass:"form-box",attrs:{model:e.value,rules:e.rules,"label-width":e.formLabelWidth}},[a("div",{directives:[{name:"show",rawName:"v-show",value:"0"===e.tabValue,expression:"tabValue === '0'"}]},[a("el-card",{staticClass:"card-box"},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.value.productSn,expression:"value.productSn"}],attrs:{label:"商品编号：",prop:"productSn"}},[a("el-input",{attrs:{disabled:""},model:{value:e.value.productSn,callback:function(t){e.$set(e.value,"productSn",t)},expression:"value.productSn"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"游戏类型：",prop:"productCategoryId"}},[a("el-cascader",{attrs:{options:e.productCateOptions,disabled:""},model:{value:e.selectProductCateValue,callback:function(t){e.selectProductCateValue=t},expression:"selectProductCateValue"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"游戏品牌：",prop:"brandId"}},[a("el-select",{attrs:{placeholder:"请选择品牌"},on:{change:e.handleBrandChange},model:{value:e.value.brandId,callback:function(t){e.$set(e.value,"brandId",t)},expression:"value.brandId"}},e._l(e.brandOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"区服信息：",prop:"gameAccountQufu"}},[a("el-input",{attrs:{disabled:""},model:{value:e.value.gameAccountQufu,callback:function(t){e.$set(e.value,"gameAccountQufu",t)},expression:"value.gameAccountQufu"}})],1),e._v(" "),e.extList.ext1.needShow?a("div",{staticClass:"ext1",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext1.detailOptions,"opetion-date":e.extList.ext1.opetionDate},on:{changequfu:e.changequfu}})],1):e._e()],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("商品规格")]),e._v(" "),a("el-form-item",{attrs:{label:"售价金额：",prop:"price"}},[a("el-input",{model:{value:e.value.price,callback:function(t){e.$set(e.value,"price",t)},expression:"value.price"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"心理底价：",prop:"originalPrice"}},[a("el-input",{model:{value:e.value.originalPrice,callback:function(t){e.$set(e.value,"originalPrice",t)},expression:"value.originalPrice"}})],1)],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("账号信息")]),e._v(" "),a("el-form-item",{attrs:{label:"权重排序：",prop:"sort"}},[a("el-input",{model:{value:e.value.sort,callback:function(t){e.$set(e.value,"sort",t)},expression:"value.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"访问量：",prop:"gameSysinfoReadcount"}},[a("el-input",{model:{value:e.value.gameSysinfoReadcount,callback:function(t){e.$set(e.value,"gameSysinfoReadcount",t)},expression:"value.gameSysinfoReadcount"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否已售：",prop:"stock"}},[a("el-radio-group",{model:{value:e.value.stock,callback:function(t){e.$set(e.value,"stock",t)},expression:"value.stock"}},[a("el-radio",{attrs:{label:0}},[e._v("已售")]),e._v(" "),a("el-radio",{attrs:{label:9}},[e._v("在售")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("已预订")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否降价：",prop:"gameGoodsJiangjia"}},[a("el-radio-group",{model:{value:e.value.gameGoodsJiangjia,callback:function(t){e.$set(e.value,"gameGoodsJiangjia",t)},expression:"value.gameGoodsJiangjia"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否上架：",prop:"publishStatus"}},[a("el-radio-group",{model:{value:e.value.publishStatus,callback:function(t){e.$set(e.value,"publishStatus",t)},expression:"value.publishStatus"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否一手：",prop:"gameGoodsYishou"}},[a("el-radio-group",{model:{value:e.value.gameGoodsYishou,callback:function(t){e.$set(e.value,"gameGoodsYishou",t)},expression:"value.gameGoodsYishou"}},[a("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),a("div"),e._v(" "),a("el-form-item",{staticStyle:{width:"460px"},attrs:{label:"人物图片：",prop:"pic"}},[a("single-upload",{staticClass:"pic-box",attrs:{"is-delet-water-list":e.value.qcsy2},model:{value:e.value.pic,callback:function(t){e.$set(e.value,"pic",t)},expression:"value.pic"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否去除水印：",prop:"qcsy2"}},[a("el-radio-group",{model:{value:e.value.qcsy2,callback:function(t){e.$set(e.value,"qcsy2",t)},expression:"value.qcsy2"}},[a("el-radio",{attrs:{label:1}},[e._v("去除")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("不去除")])],1)],1),e._v(" "),a("div"),e._v(" "),a("el-form-item",{staticClass:"pics-box",attrs:{label:"图片详情："}},[a("span",[e._v("是否去除水印： ")]),e._v(" "),a("el-radio-group",{model:{value:e.value.qcsy3,callback:function(t){e.$set(e.value,"qcsy3",t)},expression:"value.qcsy3"}},[a("el-radio",{attrs:{label:1}},[e._v("去除")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("不去除")])],1),e._v(" "),a("multi-upload",{ref:"muupload",attrs:{options:e.typeOptions,"is-delet-water-list":e.value.qcsy3,hasImgType:e.hasImgType},model:{value:e.selectProductPics,callback:function(t){e.selectProductPics=t},expression:"selectProductPics"}})],1)],1),e._v(" "),e.extList.ext2.needShow?a("el-card",{staticStyle:{"margin-bottom":"20px"}},[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext2.detailOptions,"opetion-date":e.extList.ext2.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e(),e._v(" "),a("el-card",{staticClass:"card-box"},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"账号描述：",prop:"description"}},[a("el-input",{attrs:{autosize:{minRows:2,maxRows:10},type:"textarea"},model:{value:e.value.description,callback:function(t){e.$set(e.value,"description",t)},expression:"value.description"}})],1)],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("\n          保障信息\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"联系手机：",prop:"gameCareinfoPhone"}},[a("el-input",{model:{value:e.value.gameCareinfoPhone,callback:function(t){e.$set(e.value,"gameCareinfoPhone",t)},expression:"value.gameCareinfoPhone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系时间：",prop:"gameCareinfoTime"}},[a("el-input",{model:{value:e.value.gameCareinfoTime,callback:function(t){e.$set(e.value,"gameCareinfoTime",t)},expression:"value.gameCareinfoTime"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系微信：",prop:"gameCareinfoVx"}},[a("el-input",{model:{value:e.value.gameCareinfoVx,callback:function(t){e.$set(e.value,"gameCareinfoVx",t)},expression:"value.gameCareinfoVx"}})],1)],1),e._v(" "),a("el-card",{staticClass:"card-box"},[a("div",{staticClass:"card-title"},[e._v("\n          商品标题\n        ")]),e._v(" "),a("el-form-item",{staticStyle:{width:"100%"},attrs:{prop:"subTitle"}},[a("el-input",{attrs:{autosize:{minRows:2,maxRows:10},type:"textarea"},model:{value:e.value.subTitle,callback:function(t){e.$set(e.value,"subTitle",t)},expression:"value.subTitle"}})],1)],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.tabValue,expression:"tabValue === '1'"}]},[this.memberId?a("el-card",{staticClass:"card-box"},[a("el-form-item",{attrs:{label:"昵称"}},[e._v("\n          "+e._s(e.memberInfo.nickname)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"性别"}},[e._v("\n          "+e._s(e.getGender(e.memberInfo.gender))+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"手机号"}},[e._v(" "+e._s(e.memberInfo.phone))]),e._v(" "),a("el-form-item",{attrs:{label:"备用手机号"}},[e._v("\n          "+e._s(e.value.gameCareinfoPhone2))]),e._v(" "),a("el-form-item",{attrs:{label:"IM号"}},[e._v(" "+e._s(e.memberInfo.imaccount))]),e._v(" "),a("el-form-item",{attrs:{label:"是否实名认证"}},[e._v("\n          "+e._s(e.getConfirm(e.memberInfo.realNameConfirm)))]),e._v(" "),a("el-form-item",{attrs:{label:"是否实人认证"}},[e._v("\n          "+e._s(e.getConfirm(e.memberInfo.realPersionConfirm)))]),e._v(" "),a("el-form-item",{attrs:{label:"是否包赔认证"}},[e._v("\n          "+e._s(e.getConfirm(e.memberInfo.baopeiConfirm)))])],1):a("el-card",[a("div",[e._v("后台录入，无用户信息")])])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.tabValue,expression:"tabValue === '2'"}]},[e.extList.ext4.needShow?a("el-card",[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext4.detailOptions,"opetion-date":e.extList.ext4.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e()],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===e.tabValue,expression:"tabValue === '3'"}]},[e.extList.ext5.needShow?a("el-card",[a("el-col",[a("div",{staticClass:"cardBg",attrs:{shadow:"never"}},[a("tedian",{attrs:{"detail-options":e.extList.ext5.detailOptions,"opetion-date":e.extList.ext5.opetionDate},on:{changequfu:e.changequfu}})],1)])],1):e._e()],1)]),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("productForm")}}},[e._v("确 定")])],1)],1)},staticRenderFns:[]};var O=a("VU/8")(L,I,!1,function(e){a("teiS")},"data-v-3f4c2f33",null);t.a=O.exports},teiS:function(e,t){}});