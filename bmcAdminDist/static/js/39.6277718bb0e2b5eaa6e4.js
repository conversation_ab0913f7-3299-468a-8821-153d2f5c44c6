webpackJsonp([39],{"1lns":function(t,e){},eTLC:function(t,e){},iGdT:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a("woOf"),n=a.n(r),l=a("Xxa5"),s=a.n(l),o=a("mvHQ"),c=a.n(o),i=a("exGp"),u=a.n(i),d=a("5rT4"),m={props:{dialogVisible:{type:Boolean,default:!1},url:{type:String,default:""}},data:function(){return{formLabelWidth:"100px",errorTxt:"",rules:{bankNo:[{required:!0,message:"请输入收款账号",trigger:"blur"}],accountName:[{required:!0,message:"请输入收款人",trigger:"blur"}],gameAccount:[{required:!0,message:"请输入游戏账号",trigger:"blur"}],payAmount:[{required:!0,message:"请输入金额",trigger:"blur"}]},bankList:[],contract:{attachment:{}},orderSn:"",hasContract:!1}},mounted:function(){},methods:{handleOrderIdBlur:function(){this.orderSn&&this.getContractPreview()},getContractPreview:function(){var t=this;Object(d.l)({orderSn:this.orderSn}).then(function(e){e&&200==e.code&&(t.contract=e.data,t.hasContract=!0,e.data.attachment&&(t.contract.attachment=JSON.parse(t.contract.attachment)))})},onCancel:function(){this.$emit("close")},onSubmit:function(t){var e,a=this;this.$refs[t].validate((e=u()(s.a.mark(function t(e){var r,l;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!e){t.next=9;break}return(r=n()({},a.contract)).attachment=c()(r.attachment),t.next=5,Object(d.i)(r);case 5:(l=t.sent)&&200==l.code&&Object(d.Z)({id:l.data.id}).then(function(t){200==t.code&&(a.$message.success("合同创建成功"),a.onCancel())}),t.next=10;break;case 9:return t.abrupt("return");case 10:case"end":return t.stop()}},t,a)})),function(t){return e.apply(this,arguments)}))}}},p={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"kferTransfer el-reset-clazz"},[a("el-dialog",{attrs:{visible:t.dialogVisible,"before-close":t.onCancel,"close-on-click-modal":!1,width:"33%",modal:!1,center:"",title:"创建合同"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{ref:"contract",staticClass:"form-box",attrs:{"label-width":t.formLabelWidth,model:t.contract,rules:t.rules}},[a("el-form-item",{attrs:{label:"订单编号"}},[a("el-input",{attrs:{placeholder:"请输入订单编号"},on:{blur:t.handleOrderIdBlur},model:{value:t.orderSn,callback:function(e){t.orderSn=e},expression:"orderSn"}})],1),t._v(" "),t.hasContract?a("div",[a("el-form-item",{attrs:{label:"收款账号",prop:"bankNo"}},[a("el-input",{attrs:{placeholder:"请输入收款账号"},model:{value:t.contract.bankNo,callback:function(e){t.$set(t.contract,"bankNo",e)},expression:"contract.bankNo"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"收款人",prop:"accountName"}},[a("el-input",{attrs:{placeholder:"请输入收款人"},model:{value:t.contract.accountName,callback:function(e){t.$set(t.contract,"accountName",e)},expression:"contract.accountName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:t.contract.phone,callback:function(e){t.$set(t.contract,"phone",e)},expression:"contract.phone"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"游戏账号",prop:"gameAccount"}},[a("el-input",{attrs:{placeholder:"请输入游戏账号"},model:{value:t.contract.gameAccount,callback:function(e){t.$set(t.contract,"gameAccount",e)},expression:"contract.gameAccount"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"汇款金额",prop:"payAmount"}},[a("el-input",{attrs:{placeholder:"请输入合同金额"},model:{value:t.contract.payAmount,callback:function(e){t.$set(t.contract,"payAmount",e)},expression:"contract.payAmount"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"身份证姓名",prop:"userIdName"}},[a("el-input",{attrs:{disabled:""},model:{value:t.contract.userIdName,callback:function(e){t.$set(t.contract,"userIdName",e)},expression:"contract.userIdName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"身份证号",prop:"userIdNumber"}},[a("el-input",{attrs:{disabled:""},model:{value:t.contract.userIdNumber,callback:function(e){t.$set(t.contract,"userIdNumber",e)},expression:"contract.userIdNumber"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"用户地址",prop:"userAddress"}},[a("el-input",{model:{value:t.contract.userAddress,callback:function(e){t.$set(t.contract,"userAddress",e)},expression:"contract.userAddress"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"图片"}},[a("el-image",{staticClass:"userImg",attrs:{src:t.contract.attachment.userIdPic1,"preview-src-list":[t.contract.attachment.userIdPic1]}}),t._v(" "),a("el-image",{staticClass:"userImg",attrs:{src:t.contract.attachment.userIdPic2,"preview-src-list":[t.contract.attachment.userIdPic2]}}),t._v(" "),a("el-image",{staticClass:"userImg",attrs:{src:t.contract.attachment.userOrderPic,"preview-src-list":[t.contract.attachment.userOrderPic]}})],1)],1):t._e()],1),t._v(" "),a("div",{staticClass:"m-footer spaceEnd"},[a("el-button",{on:{click:t.onCancel}},[t._v("取 消")]),t._v(" "),t.hasContract?a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("contract")}}},[t._v("确 定")]):t._e()],1)],1)],1)},staticRenderFns:[]};var f=a("VU/8")(m,p,!1,function(t){a("eTLC")},"data-v-18f77c53",null).exports,h=a("0xDb"),b={pageNum:1,pageSize:20,contractSn:null,orderSn:null,username:null,status:null,productSn:null},v=[{label:"申请中",value:"APPLYING"},{label:"签署中",value:"SIGNING"},{label:"已生效",value:"NORMAL"},{label:"已终止",value:"CANCELLED"}],g={components:{hetong:f},data:function(){return{util:h.b,listQuery:n()({},b),listLoading:!0,list:null,total:null,contractStatusList:v,showHetong:!1,actionObj:{},actionData:{},orderDetail:{}}},created:function(){this.getList()},methods:{createContract:function(){this.showHetong=!0},handleReview:function(t,e){window.open(e.fddFileUrl)},handleViewOrder:function(t,e){this.showOrderDetail=!0,this.orderId=e.orderId},canShow:function(t){var e=this.$store.getters.roles||[];return(e.includes("超级管理员")||e.includes("财务管理员"))&&0!=t.orderStatus&&4!=t.orderStatus&&5!=t.orderStatus&&6!=t.orderStatus&&7!=t.negotiaStatus&&12!=t.orderStatus},getStatusName:function(t){return{APPLYING:"申请中",SIGNING:"签署中",NORMAL:"已生效",CANCELLED:"已终止"}[t]},handleResetSearch:function(){this.listQuery=n()({},b)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},getList:function(){var t=this;this.listLoading=!0,Object(d.k)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})}}},_={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"合同编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"合同编号"},model:{value:t.listQuery.contractSn,callback:function(e){t.$set(t.listQuery,"contractSn",e)},expression:"listQuery.contractSn"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"订单编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"订单编号"},model:{value:t.listQuery.orderSn,callback:function(e){t.$set(t.listQuery,"orderSn",e)},expression:"listQuery.orderSn"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"商品编号"},model:{value:t.listQuery.productSn,callback:function(e){t.$set(t.listQuery,"productSn",e)},expression:"listQuery.productSn"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"用户手机号："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"用户手机号"},model:{value:t.listQuery.username,callback:function(e){t.$set(t.listQuery,"username",e)},expression:"listQuery.username"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"合同状态："}},[a("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.contractStatusList,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right","margin-left":"15px"},attrs:{size:"small"},on:{click:t.createContract}},[t._v("\n            新建合同\n          ")]),t._v(" "),a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:t.handleSearchList}},[t._v("\n            查询搜索\n          ")]),t._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:t.handleResetSearch}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"orderTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[a("el-table-column",{attrs:{fixed:"",label:"合同编号",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.contractSn))]}}])}),t._v(" "),a("el-table-column",{attrs:{fixed:"",label:"商品编号",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productSn))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"游戏账号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.gameAccount))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"用户手机号",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.username))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"订单编号",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.orderSn))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"游戏名称",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productCategoryName))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"金额",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("￥"+t._s(e.row.payAmount))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.getStatusName(e.row.status)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"发起时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.util.timeFormat(e.row.fddStartTime)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"完成时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.util.timeFormat(e.row.fddFinishTime)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作人",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.operateMan))]}}])}),t._v(" "),a("el-table-column",{attrs:{width:"300",label:"操作",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return t.handleReview(e.$index,e.row)}}},[t._v("查看")])]}}])})],1)],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":t.listQuery.pageNum,"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)}}})],1),t._v(" "),t.showHetong?a("hetong",{attrs:{dialogVisible:t.showHetong},on:{close:function(e){t.showHetong=!1}}}):t._e()],1)},staticRenderFns:[]};var S=a("VU/8")(g,_,!1,function(t){a("1lns")},"data-v-33af0a13",null);e.default=S.exports}});