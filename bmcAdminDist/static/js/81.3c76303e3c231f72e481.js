webpackJsonp([81],{sSHW:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=l("fZjL"),s=l.n(a),n=l("Dd8w"),r=l.n(n),i=l("woOf"),u=l.n(i),o=l("mtWM"),c=l.n(o),d=l("ZW30"),p=l("vLgD");var m=l("0xDb"),f={pageNum:1,pageSize:20,orderNo:null,userName:null,status:"4",cardId:null,recommend:null,sellerId:null,startTime:null,endTime:null,time:null},h={1:"周卡",2:"月卡",3:"半年卡",4:"尊享卡"},y={1:"",2:"success",3:"danger",4:"warning"},v={1:"",3:"danger",4:"warning",9:"success",11:"info"},g={1:"待付款",3:"付款失败",4:"已付款",9:"已完成",11:"已取消"},b={data:function(){return{totalPayAmount:"",ossUploadUrl:"https://images2.kkzhw.com",util:m.b,listQuery:u()({},f),listLoading:!0,list:null,total:null,operateType:null,sellerList:[],statusOptions:[{label:"待付款",value:"1"},{label:"付款失败",value:"3"},{label:"已付款",value:"4"},{label:"已完成",value:"9"},{label:"已取消",value:"11"}],cardIdOptions:[{label:"周卡",value:"1"},{label:"月卡",value:"2"},{label:"半年卡",value:"3"},{label:"尊享卡",value:"4"}],recommendOptions:[{label:"是",value:"1"},{label:"否",value:"0"}]}},created:function(){this.getList(),this.getSellerList()},methods:{getTotalPayAmount:function(){var e,t=this,l=r()({},this.listQuery);delete l.pageNum,delete l.pageSize,(e=l,Object(p.a)({url:"/pickColor/totalPayAmount",method:"get",params:e})).then(function(e){200==e.code&&(t.totalPayAmount=e.data)})},getSellerList:function(){var e,t=this;Object(p.a)({url:"/pickColor/seller/list",method:"get",params:e}).then(function(e){200==e.code&&(t.sellerList=e.data,t.sellerList=t.sellerList.filter(function(e){return 0!=e.sellerId}))})},getStType:function(e){return v[e]},getStName:function(e){return g[e]},getMethod:function(e){return 1==e?"微信":"支付宝"},getTypeName:function(e){return h[e]},getTypeTag:function(e){return y[e]},rename:function(e,t){return new File([e],t,{type:e.type})},onFileChange:function(e){var t=this,l=e.target.files[0];Object(d.a)().then(function(e){var a=l.name.split(".").pop(),n=e.data.fileName,r={};r.policy=e.data.policy,r.signature=e.data.signature,r.ossaccessKeyId=e.data.accessKeyId,r.key=e.data.dir+"/"+n+"."+a,r.dir=e.data.dir,r.host=e.data.host;var i=t.rename(l,n+"."+a),u=new FormData,o=r;s()(o).forEach(function(e){"fileName"!==e&&u.append(e,o[e])}),u.append("success_action_status","200"),u.append("file",i,i.name),c.a.post(t.ossUploadUrl,u,{headers:{"Content-Type":"multipart/form-data"}}).then(function(e){var l;200===e.status&&(l={url:r.host+"/"+r.dir+"/"+i.name},Object(p.a)({url:"/pickColor/uploadFile",method:"post",data:l})).then(function(e){200==e.code&&t.$message.success("上传文件成功")})})})},handleResetSearch:function(){this.listQuery=u()({},f)},handleSearchList:function(){this.listQuery.pageNum=1,this.listQuery.time?(this.listQuery.startTime=this.listQuery.time[0],this.listQuery.endTime=this.listQuery.time[1]):(this.listQuery.startTime=null,this.listQuery.endTime=null),this.getList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},getList:function(){var e=this;this.getTotalPayAmount(),this.listLoading=!0;var t,l=u()({},this.listQuery);l.time&&delete l.time,(t=l,Object(p.a)({url:"/pickColor/order/list",method:"get",params:t})).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})}}},_={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[l("div",{staticStyle:{"margin-top":"15px"}},[l("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[l("el-form-item",{attrs:{label:"订单编号"}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"订单编号"},model:{value:e.listQuery.orderNo,callback:function(t){e.$set(e.listQuery,"orderNo",t)},expression:"listQuery.orderNo"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"买家账号"}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"买家账号"},model:{value:e.listQuery.userName,callback:function(t){e.$set(e.listQuery,"userName",t)},expression:"listQuery.userName"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"订单状态"}},[l("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.statusOptions,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"会员卡类型"}},[l("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.cardId,callback:function(t){e.$set(e.listQuery,"cardId",t)},expression:"listQuery.cardId"}},e._l(e.cardIdOptions,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"是否主播推荐"}},[l("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.recommend,callback:function(t){e.$set(e.listQuery,"recommend",t)},expression:"listQuery.recommend"}},e._l(e.recommendOptions,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"所属主播"}},[l("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.sellerId,callback:function(t){e.$set(e.listQuery,"sellerId",t)},expression:"listQuery.sellerId"}},e._l(e.sellerList,function(e){return l("el-option",{key:e.sellerId,attrs:{label:e.sellerName,value:e.sellerId}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"订单创建时间"}},[l("el-date-picker",{attrs:{type:"daterange","range-separator":"至","value-format":"yyyy-MM-dd","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.listQuery.time,callback:function(t){e.$set(e.listQuery,"time",t)},expression:"listQuery.time"}})],1)],1)],1),e._v(" "),l("div",{staticClass:"spaceEnd"},[l("div",{staticStyle:{"margin-right":"10px"}},[e._v("\n        费用"),l("el-tag",{attrs:{type:"warning"}},[e._v(e._s(e.totalPayAmount))])],1),e._v(" "),l("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n        查询搜索\n      ")]),e._v(" "),l("el-button",{attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n        重置\n      ")]),e._v(" "),l("div",{staticClass:"upload"},[e._v("\n        上传客户端(支持 zip、exe上传)\n        "),l("input",{ref:"file",staticClass:"picUpload_btn",attrs:{type:"file",accept:"application/zip,application/octet-stream"},on:{change:e.onFileChange}})])],1)]),e._v(" "),l("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[l("i",{staticClass:"el-icon-tickets"}),e._v(" "),l("span",[e._v("数据列表")])]),e._v(" "),l("div",{staticClass:"table-container"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"orderTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[l("el-table-column",{attrs:{label:"订单编号",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.orderNo))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"买家信息",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.userName))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"订单价格（元）",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.price))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"订单状态",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-tag",{attrs:{type:e.getStType(t.row.status)}},[e._v("\n            "+e._s(e.getStName(t.row.status))+"\n          ")])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"卡片类型",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-tag",{attrs:{type:e.getTypeTag(t.row.cardId),effect:"dark"}},[e._v("\n            "+e._s(e.getTypeName(t.row.cardId))+"\n          ")])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"付款方式",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-tag",{attrs:{type:e.getTypeTag(t.row.method)}},[e._v("\n            "+e._s(e.getMethod(t.row.method))+"\n          ")])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"是否主播推荐",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.sellerId?l("el-tag",{attrs:{type:"success",effect:"dark"}},[e._v("\n            是\n          ")]):l("el-tag",{attrs:{type:"info",effect:"dark"}},[e._v("\n            否\n          ")])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"主播",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.sellerName))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"下单时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.util.timeFormat(t.row.payTime)))]}}])})],1)],1),e._v(" "),l("div",{staticClass:"pagination-container"},[l("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)}}})],1)],1)},staticRenderFns:[]};var w=l("VU/8")(b,_,!1,function(e){l("wrSw")},"data-v-4a5df4e4",null);t.default=w.exports},wrSw:function(e,t){}});