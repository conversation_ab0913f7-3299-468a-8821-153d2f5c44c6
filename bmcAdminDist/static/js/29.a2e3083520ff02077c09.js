webpackJsonp([29],{"/emZ":function(e,t,o){"use strict";var n=o("woOf"),l=o.n(n),a=o("4cjj"),r=o("UgCr"),i=o("mRsl"),u={type:0,name:null,platform:0,amount:null,perLimit:1,minPoint:null,startTime:null,endTime:null,useType:0,note:null,publishCount:null,productRelationList:[],productCategoryRelationList:[]},s=[{label:"全场赠券",value:0},{label:"会员赠券",value:1},{label:"购物赠券",value:2},{label:"注册赠券",value:3}],c=[{label:"全平台",value:0},{label:"移动平台",value:1},{label:"PC平台",value:2}],p={name:"CouponDetail",props:{isEdit:{type:Boolean,default:!1}},data:function(){return{coupon:l()({},u),typeOptions:l()({},s),platformOptions:l()({},c),rules:{name:[{required:!0,message:"请输入优惠券名称",trigger:"blur"},{min:2,max:140,message:"长度在 2 到 140 个字符",trigger:"blur"}],publishCount:[{type:"number",required:!0,message:"只能输入正整数",trigger:"blur"}],amount:[{type:"number",required:!0,message:"面值只能是数值，0.01-10000，限2位小数",trigger:"blur"}],minPoint:[{type:"number",required:!0,message:"只能输入正整数",trigger:"blur"}]},selectProduct:null,selectProductLoading:!1,selectProductOptions:[],selectProductCate:null,productCateOptions:[]}},created:function(){var e=this;this.isEdit&&Object(a.d)(this.$route.query.id).then(function(t){e.coupon=t.data}),this.getProductCateList()},methods:{onSubmit:function(e){var t=this;this.$refs[e].validate(function(o){if(!o)return t.$message({message:"验证失败",type:"error",duration:1e3}),!1;t.$confirm("是否提交数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.isEdit?Object(a.e)(t.$route.query.id,t.coupon).then(function(o){t.$refs[e].resetFields(),t.$message({message:"修改成功",type:"success",duration:1e3}),t.$router.back()}):Object(a.a)(t.coupon).then(function(o){t.$refs[e].resetFields(),t.$message({message:"提交成功",type:"success",duration:1e3}),t.$router.back()})})})},resetForm:function(e){this.$refs[e].resetFields(),this.coupon=l()({},u)},searchProductMethod:function(e){var t=this;""!==e?(this.loading=!0,Object(r.g)({keyword:e}).then(function(e){t.loading=!1;var o=e.data;t.selectProductOptions=[];for(var n=0;n<o.length;n++){var l=o[n];t.selectProductOptions.push({productId:l.id,productName:l.name,productSn:l.productSn})}})):this.selectProductOptions=[]},handleAddProductRelation:function(){null!==this.selectProduct?(this.coupon.productRelationList.push(this.getProductById(this.selectProduct)),this.selectProduct=null):this.$message({message:"请先选择商品",type:"warning"})},handleDeleteProductRelation:function(e,t){this.coupon.productRelationList.splice(e,1)},handleAddProductCategoryRelation:function(){null!==this.selectProductCate&&0!==this.selectProductCate.length?(this.coupon.productCategoryRelationList.push(this.getProductCateByIds(this.selectProductCate)),this.selectProductCate=[]):this.$message({message:"请先选择商品分类",type:"warning"})},handleDeleteProductCateRelation:function(e,t){this.coupon.productCategoryRelationList.splice(e,1)},getProductById:function(e){for(var t=0;t<this.selectProductOptions.length;t++)if(e===this.selectProductOptions[t].productId)return this.selectProductOptions[t];return null},getProductCateList:function(){var e=this;Object(i.e)().then(function(t){var o=t.data;e.productCateOptions=[];for(var n=0;n<o.length;n++){var l=[];if(null!=o[n].children&&o[n].children.length>0)for(var a=0;a<o[n].children.length;a++)l.push({label:o[n].children[a].name,value:o[n].children[a].id});e.productCateOptions.push({label:o[n].name,value:o[n].id,children:l})}})},getProductCateByIds:function(e){for(var t=void 0,o=void 0,n=0;n<this.productCateOptions.length;n++)if(this.productCateOptions[n].value===e[0]){o=this.productCateOptions[n].label;for(var l=0;l<this.productCateOptions[n].children.length;l++)this.productCateOptions[n].children[l].value===e[1]&&(t=this.productCateOptions[n].children[l].label)}return{productCategoryId:e[1],productCategoryName:t,parentCategoryName:o}}}},d={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-card",{staticClass:"form-container",attrs:{shadow:"never"}},[o("el-form",{ref:"couponFrom",attrs:{model:e.coupon,rules:e.rules,"label-width":"150px",size:"small"}},[o("el-form-item",{attrs:{label:"优惠券类型："}},[o("el-select",{model:{value:e.coupon.type,callback:function(t){e.$set(e.coupon,"type",t)},expression:"coupon.type"}},e._l(e.typeOptions,function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{attrs:{label:"优惠券名称：",prop:"name"}},[o("el-input",{staticClass:"input-width",model:{value:e.coupon.name,callback:function(t){e.$set(e.coupon,"name",t)},expression:"coupon.name"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"适用平台："}},[o("el-select",{model:{value:e.coupon.platform,callback:function(t){e.$set(e.coupon,"platform",t)},expression:"coupon.platform"}},e._l(e.platformOptions,function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{attrs:{label:"总发行量：",prop:"publishCount"}},[o("el-input",{staticClass:"input-width",attrs:{placeholder:"只能输入正整数"},model:{value:e.coupon.publishCount,callback:function(t){e.$set(e.coupon,"publishCount",e._n(t))},expression:"coupon.publishCount"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"面额：",prop:"amount"}},[o("el-input",{staticClass:"input-width",attrs:{placeholder:"面值只能是数值，限2位小数"},model:{value:e.coupon.amount,callback:function(t){e.$set(e.coupon,"amount",e._n(t))},expression:"coupon.amount"}},[o("template",{slot:"append"},[e._v("元")])],2)],1),e._v(" "),o("el-form-item",{attrs:{label:"每人限领："}},[o("el-input",{staticClass:"input-width",attrs:{placeholder:"只能输入正整数"},model:{value:e.coupon.perLimit,callback:function(t){e.$set(e.coupon,"perLimit",t)},expression:"coupon.perLimit"}},[o("template",{slot:"append"},[e._v("张")])],2)],1),e._v(" "),o("el-form-item",{attrs:{label:"使用门槛：",prop:"minPoint"}},[o("el-input",{staticClass:"input-width",attrs:{placeholder:"只能输入正整数"},model:{value:e.coupon.minPoint,callback:function(t){e.$set(e.coupon,"minPoint",e._n(t))},expression:"coupon.minPoint"}},[o("template",{slot:"prepend"},[e._v("满")]),e._v(" "),o("template",{slot:"append"},[e._v("元可用")])],2)],1),e._v(" "),o("el-form-item",{attrs:{label:"领取日期：",prop:"enableTime"}},[o("el-date-picker",{staticClass:"input-width",attrs:{type:"date",placeholder:"选择日期"},model:{value:e.coupon.enableTime,callback:function(t){e.$set(e.coupon,"enableTime",t)},expression:"coupon.enableTime"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"有效期："}},[o("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期"},model:{value:e.coupon.startTime,callback:function(t){e.$set(e.coupon,"startTime",t)},expression:"coupon.startTime"}}),e._v(" "),o("span",{staticStyle:{"margin-left":"20px","margin-right":"20px"}},[e._v("至")]),e._v(" "),o("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期"},model:{value:e.coupon.endTime,callback:function(t){e.$set(e.coupon,"endTime",t)},expression:"coupon.endTime"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"可使用商品："}},[o("el-radio-group",{model:{value:e.coupon.useType,callback:function(t){e.$set(e.coupon,"useType",t)},expression:"coupon.useType"}},[o("el-radio-button",{attrs:{label:0}},[e._v("全场通用")]),e._v(" "),o("el-radio-button",{attrs:{label:1}},[e._v("指定分类")]),e._v(" "),o("el-radio-button",{attrs:{label:2}},[e._v("指定商品")])],1)],1),e._v(" "),o("el-form-item",{directives:[{name:"show",rawName:"v-show",value:1===e.coupon.useType,expression:"coupon.useType === 1"}]},[o("el-cascader",{attrs:{clearable:"",placeholder:"请选择分类名称",options:e.productCateOptions},model:{value:e.selectProductCate,callback:function(t){e.selectProductCate=t},expression:"selectProductCate"}}),e._v(" "),o("el-button",{on:{click:function(t){return e.handleAddProductCategoryRelation()}}},[e._v("添加")]),e._v(" "),o("el-table",{ref:"productCateRelationTable",staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.coupon.productCategoryRelationList,border:""}},[o("el-table-column",{attrs:{label:"分类名称",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.parentCategoryName)+">"+e._s(t.row.productCategoryName))]}}])}),e._v(" "),o("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(o){return e.handleDeleteProductCateRelation(t.$index,t.row)}}},[e._v("删除\n            ")])]}}])})],1)],1),e._v(" "),o("el-form-item",{directives:[{name:"show",rawName:"v-show",value:2===e.coupon.useType,expression:"coupon.useType === 2"}]},[o("el-select",{attrs:{filterable:"",remote:"","reserve-keyword":"",placeholder:"商品名称/商品编号","remote-method":e.searchProductMethod,loading:e.selectProductLoading},model:{value:e.selectProduct,callback:function(t){e.selectProduct=t},expression:"selectProduct"}},e._l(e.selectProductOptions,function(t){return o("el-option",{key:t.productId,attrs:{label:t.productName,value:t.productId}},[o("span",{staticStyle:{float:"left"}},[e._v(e._s(t.productName))]),e._v(" "),o("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("NO."+e._s(t.productSn))])])}),1),e._v(" "),o("el-button",{on:{click:function(t){return e.handleAddProductRelation()}}},[e._v("添加")]),e._v(" "),o("el-table",{ref:"productRelationTable",staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.coupon.productRelationList,border:""}},[o("el-table-column",{attrs:{label:"商品名称",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.productName))]}}])}),e._v(" "),o("el-table-column",{attrs:{label:"编号",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("NO."+e._s(t.row.productSn))]}}])}),e._v(" "),o("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(o){return e.handleDeleteProductRelation(t.$index,t.row)}}},[e._v("删除\n            ")])]}}])})],1)],1),e._v(" "),o("el-form-item",{attrs:{label:"备注："}},[o("el-input",{staticClass:"input-width",attrs:{type:"textarea",rows:5,placeholder:"请输入内容"},model:{value:e.coupon.note,callback:function(t){e.$set(e.coupon,"note",t)},expression:"coupon.note"}})],1),e._v(" "),o("el-form-item",[o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onSubmit("couponFrom")}}},[e._v("提交")]),e._v(" "),e.isEdit?e._e():o("el-button",{on:{click:function(t){return e.resetForm("couponFrom")}}},[e._v("重置")])],1)],1)],1)},staticRenderFns:[]};var m=o("VU/8")(p,d,!1,function(e){o("en4y")},"data-v-50e5c623",null);t.a=m.exports},"5y1R":function(e,t){},"718U":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={name:"addCoupon",components:{CouponDetail:o("/emZ").a}},l={render:function(){var e=this.$createElement;return(this._self._c||e)("coupon-detail",{attrs:{isEdit:!1}})},staticRenderFns:[]};var a=o("VU/8")(n,l,!1,function(e){o("5y1R")},"data-v-16744c27",null);t.default=a.exports},en4y:function(e,t){}});