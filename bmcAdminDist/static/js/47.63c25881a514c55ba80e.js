webpackJsonp([47],{"+qTr":function(t,e){},dHnX:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("mvHQ"),l=n.n(a),r=n("3idm"),s={name:"ProductAttrList",filters:{inputTypeFilter:function(t){return 1===t?"从列表中选取":"手工录入"},selectTypeFilter:function(t){return 1===t?"单选":2===t?"多选":3===t?"级联":"输入框"},filterTypeFitler:function(t){return 0==t?"单选":"多选"},searchTypeFitler:function(t){return 1===t?"关键字检索":2===t?"范围检索":3===t?"高级检索":"不需要检索"},handAddStatusFilter:function(t){return 1==t?"是":2==t?"隐藏":"否"}},data:function(){return{tabValue:"1",list:null,total:null,listLoading:!0,listQuery:{pageNum:1,pageSize:80},operateType:null,multipleSelection:[],operates:[{label:"删除",value:"deleteProductAttr"}]}},created:function(){this.getList()},methods:{jsonFormat:function(t){try{return t=JSON.parse(t),t=l()(t,null,2)}catch(e){return t}},formatOpts:function(t,e){return 3===e.selectType?"[级联]":t},changeTab:function(){this.getList()},getList:function(){var t=this;this.listLoading=!0,this.listQuery.type=parseInt(this.tabValue),Object(r.c)(this.$route.query.cid,this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})},addProductAttr:function(){this.$router.push({path:"/pms/addProductAttr",query:{cid:this.$route.query.cid,type:parseInt(this.tabValue)}})},handleSelectionChange:function(t){this.multipleSelection=t},handleBatchOperate:function(){if(this.multipleSelection<1)this.$message({message:"请选择一条记录",type:"warning",duration:1e3});else if("deleteProductAttr"===this.operateType){for(var t=[],e=0;e<this.multipleSelection.length;e++)t.push(this.multipleSelection[e].id);this.handleDeleteProductAttr(t)}else this.$message({message:"请选择批量操作类型",type:"warning",duration:1e3})},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleUpdate:function(t,e){this.$router.push({path:"/pms/updateProductAttr",query:{id:e.id,type:parseInt(this.tabValue)}})},handleDeleteProductAttr:function(t){var e=this;this.$confirm("是否要删除该属性","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var n=new URLSearchParams;n.append("ids",t),Object(r.b)(n).then(function(t){e.$message({message:"删除成功",type:"success",duration:1e3}),e.getList()})})},handleDelete:function(t,e){var n=[];n.push(e.id),this.handleDeleteProductAttr(n)}}},i={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[n("i",{staticClass:"el-icon-tickets",staticStyle:{"margin-top":"5px"}}),t._v(" "),n("span",{staticStyle:{"margin-top":"5px"}},[t._v("数据列表")]),t._v(" "),n("el-button",{staticClass:"btn-add",attrs:{size:"mini"},on:{click:function(e){return t.addProductAttr()}}},[t._v("\n      添加\n    ")])],1),t._v(" "),n("div",{staticClass:"table-container"},[n("el-tabs",{on:{"tab-click":t.changeTab},model:{value:t.tabValue,callback:function(e){t.tabValue=e},expression:"tabValue"}},[n("el-tab-pane",{attrs:{name:"1"}},[n("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n          扩展属性1\n          "),n("el-popover",{attrs:{placement:"top-start",title:"单选属性或输入框",width:"200",trigger:"hover",content:"性别区服衣品等"}},[n("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"2"}},[n("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n          扩展属性2\n          "),n("el-popover",{attrs:{placement:"top-start",title:"多选属性",width:"200",trigger:"hover",content:"皮肤坐骑等多选属性"}},[n("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"3"}},[n("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n          扩展属性3\n          "),n("el-popover",{attrs:{placement:"top-start",title:"搜索标签",width:"200",trigger:"hover",content:"专区或者账号标签"}},[n("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"4"}},[n("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n          常见问题4\n          "),n("el-popover",{attrs:{placement:"top-start",title:"常见问题",width:"200",trigger:"hover",content:"用户咨询产品会显示的常见问题，方便用户继续咨询"}},[n("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"5"}},[n("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n          账号信息5\n          "),n("el-popover",{attrs:{placement:"top-start",title:"自动上号填的信息",width:"200",trigger:"hover",content:"账号密码或王者营地等信息"}},[n("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"6"}},[n("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n          扩展属性6\n          "),n("el-popover",{attrs:{placement:"top-start",title:"商品前台逻辑依赖属性",width:"200",trigger:"hover",content:"勿动"}},[n("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"7"}},[n("div",{attrs:{slot:"label"},slot:"label"},[t._v("\n          扩展属性7\n          "),n("el-popover",{attrs:{placement:"top-start",title:"订单属性",width:"200",trigger:"hover",content:"勿动"}},[n("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)])],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productAttrTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"60",align:"center"}}),t._v(" "),n("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"属性名称",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.name))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"商品类型",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.$route.query.cname))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"属性录入方式",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("selectTypeFilter")(e.row.selectType)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"是否客户端必填",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("handAddStatusFilter")(e.row.handAddStatus)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"属性筛选方式",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("filterTypeFitler")(e.row.filterType)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"属性筛选方式",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("searchTypeFitler")(e.row.searchType)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"可选值列表",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top-start"}},[n("pre",{staticStyle:{width:"260px","max-height":"300px","overflow-y":"auto"},attrs:{slot:"content"},slot:"content"},[t._v(t._s(t.jsonFormat(e.row.inputList)))]),t._v(" "),n("div",{staticClass:"ellipsis"},[t._v("\n              "+t._s(t.formatOpts(e.row.inputList,e.row))+"\n            ")])])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"综合排序",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.searchSort))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"排序",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.sort))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.handleUpdate(e.$index,e.row)}}},[t._v("编辑\n          ")]),t._v(" "),n("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(n){return t.handleDelete(e.$index,e.row)}}},[t._v("删除\n          ")])]}}])})],1)],1),t._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[80,60,40],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},staticRenderFns:[]};var o=n("VU/8")(s,i,!1,function(t){n("+qTr")},"data-v-ef3df374",null);e.default=o.exports}});