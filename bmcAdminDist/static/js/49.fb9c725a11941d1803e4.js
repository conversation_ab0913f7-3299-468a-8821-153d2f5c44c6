webpackJsonp([49],{OtMI:function(e,t){},dfom:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("woOf"),l=a.n(i),s=a("bOdI"),n=a.n(s),r=a("mRsl"),o=a("M9A7"),u=a("vLgD");var c,d=a("0Dnf"),p=a("0xDb"),v=(c={projectCategoryId:"",pageNum:1,pageSize:20,operateType:null,operateMan:null},n()(c,"operateType",null),n()(c,"startTime",null),n()(c,"endTime",null),n()(c,"time",null),c),m={data:function(){return{util:p.b,listQuery:l()({},v),listLoading:!0,list:null,total:null,operateManList:[],operateTypeList:d.j,showDetailDialog:!1,detail:{}}},created:function(){this.getList(),this.getCate(),this.getManList()},methods:{isOperateTypeAdmin:function(e){return!!["ADMIN_ADD","ADMIN_EDIT","ADMIN_DELETE","ADMIN_VERIFY","ADMIN_BIND"].includes(e)},getNameByType:function(e){return(d.k[e]||{}).label||e},getManList:function(){var e=this;Object(o.d)({pageNum:1,pageSize:999}).then(function(t){var a=t.data.list;e.operateManList=a.map(function(e){return{label:e.nickName,value:e.username}})})},showDetail:function(e){var t,a=this,i={recordId:e.id};(t=i,Object(u.a)({url:"/product/history/detail",method:"get",params:t})).then(function(e){200==e.code&&(a.detail=e.data,a.showDetailDialog=!0)})},getCate:function(){var e=this;Object(r.d)(74,{pageNum:1,pageSize:999}).then(function(t){200==t.code&&(e.listCategory=t.data.list)})},handleResetSearch:function(){this.listQuery=l()({},v)},handleSearchList:function(){this.listQuery.pageNum=1,this.listQuery.time?(this.listQuery.startTime=this.listQuery.time[0],this.listQuery.endTime=this.listQuery.time[1]):(this.listQuery.startTime=null,this.listQuery.endTime=null),this.getList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},getList:function(){var e=this;this.listLoading=!0;var t,a=l()({},this.listQuery);a.time&&delete a.time,(t=a,Object(u.a)({url:"/product/history",method:"get",params:t})).then(function(t){e.listLoading=!1,e.list=t.data})}}},y={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"账号编号"}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"账号编号"},model:{value:e.listQuery.productSn,callback:function(t){e.$set(e.listQuery,"productSn",t)},expression:"listQuery.productSn"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"对象类型"}},[a("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.operateType,callback:function(t){e.$set(e.listQuery,"operateType",t)},expression:"listQuery.operateType"}},e._l(e.operateTypeList,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"操作人"}},[a("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.operateMan,callback:function(t){e.$set(e.listQuery,"operateMan",t)},expression:"listQuery.operateMan"}},e._l(e.operateManList,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"创建时间"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","value-format":"yyyy-MM-dd","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.listQuery.time,callback:function(t){e.$set(e.listQuery,"time",t)},expression:"listQuery.time"}})],1)],1)],1),e._v(" "),a("div",{staticClass:"spaceEnd"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n        查询搜索\n      ")]),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n        重置\n      ")])],1)]),e._v(" "),a("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "),a("span",[e._v("数据列表")])]),e._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"orderTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[a("el-table-column",{attrs:{label:"编号",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"账号编号",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.productSn))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"对象类型",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e.getNameByType(t.row.operateType))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作人",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.operateMan))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"录入时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e.util.timeFormat(t.row.createTime))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"success"},on:{click:function(a){return e.showDetail(t.row)}}},[e._v("详情")])]}}])})],1)],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)}}})],1),e._v(" "),a("el-dialog",{attrs:{title:"详情",visible:e.showDetailDialog,width:"40%",center:""},on:{"update:visible":function(t){e.showDetailDialog=t}}},[a("div",{staticClass:"detailbox"},[a("el-link",{attrs:{type:"success"}},[e._v("详情信息:")]),e._v(" "),a("div",{staticClass:"spaceStart item"},[a("div",{staticClass:"label"},[e._v("详情账号：")]),e._v(" "),a("div",[e._v(e._s(e.detail.productSn))])]),e._v(" "),a("div",{staticClass:"spaceStart item"},[a("div",{staticClass:"label"},[e._v("对象类型：")]),e._v(" "),e.isOperateTypeAdmin(e.detail.operateType)?a("el-tag",{attrs:{type:"success"}},[e._v("\n          "+e._s(e.getNameByType(e.detail.operateType))+"\n        ")]):a("el-tag",[e._v("\n          "+e._s(e.getNameByType(e.detail.operateType))+"\n        ")])],1),e._v(" "),a("div",{staticClass:"spaceStart item"},[a("div",{staticClass:"label"},[e._v("操作人：")]),e._v(" "),a("div",[e._v(e._s(e.detail.operateMan))])]),e._v(" "),a("div",{staticClass:"spaceStart item"},[a("div",{staticClass:"label"},[e._v("操作详情：")]),e._v(" "),a("pre",[e._v(e._s(e.detail.operateDetail))])]),e._v(" "),a("div",{staticClass:"spaceStart item"},[a("div",{staticClass:"label"},[e._v("操作时间：")]),e._v(" "),a("div",[e._v(e._s(e.util.timeFormat(e.detail.createTime)))])])],1)])],1)},staticRenderFns:[]};var h=a("VU/8")(m,y,!1,function(e){a("OtMI")},"data-v-d6c1ee3a",null);t.default=h.exports}});