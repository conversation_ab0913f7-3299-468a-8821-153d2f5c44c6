webpackJsonp([57],{"7KJ4":function(t,e){},SILY:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("mvHQ"),l=a.n(i),n=a("Lfj9"),o={name:"ProductCateList",data:function(){return{list:null,dialogFormVisibleTitle:"",dialogFormVisible:!1,total:null,productCategoryList:[],listLoading:!0,productUnitList:[{id:1,name:"NEW"},{id:2,name:"HOT"},{id:0,name:"无"}],formValue:{productCategoryId:"",productUnit:"",description:"",showStatus:1,sort:0},listQuery:{pageNum:1,pageSize:20},parentId:0}},watch:{},created:function(){this.getList()},mounted:function(){var t=this;Object(n._5)({pageNum:1,pageSize:1e3}).then(function(e){t.productCategoryList=e.data.list})},methods:{cloneBtn:function(){this.dialogFormVisible=!1},submitBtn:function(){var t=this;this.$refs.form.validate(function(e){e&&("新增"==t.dialogFormVisibleTitle?Object(n.K)(t.formValue).then(function(e){t.$message.success("添加成功"),t.dialogFormVisible=!1,t.getList()}):Object(n.M)(t.formValue.id,t.formValue).then(function(e){t.$message.success("编辑成功"),t.dialogFormVisible=!1,t.getList()}))})},handleAddProductCate:function(){this.dialogFormVisibleTitle="新增",this.formValue={productCategoryId:"",productUnit:"",description:"",showStatus:1,sort:0},this.dialogFormVisible=!0},getList:function(){var t=this;this.listLoading=!0,Object(n.L)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleShowStatusChange:function(t,e){var a=this,i=new URLSearchParams,l=[];l.push(e.id),i.append("ids",l),i.append("showStatus",e.showStatus),Object(n._31)(i).then(function(t){a.$message({message:"修改成功",type:"success",duration:1e3})})},handleUpdate:function(t,e){this.dialogFormVisibleTitle="编辑",this.formValue=JSON.parse(l()(e)),this.dialogFormVisible=!0},handleDelete:function(t,e){var a=this;this.$confirm("是否要删除该分类","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(n.h)(e.id).then(function(t){a.$message({message:"删除成功",type:"success",duration:1e3}),a.getList()})})}}},s={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[a("i",{staticClass:"el-icon-tickets",staticStyle:{"margin-top":"5px"}}),t._v(" "),a("span",{staticStyle:{"margin-top":"5px"}},[t._v("数据列表")]),t._v(" "),a("el-button",{staticClass:"btn-add",attrs:{size:"mini"},on:{click:function(e){return t.handleAddProductCate()}}},[t._v("\n            添加\n        ")])],1),t._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productCateTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[a("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),a("el-table-column",{attrs:{width:"100",label:"分类图标",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("img",{staticStyle:{width:"60px",height:"60px"},attrs:{src:t.row.icon,alt:""}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"分类名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.name))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"是否显示",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                    "+t._s(e.row.showStatus?"是":"否")+"\n                    ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"排序",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.sort))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return t.handleUpdate(e.$index,e.row)}}},[t._v("编辑\n                    ")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return t.handleDelete(e.$index,e.row)}}},[t._v("删除\n                    ")])]}}])})],1)],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("el-dialog",{attrs:{title:t.dialogFormVisibleTitle,visible:t.dialogFormVisible},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"form",attrs:{"label-width":"100px",model:t.formValue}},[a("el-form-item",{attrs:{label:"产品名称：",rules:[{required:!0,message:"请选择产品名称",trigger:"change"}],prop:"productCategoryId"}},[a("el-select",{staticStyle:{width:"300px"},attrs:{disabled:"编辑"==t.dialogFormVisibleTitle,filterable:"",placeholder:"请选择"},model:{value:t.formValue.productCategoryId,callback:function(e){t.$set(t.formValue,"productCategoryId",e)},expression:"formValue.productCategoryId"}},t._l(t.productCategoryList,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"产品标签："}},[a("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择"},model:{value:t.formValue.productUnit,callback:function(e){t.$set(t.formValue,"productUnit",e)},expression:"formValue.productUnit"}},t._l(t.productUnitList,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.name}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"分类说明："}},[a("el-input",{staticStyle:{width:"300px"},attrs:{type:"textarea",autocomplete:"off"},model:{value:t.formValue.description,callback:function(e){t.$set(t.formValue,"description",e)},expression:"formValue.description"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"是否显示："}},[a("el-radio-group",{model:{value:t.formValue.showStatus,callback:function(e){t.$set(t.formValue,"showStatus",e)},expression:"formValue.showStatus"}},[a("el-radio",{attrs:{label:1}},[t._v("是")]),t._v(" "),a("el-radio",{attrs:{label:0}},[t._v("否")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"排序："}},[a("el-input",{staticClass:"editNum",staticStyle:{width:"300px"},attrs:{type:"number"},model:{value:t.formValue.sort,callback:function(e){t.$set(t.formValue,"sort",e)},expression:"formValue.sort"}})],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.cloneBtn}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.submitBtn}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var r=a("VU/8")(o,s,!1,function(t){a("7KJ4")},"data-v-92e8986e",null);e.default=r.exports}});