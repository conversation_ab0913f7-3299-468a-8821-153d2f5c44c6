webpackJsonp([92],{qNxn:function(t,e,l){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=l("mvHQ"),i=l.n(a),n=l("Lfj9"),s=l("PJh5"),r=l.n(s),o={name:"ProductCateList",data:function(){return{productList:[],list:null,dialogFormVisibleTitle:"",dialogFormVisible:!1,total:null,productCategoryList:[],listLoading:!0,productStatus:{ON_SALE:"在售",DOWN_SHELF:"下架/删除",SOLD:"已售",UNKNOWN:"未知"},productLeadStatus:{NEW:"新线索",CONTACTED:"已联系",CONFIRMED:"已确认",CONVERTED:"已转化",LOST:"已丢失"},statusType:[{label:"新线索",value:"NEW"},{label:"已联系",value:"CONTACTED"},{label:"已确认",value:"CONFIRMED"},{label:"已转化",value:"CONVERTED"},{label:"已丢失",value:"LOST"}],productUnitList:[{id:1,name:"NEW"},{id:2,name:"HOT"},{id:0,name:"无"}],formValue:{productSn:"",note:"",type:"SALES",status:""},listQuery:{pageNum:1,pageSize:20,type:"RECYCLED",productSn:"",status:""},parentId:0}},watch:{},created:function(){this.getList()},mounted:function(){},methods:{formatTime:function(t){return t=new Date(t),r()(t).format("YYYY-MM-DD HH:mm:ss")},handleMsg:function(t,e){var l="p2p-"+e.productMemberIm;this.$router.push({path:"/productKF/imList?sessionId="+l})},searchProduct:function(){var t=this;this.formValue.productSn?Object(n.D)({productSn:this.formValue.productSn}).then(function(e){if(e.data&&e.data.product){var l=[];l.push(e.data.product),t.productList=l}console.log(e,2222222)}):this.productList=[]},cloneBtn:function(){this.dialogFormVisible=!1},submitBtn:function(){var t=this;this.$refs.form.validate(function(e){e&&("新增"==t.dialogFormVisibleTitle?Object(n._1)(t.formValue).then(function(e){t.$message.success("添加成功"),t.dialogFormVisible=!1,t.getList()}):Object(n._4)(t.formValue).then(function(e){t.$message.success("编辑成功"),t.dialogFormVisible=!1,t.getList()}))})},handleAddProductCate:function(){console.log("开始执行 handleAddProductCate 函数"),this.dialogFormVisibleTitle="新增",this.productList=[],this.formValue={productSn:"",note:"",type:"SALES",status:""},this.dialogFormVisible=!0},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleResetSearch:function(){this.listQuery.productSn="",this.listQuery.status="",this.listQuery.pageNum=1,this.getList()},getList:function(){var t=this;this.listLoading=!0,Object(n._3)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleUpdate:function(t,e){this.dialogFormVisibleTitle="编辑",this.formValue=JSON.parse(i()(e)),this.formValue.type="SALES",this.searchProduct(),this.dialogFormVisible=!0},handleDelete:function(t,e){var l=this;this.$confirm("是否要删除该分类","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(n._2)({id:e.id,type:"SALES"}).then(function(t){l.$message({message:"删除成功",type:"success",duration:1e3}),l.getList()})})}}},u={render:function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"app-container"},[l("el-form",{staticStyle:{width:"100%","margin-bottom":"-20px"},attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"100px"}},[l("el-form-item",{attrs:{label:"商品编号："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"请输入商品编号",clearable:""},model:{value:t.listQuery.productSn,callback:function(e){t.$set(t.listQuery,"productSn",e)},expression:"listQuery.productSn"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"线索状态："}},[l("el-select",{attrs:{placeholder:"请选择状态"},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusType,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),l("el-form-item",[l("el-button",{staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n        重置\n      ")]),t._v(" "),l("el-button",{staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n        查询搜索\n      ")])],1),t._v(" "),l("el-button",{staticClass:"btn-add",staticStyle:{float:"right"},attrs:{size:"small"},on:{click:function(e){return t.handleAddProductCate()}}},[t._v("\n      添加\n    ")])],1),t._v(" "),l("div",{staticClass:"table-container"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productCateTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[l("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"店主ID",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.leadMemberId))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"商品编号",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productSn))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"商品状态",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.productStatus[e.row.productStatus]))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"商品分类名称",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.productCategoryName))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"底价",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.originalPrice))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"现价",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.price))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"商品标题",width:"260",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("div",{staticClass:"text_linTwo",attrs:{title:e.row.subTitle}},[t._v("\n            "+t._s(e.row.subTitle)+"\n          ")])]}}])}),t._v(" "),l("el-table-column",{attrs:{width:"100",label:"封面图",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[l("img",{staticStyle:{width:"60px",height:"60px"},attrs:{src:t.row.pic,alt:""}})]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"线索状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.productLeadStatus[e.row.status]))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"备注",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.note))]}}])}),t._v(" "),l("el-table-column",{attrs:{label:"操作",width:"250",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-button",{attrs:{size:"mini"},on:{click:function(l){return t.handleMsg(e.$index,e.row)}}},[t._v("进入会话\n          ")]),t._v(" "),l("el-button",{attrs:{size:"mini"},on:{click:function(l){return t.handleUpdate(e.$index,e.row)}}},[t._v("编辑\n          ")]),t._v(" "),l("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(l){return t.handleDelete(e.$index,e.row)}}},[t._v("删除\n          ")])]}}])})],1)],1),t._v(" "),t.list&&t.list.length>0?l("div",{staticClass:"pagination-container"},[l("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1):t._e(),t._v(" "),l("el-dialog",{attrs:{title:t.dialogFormVisibleTitle,visible:t.dialogFormVisible,width:"1000px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[l("el-form",{ref:"form",attrs:{"label-width":"100px",model:t.formValue}},[l("el-form-item",{attrs:{label:"商品编号："}},[l("el-input",{staticStyle:{width:"300px"},on:{change:t.searchProduct},model:{value:t.formValue.productSn,callback:function(e){t.$set(t.formValue,"productSn",e)},expression:"formValue.productSn"}})],1),t._v(" "),t.productList.length>0?l("el-table",{staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{data:t.productList}},[l("el-table-column",{attrs:{align:"center",prop:"productSn",label:"商品编号",width:"150"}}),t._v(" "),l("el-table-column",{attrs:{align:"center",prop:"productCategoryName",label:"分类名称",width:"150"}}),t._v(" "),l("el-table-column",{staticStyle:{padding:"0px"},attrs:{align:"center",prop:"productCategoryName",label:"商品图",width:"120"},scopedSlots:t._u([{key:"default",fn:function(t){return[l("img",{staticStyle:{width:"100%"},attrs:{src:t.row.pic,alt:""}})]}}],null,!1,42026410)}),t._v(" "),l("el-table-column",{attrs:{align:"center",prop:"price",label:"单价",width:"100"}}),t._v(" "),l("el-table-column",{attrs:{prop:"productCategoryName",label:"商品标题",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("div",{staticClass:"text_linTwo",attrs:{title:e.row.detailTitle}},[t._v("\n              "+t._s(e.row.detailTitle)+"\n            ")])]}}],null,!1,2747488504)})],1):t._e(),t._v(" "),l("el-form-item",{attrs:{label:"线索状态："}},[l("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择线索状态"},model:{value:t.formValue.status,callback:function(e){t.$set(t.formValue,"status",e)},expression:"formValue.status"}},t._l(t.statusType,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"备注："}},[l("el-input",{staticStyle:{width:"300px"},attrs:{type:"textarea",autocomplete:"off"},model:{value:t.formValue.note,callback:function(e){t.$set(t.formValue,"note",e)},expression:"formValue.note"}})],1)],1),t._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:t.cloneBtn}},[t._v("取 消")]),t._v(" "),l("el-button",{attrs:{disabled:!(t.productList.length>0),type:"primary"},on:{click:t.submitBtn}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var c=l("VU/8")(o,u,!1,function(t){l("wmf8")},"data-v-2bb0568c",null);e.default=c.exports},wmf8:function(t,e){}});