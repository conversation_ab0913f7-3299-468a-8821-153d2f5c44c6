webpackJsonp([98],{V0Wh:function(e,t){},hwrQ:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n("woOf"),l=n.n(a),i=(n("PJh5"),n("IcnI"),n("0xDb")),r=n("CTAa"),s={0:"交易订单群",1:"回收沟通群",2:"担保沟通群",3:"估价沟通群"},u={keyword:null,pageNum:1,pageSize:20},o={components:{},data:function(){return{util:i.b,listQuery:l()({},u),list:null,total:null,listLoading:!0}},created:function(){this.initList()},methods:{isTeam:function(e){return/^\d+$/.test(e)},goChat:function(e){var t=this;Object(r.f)(e).then(function(e){200==e.code&&t.$message({type:"success",message:e.message})})},onDel:function(e,t){var n=this;this.$confirm("是否要删除","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(r.e)(t.id).then(function(e){n.$message({type:"success",message:"删除成功!"}),n.initList()})})},getType:function(e){return s[e]},handleSearchList:function(){this.listQuery.pageNum=1,this.initList()},handleResetSearch:function(){this.listQuery=l()({},u)},handleSelectionChange:function(){},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.initList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.initList()},initList:function(){var e=this;Object(r.c)(this.listQuery).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})}}},c={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[n("div",{staticStyle:{"margin-top":"15px"}},[n("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[n("el-form-item",{attrs:{label:"商品编号："}},[n("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品编号"},model:{value:e.listQuery.productSn,callback:function(t){e.$set(e.listQuery,"productSn",t)},expression:"listQuery.productSn"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"订单编号："}},[n("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"订单编号"},model:{value:e.listQuery.orderSn,callback:function(t){e.$set(e.listQuery,"orderSn",t)},expression:"listQuery.orderSn"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"用户名："}},[n("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"用户名"},model:{value:e.listQuery.username,callback:function(t){e.$set(e.listQuery,"username",t)},expression:"listQuery.username"}})],1),e._v(" "),n("el-form-item",[n("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询结果\n          ")]),e._v(" "),n("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":e.listQuery.pageNum,total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"index",width:"60",label:"序号"}}),e._v(" "),n("el-table-column",{attrs:{label:"会话群类型",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(e.getType(t.row.type)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"云信群组号",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v("\n            "+e._s(t.row.teamId)),e.isTeam(t.row.teamId)?n("span",{staticClass:"goChat",on:{click:function(n){return e.goChat(t.row.teamId)}}},[e._v("进入群聊")]):e._e()])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"买家IM",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.buyerim))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"买家用户名",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.buyerUsername))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"卖家IM",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.sellerim))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"卖家用户名",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.sellerUsername))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"流程客服IM",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.flowim))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"群主IM",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.ownerim))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"订单编号",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.orderSn))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"商品编号",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.productSn))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"流程步骤",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v(e._s(t.row.teamState))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"创建时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v("\n            "+e._s(e.util.timeFormat(t.row.createTime,"YYYY-MM-DD HH:mm:ss"))+"\n          ")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"最后更新时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("p",[e._v("\n            "+e._s(e.util.timeFormat(t.row.updateTime,"YYYY-MM-DD HH:mm:ss"))+"\n          ")])]}}])})],1)],1),e._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":e.listQuery.pageNum,total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)},staticRenderFns:[]};var d=n("VU/8")(o,c,!1,function(e){n("V0Wh")},"data-v-0a6e6f44",null);t.default=d.exports}});