webpackJsonp([51],{"1PiQ":function(e,t){},yspQ:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("woOf"),n=a.n(l),i=a("UgCr"),s=a("xT6B"),r={phone:null,pageNum:1,pageSize:20},u={name:"Kefuguanli",components:{SingleUpload:a("TZVV").a},filters:{formatCreateTime:function(e){var t=new Date(e);return Object(s.a)(t,"YYYY-MM-DD HH:mm:ss")},verifyStatusFilter:function(e){return e?"启用":"禁用"},verifyStatusFilter2:function(e){return e?"禁用":"启用"}},data:function(){return{subjectList:[],prefrenceAreaList:[],listLoading:!0,list:[],total:0,showAddModel:!1,listQuery:n()({},r),value:{phone:"",userIdName:"",userIdNumber:"",note:""},rules:{},formLabelWidth:"140px"}},computed:{selectSubject:{get:function(){var e=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return e;for(var t=0;t<this.value.subjectProductRelationList.length;t++)e.push(this.value.subjectProductRelationList[t].subjectId);return e},set:function(e){this.value.subjectProductRelationList=[];for(var t=0;t<e.length;t++)this.value.subjectProductRelationList.push({subjectId:e[t]})}}},created:function(){this.getList()},methods:{handleResetSearch:function(){this.listQuery=n()({},r)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},filterMethod:function(e,t){return t.label.indexOf(e)>-1},getButtonType:function(e){return e?"danger":"success"},toggleState:function(e,t){var a=this,l=t.id,s=n()({},t);Object(i.b)(l,s).then(function(){a.getList()})},getList:function(){var e=this;this.listLoading=!0,Object(i.i)(this.listQuery).then(function(t){e.listLoading=!1;var a=t.data.list;e.list=a,e.total=t.data.total})},handleUpdateProduct:function(e,t){var a=this,l=t.id;Object(i.h)(l).then(function(e){a.value=n()({},e.data),a.id=l,a.isEdit=!0,a.showAddModel=!0})},createHelp:function(){this.clearValue(),this.id="",this.isEdit=!1,this.showAddModel=!0},clearValue:function(){this.value={phone:"",userIdName:"",userIdNumber:"",note:""}},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},cancel:function(){this.showAddModel=!1},submitForm:function(){var e=this;this.showAddModel=!1,this.isEdit?Object(i.b)(this.id,this.value).then(function(){e.getList()}):Object(i.a)(this.value).then(function(){e.getList()})}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"table-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",[a("i",{staticClass:"el-icon-search"}),e._v(" "),a("span",[e._v(e._s(e.categoryName))])]),e._v(" "),a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"手机号："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"手机号"},model:{value:e.listQuery.phone,callback:function(t){e.$set(e.listQuery,"phone",t)},expression:"listQuery.phone"}})],1),e._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleSearchList}},[e._v("\n            查询结果\n          ")]),e._v(" "),a("el-button",{staticStyle:{"margin-right":"15px"},attrs:{size:"small"},on:{click:e.handleResetSearch}},[e._v("\n            重置\n          ")]),e._v(" "),a("el-button",{staticClass:"add-help",attrs:{type:"primary"},on:{click:e.createHelp}},[e._v("新建黑号")])],1)],1)]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[a("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"手机号码",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.phone))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"真实姓名",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.userIdName))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"身份证号码",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.userIdNumber))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"备注",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[e._v(e._s(t.row.note))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatCreateTime")(t.row.updateTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatCreateTime")(t.row.createTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleUpdateProduct(t.$index,t.row)}}},[e._v("详情/编辑\n            ")])],1)]}}])})],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),e.showAddModel?a("el-dialog",{attrs:{visible:e.showAddModel,width:"80%",top:"1vh"},on:{"update:visible":function(t){e.showAddModel=t}}},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("编辑黑号信息")])]),e._v(" "),a("el-form",{ref:"helpForm",staticClass:"form-box",attrs:{model:e.value,rules:e.rules,"label-width":e.formLabelWidth}},[a("el-form-item",{attrs:{label:"手机号码"}},[a("el-input",{model:{value:e.value.phone,callback:function(t){e.$set(e.value,"phone",t)},expression:"value.phone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"真实姓名"}},[a("el-input",{model:{value:e.value.userIdName,callback:function(t){e.$set(e.value,"userIdName",t)},expression:"value.userIdName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"身份证号码"}},[a("el-input",{model:{value:e.value.userIdNumber,callback:function(t){e.$set(e.value,"userIdNumber",t)},expression:"value.userIdNumber"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{model:{value:e.value.note,callback:function(t){e.$set(e.value,"note",t)},expression:"value.note"}})],1)],1),e._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("helpForm")}}},[e._v("确 定")])],1)],1)],1):e._e()],1)])},staticRenderFns:[]};var c=a("VU/8")(u,o,!1,function(e){a("1PiQ")},"data-v-c752a2c6",null);t.default=c.exports}});