webpackJsonp([91],{Adki:function(e,t){},QuLB:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=l("Dd8w"),n=l.n(a),r=l("bOdI"),i=l.n(r),s=l("woOf"),o=l.n(s),u=l("c9K3"),c=l("5rT4"),d=l("FWz8"),f=l("mRsl"),v=l("0xDb"),p={pageNum:1,pageSize:20,negotiationStatus:null,buyerUsername:null,sellerUsername:null,productSn:null,payType:null,sourceType:null,orderStatus:null,time:null},m=[{label:"未支付",value:"0"},{label:"支付宝",value:"1"},{label:"微信",value:"2"}],b=[{label:"待付款",value:"0"},{label:"已预定",value:"1"},{label:"换绑中",value:"2"},{label:"换绑完成",value:"3"},{label:"已关闭",value:"4"},{label:"无效订单",value:"5"},{label:"已退款",value:"6"},{label:"待汇款",value:"7"},{label:"汇款中",value:"8"},{label:"代发待审核",value:"9"},{label:"代发失败",value:"10"},{label:"汇款成功",value:"11"},{label:"结单",value:"12"}],g=[{label:"议价订单创建，未支付",value:"-1"},{label:"买家已报价，待卖家回复",value:"0"},{label:"卖家已接受，待付款",value:"1"},{label:"已拒绝，待重新报价或取消",value:"2"},{label:"已取消",value:"3"},{label:"全款订单已付",value:"4"},{label:"卖家拒绝并拉黑",value:"5"},{label:"已退款",value:"6"},{label:"退款中",value:"7"},{label:"议价买家违约",value:"8"}],h={components:{orderDetail:u.default},data:function(){return{backInfo:{note:null,id:null,type:0,returnAmount:null},util:v.b,listQuery:o()({},p),listLoading:!0,list:null,total:null,listCategory:[],negotiationStatusList:g,orderStatusList:b,payTypeList:m,backDialogVisible:!1,showOrderDetail:!1}},created:function(){this.getCate(),this.getList()},methods:{handleViewOrder:function(e,t){this.showOrderDetail=!0,this.orderId=t.orderId},getCate:function(){var e=this;Object(f.d)(74,{pageNum:1,pageSize:999}).then(function(t){200==t.code&&(e.listCategory=t.data.list)})},getOrderName:function(e){return b.find(function(t){return t.value==e}).label},canShow:function(e){var t=this.$store.getters.roles||[];return(t.includes("超级管理员")||t.includes("财务管理员"))&&0!=e.orderStatus&&4!=e.orderStatus&&5!=e.orderStatus&&6!=e.orderStatus&&7!=e.negotiaStatus&&12!=e.orderStatus&&"free"!=e.payChannel},canFreeShow:function(e){var t=this.$store.getters.roles||[];return(t.includes("超级管理员")||t.includes("财务管理员"))&&"free"==e.payChannel&&4!=e.orderStatus},getStatusName:function(e){return i()({"-1":"议价订单创建,未支付",0:"买家已报价，待卖家回复",1:"卖家已接受，待付款",2:"已拒绝，待重新报价或取消",3:"已取消",4:"全款订单已付",5:"卖家拒绝并拉黑",6:"已退款",7:"退款中"},"6","议价买家违约")[e]},handleResetSearch:function(){this.listQuery=o()({},p)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},getList:function(){var e=this;this.listLoading=!0;var t=n()({},this.listQuery);t.time&&(t.startTime=t.time[0],t.endTime=t.time[1]),delete t.time,Object(d.h)(t).then(function(t){e.listLoading=!1,e.list=t.data.list,e.list.forEach(function(e){e.logs&&(e.logs=JSON.parse(e.logs))}),e.total=t.data.total})},onReturn:function(e,t){var l=this;this.$confirm("确认全额退款"+t.payAmount+"元吗？","提示",{cancelButtonText:"取消",confirmButtonText:"确定",type:"warning",distinguishCancelAndClose:!0}).then(function(){var e={returnAmount:t.payAmount,orderId:t.orderId,reason:"议价退款",type:0};Object(c.T)(e).then(function(e){l.backDialogVisible=!1,l.$message({type:"success",message:"申请退款成功!"}),l.getList()})})},onOver:function(e,t){var l=this;this.$confirm("确认结单吗？","提示",{cancelButtonText:"取消",confirmButtonText:"确定",type:"warning",distinguishCancelAndClose:!0}).then(function(){Object(d.j)({id:t.orderId}).then(function(e){200==e.code&&l.$message.success(e.message)})})},onCancel:function(e,t){var l=this;this.$confirm("确认取消议价吗？","提示",{cancelButtonText:"取消",confirmButtonText:"确定",type:"warning",distinguishCancelAndClose:!0}).then(function(){Object(d.g)({negoId:t.negoId}).then(function(e){200==e.code&&l.$message.success("取消成功")})})}}},y={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[l("div",{staticStyle:{"margin-top":"15px"}},[l("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[l("el-form-item",{attrs:{label:"游戏分类："}},[l("el-select",{staticStyle:{width:"248px"},model:{value:e.listQuery.productCategoryId,callback:function(t){e.$set(e.listQuery,"productCategoryId",t)},expression:"listQuery.productCategoryId"}},e._l(e.listCategory,function(e){return l("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"议价状态："}},[l("el-select",{staticClass:"input-width",attrs:{clearable:""},model:{value:e.listQuery.negotiationStatus,callback:function(t){e.$set(e.listQuery,"negotiationStatus",t)},expression:"listQuery.negotiationStatus"}},e._l(e.negotiationStatusList,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"发起议价用户："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"发起议价用户"},model:{value:e.listQuery.buyerUsername,callback:function(t){e.$set(e.listQuery,"buyerUsername",t)},expression:"listQuery.buyerUsername"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"接受议价用户："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"接受议价用户"},model:{value:e.listQuery.sellerUsername,callback:function(t){e.$set(e.listQuery,"sellerUsername",t)},expression:"listQuery.sellerUsername"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"商品编号："}},[l("el-input",{staticClass:"input-width",attrs:{placeholder:"商品编号"},model:{value:e.listQuery.productSn,callback:function(t){e.$set(e.listQuery,"productSn",t)},expression:"listQuery.productSn"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"付款方式："}},[l("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.payType,callback:function(t){e.$set(e.listQuery,"payType",t)},expression:"listQuery.payType"}},e._l(e.payTypeList,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"订单状态："}},[l("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.orderStatus,callback:function(t){e.$set(e.listQuery,"orderStatus",t)},expression:"listQuery.orderStatus"}},e._l(e.orderStatusList,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"议价创建时间："}},[l("el-date-picker",{attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.listQuery.time,callback:function(t){e.$set(e.listQuery,"time",t)},expression:"listQuery.time"}})],1),e._v(" "),l("el-form-item",[l("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询搜索\n          ")]),e._v(" "),l("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),l("div",{staticClass:"table-container"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"orderTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[l("el-table-column",{attrs:{fixed:"",label:"商品编号",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.productSn))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"游戏名称",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.productCategoryName))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"发起议价用户",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.buyerUsername))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"接受议价用户",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.sellerUsername))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"原金额（元）",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("￥"+e._s(t.row.originPrice))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"议价金额（元）",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("￥"+e._s(t.row.offerPrice))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"支付金额（元）",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.payAmount))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"议价状态",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.getStatusName(t.row.negotiaStatus)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"订单状态",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.getOrderName(t.row.orderStatus)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"是否还价",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.sellerOfferPrice?l("el-tag",{attrs:{type:"success"}},[e._v("是")]):l("el-tag",{attrs:{type:"warning"}},[e._v("否")])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"支付平台订单编号",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.accpTxno))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"议价时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.util.timeFormat(t.row.createTime)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"支付时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.util.timeFormat(t.row.paymentTime)))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"退款金额（元）",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.returnAmount))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"退款原因",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.returnType))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"议价日志",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-popover",{attrs:{placement:"top-start",title:"议价日志",width:"400",trigger:"hover"}},[e._l(t.row.logs,function(t){return l("div",{staticClass:"logs"},[l("div",[e._v(e._s(t))])])}),e._v(" "),l("div",{attrs:{slot:"reference"},slot:"reference"},[e._v("议价日志")])],2),e._v(" "),l("el-pro")]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"退款操作人",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.returnOperator))]}}])}),e._v(" "),l("el-table-column",{attrs:{label:"退款时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.util.timeFormat(t.row.returnTime)))]}}])}),e._v(" "),l("el-table-column",{attrs:{width:"300",label:"操作",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{attrs:{size:"mini"},on:{click:function(l){return e.handleViewOrder(t.$index,t.row)}}},[e._v("查看订单")]),e._v(" "),e.canShow(t.row)?l("el-button",{attrs:{size:"mini"},on:{click:function(l){return e.onReturn(t.$index,t.row)}}},[e._v("退款")]):e._e(),e._v(" "),e.canShow(t.row)?l("el-button",{attrs:{size:"mini"},on:{click:function(l){return e.onOver(t.$index,t.row)}}},[e._v("结单")]):e._e(),e._v(" "),e.canFreeShow(t.row)?l("el-button",{attrs:{size:"mini"},on:{click:function(l){return e.onCancel(t.$index,t.row)}}},[e._v("取消议价")]):e._e()]}}])})],1)],1),e._v(" "),l("div",{staticClass:"pagination-container"},[l("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)}}})],1),e._v(" "),l("el-dialog",{attrs:{width:"70%",visible:e.showOrderDetail},on:{"update:visible":function(t){e.showOrderDetail=t}}},[e.showOrderDetail?l("orderDetail",{attrs:{hideBtn:"1",id:e.orderId}}):e._e(),e._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.showOrderDetail=!1}}},[e._v("取 消")]),e._v(" "),l("el-button",{attrs:{type:"primary"},on:{click:function(t){e.showOrderDetail=!1}}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var _=l("VU/8")(h,y,!1,function(e){l("Adki")},"data-v-2c04c357",null);t.default=_.exports}});