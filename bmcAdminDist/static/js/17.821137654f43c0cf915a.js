webpackJsonp([17],{"+6Bu":function(t,e,i){"use strict";e.__esModule=!0,e.default=function(t,e){var i={};for(var a in t)e.indexOf(a)>=0||Object.prototype.hasOwnProperty.call(t,a)&&(i[a]=t[a]);return i}},"1xSC":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=i("fZjL"),s=i.n(a),n=i("Xxa5"),r=i.n(n),l=i("lHA8"),o=i.n(l),c=i("exGp"),u=i.n(c),p=i("+6Bu"),m=i.n(p),h=i("Dd8w"),d=i.n(h),v=i("mvHQ"),f=i.n(v),g=i("d7EF"),y=i.n(g),b=i("Gu7T"),x=i.n(b),S=i("Lfj9"),k=i("ANBZ"),T=i("PJh5"),C=i.n(T),V=i("zBuL"),N=i("/ekJ"),L=i.n(N),I=(i("mERs"),i("/dO2")),w={components:{CheckBoxList:k.a,InputNumber:V.a},data:function(){return{excludeBmc:"0",startTimeUnit:"小时",startTimeUnitList:["分钟","小时","天"],startTimeNumber:"",list:null,total:null,startTime:"",endTime:"",listLoading:!0,tabPosition:"chat",dialogFormVisible:!1,ruleForm:{tagName:"",tagType:0,description:"",sort:0,isActive:0,isUnique:0,isAutoboot:0},rules:{name:[{required:!0,message:"请输入标签名称",trigger:"blur"}],platform:[{required:!0,message:"请选择平台",trigger:"change"}]},tagVisible:!1,listQuery:{pageNum:1,pageSize:20},parentId:74,keyword2:"",keyword:"",productCategoryId:"",checkBoxAttributeList:[],checkBoxAttrGroup:[],optsSearchResult:[],inputAttributeList:[],isExpand:!1,tagDetail:{},searchTotal:"",isUniqueFlag:0,cateList:[],sortableInstance:null,comprehensiveData:[]}},computed:{newCheckBoxAttrGroup:function(){return this.checkBoxAttrGroup}},mounted:function(){this.productCategoryId=this.$route.query.id,this.getList()},methods:{initDragSort:function(){var t=this,e=document.querySelectorAll(".sortItemBox")[0];this.sortableInstance=I.a.create(e,{onEnd:function(e){var i=e.oldIndex,a=e.newIndex,s=[].concat(x()(t.comprehensiveData)),n=s.splice(i,1),r=y()(n,1)[0];s.splice(a,0,r),t.comprehensiveData=JSON.parse(f()(s)),t.$forceUpdate()}})},sortChos:function(t){var e=this;this.comprehensiveData.forEach(function(i,a){if(i.sortName===t.sortName){var s=""===t.sort?"desc":"desc"===t.sort?"asc":"";e.$set(i,"sort",s)}}),this.$forceUpdate()},formatClasData:function(){var t=this;if(this.comprehensiveData&&this.comprehensiveData.length){var e=[];e.push({sortName:"上架时间",sort:"",order:"publishTime",sortId:0}),e.push({sortName:"价格",sort:"",order:"price",sortId:1});var i=2;return this.cateList.forEach(function(t){var a=_.cloneDeep(t);if(a.searchSort){var s={sortName:""+a.name,sort:"",order:a.searchSort,sortId:i};e.push(s),i++}}),void e.forEach(function(e){if(!t.comprehensiveData.some(function(t){return t.name===e.name})){var i=Date.now()+Math.floor(1e4*Math.random());t.comprehensiveData.push(d()({},e,{sortId:i}))}})}this.comprehensiveData=[],this.comprehensiveData.push({sortName:"上架时间",sort:"",order:"publishTime",sortId:0}),this.comprehensiveData.push({sortName:"价格",sort:"",order:"price",sortId:1});var a=2;this.cateList.forEach(function(e){var i=_.cloneDeep(e);if(i.searchSort){var s={sortName:""+i.name,sort:"",order:i.searchSort,sortId:a};t.comprehensiveData.push(s),a++}})},tabClick:function(t){this.tabPosition=t,"highLevel"==t&&(this.initJsonEdit(),this.editor&&this.editor.set(this.tagDetail))},initJsonEdit:function(){var t=document.getElementById("jsoneditor");this.editor=new L.a(t,{mode:"text",indentation:2,enableSort:!1,enableTransform:!1})},searchTagType:function(t){var e="";switch(t){case 0:e="H5";break;case 1:e="PC";break;case 2:e="H5和PC";break;default:e=""}return e},getList:function(){var t=this;this.listLoading=!0,Object(S.F)(this.productCategoryId,this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})},tagSubmit:function(){var t=this;this.$refs.ruleForm.validate(function(e){if(!e)return console.log("error submit!!"),!1;var i=d()({},t.ruleForm,{categoryId:t.productCategoryId});i.id?Object(S._29)(i.id,i).then(function(e){200==e.code&&(t.$message.success("修改成功"),t.tagVisible=!1,t.getList())}):Object(S.a)(i).then(function(e){200==e.code&&(t.$message.success("新增成功"),t.tagVisible=!1,t.getList())})})},handleDelete:function(t,e){var i=this;this.$confirm("是否要删除该标签","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(S.l)(e.id).then(function(t){200==t.code&&(i.$message.success("删除成功"),i.getList())})})},handleUpdate:function(t,e){this.ruleForm=JSON.parse(f()(e)),this.tagVisible=!0},createConfig:function(){this.ruleForm={tagName:"",tagType:0,description:"",sort:0,isActive:0,isUnique:0,isAutoboot:0},this.tagVisible=!0},handleConfigEdit:function(t,e){var i=this;this.isUniqueFlag=e.isUnique,this.tabPosition="chat",this.keyword="",this.searchTotal="",this.checkBoxAttributeList=[],this.inputAttributeList=[],this.checkBoxAttrGroup=[],this.dialogFormVisible=!0,this.ruleForm=JSON.parse(f()(e)),setTimeout(function(){i.getProductAttributeList()})},submitForm:function(){var t=this,e={};if("chat"==this.tabPosition){var i=void 0;this.inputAttributeList.map(function(t){var e=t.selectValue||[],a=y()(e,2),s=a[0],n=a[1];"价格"!==t.name||isNaN(s)&&isNaN(n)||(i=[{min:isNaN(s)?void 0:parseInt(s,10),key:"price",max:isNaN(n)?void 0:parseInt(n,10)}])}),e={keyword:this.keyword,excludeBmc:this.excludeBmc||"",startTimeNumber:this.startTimeNumber||"",startTimeUnit:this.startTimeUnit||"",startTime:this.startTime?C()(this.startTime).format("YYYY-MM-DD HH:mm:ss"):"",endTime:this.endTime?C()(this.endTime).format("YYYY-MM-DD HH:mm:ss"):"",queryIntParams:i,queryOrderSortParams:this.comprehensiveData,attrValueList:this.checkBoxAttributeList.concat(this.inputAttributeList).filter(function(t){return t.selectValue.length>0&&t.selectValue.some(function(t){return""!==t})}).map(function(t){t.inputList,t.optionList;var e=m()(t,["inputList","optionList"]);return d()({},e,{value:e.selectValue.join()})})}}if("highLevel"==this.tabPosition){var a=this.editor.get(),s=void 0;console.log(22222),a.attrValueList.filter(function(t){var e=t.selectValue||[],i=y()(e,2),a=i[0],n=i[1];return"价格"!==t.name||isNaN(a)&&isNaN(n)||(s=[{min:isNaN(a)?void 0:parseInt(a,10),key:"price",max:isNaN(n)?void 0:parseInt(n,10)}]),t.selectValue&&t.selectValue.length>0&&t.selectValue.some(function(t){return""!==t})}).map(function(t){t.inputList,t.optionList;var e=m()(t,["inputList","optionList"]);return d()({},e,{value:e.selectValue.join()})}),console.log(3333333),a.queryIntParams=s,e=a}if(0==this.isUniqueFlag&&e.attrValueList.length>1)this.$message.error("不是唯一只能选择一个属性");else{var n=d()({},this.ruleForm,{categoryId:this.productCategoryId,searchConditions:f()(e),keyword:this.keyword,excludeBmc:this.excludeBmc||"",startTimeNumber:this.startTimeNumber||"",startTimeUnit:this.startTimeUnit||"",startTime:this.startTime?C()(this.startTime).format("YYYY-MM-DD HH:mm:ss"):"",endTime:this.endTime?C()(this.endTime).format("YYYY-MM-DD HH:mm:ss"):""});Object(S._29)(n.id,n).then(function(e){200==e.code&&(t.$message.success("配置成功"),t.dialogFormVisible=!1,t.getList())})}},testListClick:function(){var t=this,e={productCategoryId:this.productCategoryId,pageNum:1,pageSize:10,memberId:278770},i=void 0,a=this.inputAttributeList.map(function(t){var e=t.selectValue||[],a=y()(e,2),s=a[0],n=a[1];return"价格"!==t.name||isNaN(s)&&isNaN(n)||(i=[{min:isNaN(s)?void 0:parseInt(s,10),key:"price",max:isNaN(n)?void 0:parseInt(n,10)}]),d()({},t,{value:isNaN(s)&&isNaN(n)?"":(s||0)+"-"+(n||9999999)})}).filter(function(t){return"价格"!==t.name}),s={keyword:this.keyword,excludeBmc:this.excludeBmc,startTimeNumber:this.startTimeNumber,startTimeUnit:this.startTimeUnit,queryIntParams:i,startTime:this.startTime?C()(this.startTime).format("YYYY-MM-DD HH:mm:ss"):"",endTime:this.endTime?C()(this.endTime).format("YYYY-MM-DD HH:mm:ss"):"",order:"",queryStrParams:[],sort:"",attrValueList:[],queryOrderSortParams:this.comprehensiveData};if("chat"==this.tabPosition&&(s.attrValueList=this.checkBoxAttributeList.concat(a).filter(function(t){return t.selectValue.length>0&&t.selectValue.some(function(t){return""!==t})&&"价格"!=t.name}).map(function(t){t.inputList,t.optionList;var e=m()(t,["inputList","optionList"]);return d()({},e,{value:e.value?e.value:e.selectValue.join()})})),"highLevel"==this.tabPosition){var n=this.editor.get(),r=void 0,l=n.attrValueList.map(function(t){var e=t.selectValue||[],i=y()(e,2),a=i[0],s=i[1];return"价格"!==t.name||isNaN(a)&&isNaN(s)||(r=[{min:isNaN(a)?void 0:parseInt(a,10),key:"price",max:isNaN(s)?void 0:parseInt(s,10)}]),d()({},t,{value:isNaN(a)&&isNaN(s)?t.value?t.value:"":(a||0)+"-"+(s||9999999)})}).filter(function(t){return"价格"!==t.name});s={keyword:n.keyword,queryIntParams:r,order:"",queryStrParams:[],sort:"",attrValueList:l}}this.searchTotal="",Object(S._13)(e,s).then(function(e){e.code&&(t.searchTotal=e.data.total)})},handelOptsSearch:function(t){var e=[];t&&this.checkBoxAttributeList.forEach(function(i){if(3===i.selectType){var a=function(a){i.optionList[a].forEach(function(s){s.includes(t)&&e.push({name:i.name,parent:a,value:s})})};for(var s in i.optionList)a(s)}else e=e.concat(i.optionList.filter(function(e){return e.includes(t)}).map(function(t){return{name:i.name,value:t}}))}),this.optsSearchResult=e},handelCheckboxAttrChange:function(t,e){var i=e.name,a=!1,s="";if(this.checkBoxAttributeList.concat(this.inputAttributeList).forEach(function(t){t.selectValue&&t.selectValue.length&&t.selectValue.some(function(t){return""!==t})&&(a=!0,s=t.name)}),0==this.isUniqueFlag&&a&&s!==i)this.$message.error("不是唯一只能选择一个属性");else{var n=this.checkBoxAttributeList.findIndex(function(t){return t.name===i});this.$set(this.checkBoxAttributeList[n],"selectValue",t)}},searchTypeClick:function(t,e){var i=this.checkBoxAttributeList.findIndex(function(e){return e.name===t.name});this.$set(this.checkBoxAttributeList[i],"valueSearchType",e)},getOptIsCheck:function(t){var e=t.name,i=t.ename,a=t.selectType,s=t.parent,n=t.value;return"gameAccountQufu"===i&&3===a&&(n=s+"|"+n),this.selectValueList().find(function(t){return t.value===n&&t.name===e})},selectValueList:function(){var t=[];return this.checkBoxAttributeList.forEach(function(e){var i=e.selectValue.map(function(t){return{name:e.name,value:t,parent:"gameAccountQufu"===e.ename&&3===e.selectType?t.split("|")[0]:""}});t=t.concat(i)}),t},selectNumValueList:function(){var t=[];return this.inputAttributeList.forEach(function(e){var i=JSON.parse(f()(e.selectValue));if(i.length&&!i.every(function(t){return""===t})){""!==i[0]&&null!==i[0]||(i[0]=0),""!==i[1]&&null!==i[1]||(i[1]=9999999);var a={name:e.name,value:e.name+i.join("-")};t=t.concat(a)}}),t},handelOptNumClick:function(t){var e=t.name,i=this.inputAttributeList.findIndex(function(t){return t.name===e});this.$set(this.inputAttributeList[i],"selectValue",[])},handelInputAttrChange:function(t,e){var i=!1,a="";this.checkBoxAttributeList.concat(this.inputAttributeList).forEach(function(t){t.selectValue&&t.selectValue.length&&t.selectValue.some(function(t){return""!==t})&&(i=!0,a=t.name)}),0==this.isUniqueFlag&&i&&a!==this.inputAttributeList[t].name?this.$message.error("不是唯一只能选择一个属性"):this.$set(this.inputAttributeList[t],"selectValue",e)},handelOptClick:function(t){var e=t.name,i=t.parent,a=t.value,s=t.selectType,n=t.ename,r=this.checkBoxAttributeList.findIndex(function(t){return t.name===e}),l=this.checkBoxAttributeList[r]||{},o=l.selectValue,c=l.filterType,u=o;"gameAccountQufu"!==n||3!==s||a.includes("|")||(a=i+"|"+a),1!==c?u=o.includes(a)?[]:[a]:o.includes(a)?u=o.filter(function(t){return t!==a}):u.push(a),this.$set(this.checkBoxAttributeList[r],"selectValue",u),this.$forceUpdate()},getProductAttributeList:function(){var t=this;return u()(r.a.mark(function e(){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(S.m)(t.ruleForm.id).then(function(e){200===e.code&&(e.data.searchConditions?(t.tagDetail=JSON.parse(e.data.searchConditions),t.tagDetail.queryOrderSortParams&&t.tagDetail.queryOrderSortParams.length&&(t.comprehensiveData=t.tagDetail.queryOrderSortParams)):t.tagDetail={})});case 2:Object(S.B)(t.productCategoryId).then(function(e){if(e.code=200){var i=e.data.filter(function(t){return 0!==t.type&&0!==t.searchType&&!1!==JSON.parse(t.custom||"{}").showSearch}).sort(function(t,e){return e.sort-t.sort}),a=[];t.checkBoxAttributeList=i.filter(function(t){return 0!==t.inputType}).map(function(e){var i=e.inputList,s=e.selectType,n=e.name,r=e.searchType,l=e.nameGroup,o=e.custom,c=(e.selectValue,l||n);a.push(c);var u=i.split(","),p=null;if(3===s){u={};var m=JSON.parse(i);p=m.map(function(t){return t.parent_name}),m.forEach(function(t){var e=t.parent_name,i=t.childList;u[e]=i})}var h=t.getTagDetailSelectValue(e.name)||[];if("商品类型"===n){var v=new URLSearchParams(window.location.search);if(v.has("goodsType"))h=u.includes(v.get("goodsType"))?[v.get("goodsType")]:[];else if(o&&"{}"!==o){var f=JSON.parse(o);h=u.includes(f.sdefault)?[f.sdefault]:[]}else h=[u[0]]}return d()({},e,{selectValue:h,childValue:{},hasClear:"商品类型"!=n&&3!==r,optionList:u,pOptionList:p,defaultValue:h,nameGroup:c,valueSearchType:t.getTagDetailValueSearchType(e.name)})}),t.checkBoxAttrGroup=[].concat(x()(new o.a(a)));var s=i.filter(function(t){return 0===t.inputType}).map(function(e){return d()({},e,{selectValue:t.getTagDetailSelectValue(e.name)||[],defaultValue:[]})});t.inputAttributeList=[{name:"价格",selectType:0,inputType:0,inputList:"",sort:0,filterType:0,searchType:2,type:1,searchSort:0,selectValue:t.getTagDetailSelectValue("价格")||[],defaultValue:[]}].concat(s);var n=[];e.data.forEach(function(t){0!==t.type&&0!==t.searchType&&(t.moreTxt="展开全部",n.push(t))}),t.cateList=n,t.formatClasData(),t.initDragSort()}});case 3:case"end":return e.stop()}},e,t)}))()},getTagDetailSelectValue:function(t){var e=[];return this.tagDetail&&s()(this.tagDetail).length>0&&(this.keyword=this.tagDetail.keyword,this.excludeBmc=this.tagDetail.excludeBmc,this.startTimeNumber=this.tagDetail.startTimeNumber,this.startTimeUnit=this.tagDetail.startTimeUnit,this.startTime=this.tagDetail.startTime,this.endTime=this.tagDetail.endTime,this.tagDetail.attrValueList.forEach(function(i){i.name==t&&(e=i.selectValue)})),e},getTagDetailValueSearchType:function(t){var e="must";return this.tagDetail&&s()(this.tagDetail).length>0&&(this.keyword=this.tagDetail.keyword,this.excludeBmc=this.tagDetail.excludeBmc,this.startTimeNumber=this.tagDetail.startTimeNumber,this.startTimeUnit=this.tagDetail.startTimeUnit,this.startTime=this.tagDetail.startTime,this.endTime=this.tagDetail.endTime,this.tagDetail.attrValueList.forEach(function(i){i.name==t&&i.valueSearchType&&(e=i.valueSearchType)})),e}}},D={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{padding:"0px 20px 20px 20px"}},[i("el-button",{staticClass:"add-help",attrs:{type:"primary"},on:{click:t.createConfig}},[t._v("新增筛选标签")]),t._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productCateTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[i("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"标签名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.tagName))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"是否生效",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.isActive?"是":"否"))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"是否蹲号",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.isAutoboot?"是":"否"))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"标签说明",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.description))]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"操作",width:"260",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{size:"mini"},on:{click:function(i){return t.handleConfigEdit(e.$index,e.row)}}},[t._v("搜索配置\n        ")]),t._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:function(i){return t.handleUpdate(e.$index,e.row)}}},[t._v("编辑\n        ")]),t._v(" "),i("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(i){return t.handleDelete(e.$index,e.row)}}},[t._v("删除\n        ")])]}}])})],1),t._v(" "),i("el-dialog",{attrs:{title:"新增标签",width:"600px",visible:t.tagVisible},on:{"update:visible":function(e){t.tagVisible=e}}},[i("div",[i("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"标签名称",prop:"tagName"}},[i("el-input",{model:{value:t.ruleForm.tagName,callback:function(e){t.$set(t.ruleForm,"tagName",e)},expression:"ruleForm.tagName"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"是否生效"}},[i("el-radio-group",{model:{value:t.ruleForm.isActive,callback:function(e){t.$set(t.ruleForm,"isActive",e)},expression:"ruleForm.isActive"}},[i("el-radio",{attrs:{label:0}},[t._v("否")]),t._v(" "),i("el-radio",{attrs:{label:1}},[t._v("是")])],1)],1),t._v(" "),i("el-form-item",{attrs:{label:"是否蹲号"}},[i("el-radio-group",{model:{value:t.ruleForm.isAutoboot,callback:function(e){t.$set(t.ruleForm,"isAutoboot",e)},expression:"ruleForm.isAutoboot"}},[i("el-radio",{attrs:{label:0}},[t._v("否")]),t._v(" "),i("el-radio",{attrs:{label:1}},[t._v("是")])],1),t._v(" "),i("span",{staticStyle:{color:"#9a9a9a"}},[t._v("（新上架商品如果有匹配，将自动转化为回收线索）")])],1),t._v(" "),i("el-form-item",{attrs:{label:"标签说明"}},[i("el-input",{attrs:{type:"textarea"},model:{value:t.ruleForm.description,callback:function(e){t.$set(t.ruleForm,"description",e)},expression:"ruleForm.description"}})],1)],1)],1),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.tagVisible=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:t.tagSubmit}},[t._v("确 定")])],1)]),t._v(" "),i("el-dialog",{staticStyle:{top:"-120px"},attrs:{title:"搜索配置",width:"1200px",visible:t.dialogFormVisible},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[i("el-radio-group",{on:{change:t.tabClick},model:{value:t.tabPosition,callback:function(e){t.tabPosition=e},expression:"tabPosition"}},[i("el-radio-button",{attrs:{label:"chat"}},[t._v("可视化模式")]),t._v(" "),i("el-radio-button",{attrs:{label:"highLevel"}},[t._v("高级模式")])],1),t._v(" "),i("div",{staticStyle:{"margin-top":"10px","margin-bottom":"20px"}},[t._v("\n      当前搜索唯一性："+t._s(1==t.isUniqueFlag?"是":"否")+"（是：可以选中多个属性,前台标签互斥，\n      否：只能选择一个属性，前台标签搜索不互斥）\n    ")]),t._v(" "),"highLevel"==t.tabPosition?i("div",[i("div",{staticStyle:{width:"1160px",height:"500px"},attrs:{id:"jsoneditor"}})]):t._e(),t._v(" "),"chat"==t.tabPosition?i("div",{staticStyle:{width:"100%",height:"500px",overflow:"auto"}},[i("el-input",{staticStyle:{width:"321px","margin-bottom":"24px"},attrs:{placeholder:"请输入内容","prefix-icon":"el-icon-search",clearable:""},on:{input:t.handelOptsSearch},model:{value:t.keyword2,callback:function(e){t.keyword2=e},expression:"keyword2"}}),t._v(" "),!t.keyword2||t.optsSearchResult&&t.optsSearchResult.length?t._e():i("div",{staticStyle:{color:"rgba(0, 0, 0, 0.4)","font-family":"PingFang SC","font-size":"14px","font-style":"normal","font-weight":"400","line-height":"normal","letter-spacing":"0.56px"}},[t._v("\n        暂无符合条件的筛选项\n      ")]),t._v(" "),i("div",{staticStyle:{display:"flex","align-items":"center","flex-wrap":"wrap"}},t._l(t.optsSearchResult,function(e){return i("div",{key:e.value,staticClass:"opt-item spaceAround",class:t.getOptIsCheck(e)?"active":"",on:{click:function(i){return t.handelOptClick(e)}}},[t._v("\n          "+t._s(e.value)+"\n        ")])}),0),t._v(" "),t._l(t.newCheckBoxAttrGroup,function(e,a){return i("CheckBoxList",{key:e,attrs:{"group-name":e,index:a,list:t.checkBoxAttributeList.filter(function(t){return t.nameGroup===e}).map(function(t){return Object.assign({},t,{valueSearchType:t.valueSearchType||"must",valueSearchTypeValue:"must"!=t.valueSearchType?parseInt(t.valueSearchType.split("_")[1])||0:"",maxSholdValue:t.selectValue?t.selectValue.length:1})})},on:{change:t.handelCheckboxAttrChange,searchTypeClick:t.searchTypeClick}})}),t._v(" "),i("div",{staticStyle:{"margin-top":"10px"}},[i("div",{staticClass:"spaceStart flexWrap sxbox"},t._l(t.inputAttributeList,function(e,a){return i("InputNumber",{key:a,attrs:{item:e},on:{change:function(e,i){return t.handelInputAttrChange(a,e,i)}}})}),1),t._v(" "),i("div",{staticClass:"keyword_box"},[i("div",{staticClass:"spaceBetween"},[i("div",{staticClass:"spaceStart"},[i("div",{staticClass:"playSearch_tit"},[t._v("关键词")]),t._v(" "),i("el-input",{staticClass:"search_keyword",attrs:{placeholder:"请输入您要查找账号/关键词"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchListFun.apply(null,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)])])]),t._v(" "),i("div",{staticStyle:{"border-top":"dashed 1px #dcdcdc","padding-top":"10px","margin-top":"-10px"}},[i("div",{staticClass:"search_dzTitle"},[t._v("排序条件（可拖拽排序）")]),t._v(" "),i("div",{staticClass:"spaceStart sortItemBox",staticStyle:{"flex-wrap":"wrap","margin-bottom":"10px"}},t._l(t.comprehensiveData,function(e,a){return i("div",{key:e.sortId,staticClass:"spaceStart sort_item",class:""!=e.sort?"active":"",on:{click:function(i){return t.sortChos(e)}}},[i("div",[t._v(t._s(e.sortName))]),t._v(" "),""==e.sort&&""!=e.value?i("IconFont",{staticStyle:{margin:"0 0 0 4px"},attrs:{size:15,icon:"sort"}}):t._e(),t._v(" "),"asc"==e.sort&&""!=e.value?i("IconFont",{staticStyle:{margin:"0 0 0 4px"},attrs:{size:15,icon:"asc"}}):t._e(),t._v(" "),"desc"==e.sort&&""!=e.value?i("IconFont",{staticStyle:{margin:"0 0 0 4px"},attrs:{size:15,icon:"desc"}}):t._e()],1)}),0)]),t._v(" "),i("div",{staticStyle:{"border-top":"dashed 1px #dcdcdc","padding-top":"10px"}},[i("div",{staticClass:"search_dzTitle"},[t._v("其他条件")]),t._v(" "),i("div",{staticClass:"spaceStart",staticStyle:{"margin-bottom":"20px"}},[i("div",{staticClass:"labelTitle"},[t._v("是否排除号商")]),t._v(" "),i("el-radio",{attrs:{label:"1"},model:{value:t.excludeBmc,callback:function(e){t.excludeBmc=e},expression:"excludeBmc"}},[t._v("是")]),t._v(" "),i("el-radio",{attrs:{label:"0"},model:{value:t.excludeBmc,callback:function(e){t.excludeBmc=e},expression:"excludeBmc"}},[t._v("否")]),t._v(" "),i("div",{staticClass:"labelTitle",staticStyle:{width:"120px","margin-left":"20px"}},[t._v("\n            筛选时间范围：\n          ")]),t._v(" "),i("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","default-time":"00:00:00"},model:{value:t.startTime,callback:function(e){t.startTime=e},expression:"startTime"}}),t._v(" "),i("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","default-time":"23:59:59"},model:{value:t.endTime,callback:function(e){t.endTime=e},expression:"endTime"}}),t._v(" "),i("div",{staticClass:"labelTitle",staticStyle:{width:"85px","margin-left":"20px"}},[t._v("\n            快捷时间：\n          ")]),t._v(" "),i("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入",oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:t.startTimeNumber,callback:function(e){t.startTimeNumber=e},expression:"startTimeNumber"}},[i("template",{slot:"append"},[i("el-select",{staticStyle:{width:"80px"},attrs:{placeholder:"请选择"},model:{value:t.startTimeUnit,callback:function(e){t.startTimeUnit=e},expression:"startTimeUnit"}},t._l(t.startTimeUnitList,function(t){return i("el-option",{key:t,attrs:{label:t,value:t}})}),1)],1)],2)],1)])],2):t._e(),t._v(" "),(t.selectValueList().length||t.selectNumValueList().length)&&"chat"==t.tabPosition?i("div",{staticClass:"spaceStart",staticStyle:{"align-items":"baseline","border-bottom":"0.5px solid #ff7a00","margin-bottom":"0px","padding-bottom":"20px","margin-top":"20px","max-height":"200px","overflow-y":"auto"}},[i("span",{staticClass:"playSearch_tit",staticStyle:{"font-weight":"600"}},[t._v("您已选择：")]),t._v(" "),i("div",{staticClass:"spaceStart flexWrap",staticStyle:{flex:"1"}},[t._l(t.selectValueList(),function(e){return i("span",{key:e.value,staticClass:"opt-item",on:{click:function(i){return t.handelOptClick(e)}}},[t._v("\n          "+t._s(e.value)+" "),i("i",{staticClass:"el-icon-close",staticStyle:{"font-size":"14px",cursor:"pointer"}})])}),t._v(" "),t._l(t.selectNumValueList(),function(e){return i("span",{key:e.value,staticClass:"opt-item",on:{click:function(i){return t.handelOptNumClick(e)}}},[t._v("\n          "+t._s(e.value)+" "),i("i",{staticClass:"el-icon-close",staticStyle:{"font-size":"14px",cursor:"pointer"}})])})],2)]):t._e(),t._v(" "),i("div",{staticClass:"config-footer",staticStyle:{position:"relative"},attrs:{slot:"footer"},slot:"footer"},[""!==t.searchTotal?i("div",{staticStyle:{position:"absolute",top:"10px"}},[t._v("\n        测试查询到"+t._s(t.searchTotal||0)+"条\n      ")]):t._e(),t._v(" "),i("el-button",{on:{click:t.testListClick}},[t._v("搜索测试")]),t._v(" "),i("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var A=i("VU/8")(w,D,!1,function(t){i("P5WN")},"data-v-90e6aa88",null);e.default=A.exports},ANBZ:function(t,e,i){"use strict";var a=i("mvHQ"),s=i.n(a),n={props:{groupName:{type:String,default:""},list:{type:Array,default:function(){return[]}}},data:function(){return{isExpand:!1,isSubExpand:!1,sholdValue:1,maxSholdValue:1,parentValue:"",attrName:""}},computed:{currentItem:function(){var t=this;return this.list.filter(function(e){return e.name===t.attrName})[0]||{}},serverOptions:function(){return this.currentItem.optionList[this.parentValue]||[]}},mounted:function(){var t=this.list[0].name;this.attrName=t},methods:{clearCurrentItem:function(t){console.log(t),this.$emit("change",[],this.currentItem)},allCurrentItem:function(t){console.log(t),this.$emit("change",this.currentItem.optionList,this.currentItem)},getValueSearchType:function(t){if(t){var e=parseInt(t.split("_")[1]);console.log(e,33332222),this.sholdValue=e}},getCurrentItemNum:function(t){var e=this.list.filter(function(e){return e.name===t})[0]||{};console.log(t,e,"dui");var i=0;return e&&e.selectValue&&e.selectValue.length&&(i=e.selectValue.length),console.log(e,i),i},isType:function(t){var e=!0;return t.forEach(function(t){2!=t.selectType&&(e=!1)}),e},getSearchType:function(t){return t[0].valueSearchType},searchTypeClick:function(t,e){this.$emit("searchTypeClick",t,"must"==e?"should_1":"must")},handleChange:function(t,e,i){console.log(i,11111),this.$emit("searchTypeClick",t,"should_"+i)},handelParentClick:function(t){this.parentValue=t,this.choose(t)},handelAttrClick:function(t){this.attrName=t},toggel:function(){this.isExpand=!this.isExpand},toggelSub:function(){this.isSubExpand=!this.isSubExpand},clearParentValue:function(){},clearValue:function(t){"服务器"!==t?(this.parentValue="",this.currentItem.selectValue&&this.currentItem.selectValue.length&&this.$emit("change",[],this.currentItem)):this.$emit("change",[this.parentValue],this.currentItem)},choose:function(t){var e=this.currentItem.selectValue||[],i=JSON.parse(s()(e)),a=this.currentItem,n=a.filterType,r=a.selectType;if("gameAccountQufu"===a.ename&&3===r&&this.parentValue!==t&&(t=this.parentValue+"|"+t),1!==n?i=e.includes(t)?[]:[t]:e.includes(t)?i=e.filter(function(e){return e!==t}):i.push(t),i.length){var l=parseInt(i.length);this.maxSholdValue=l,console.log(this.maxSholdValue,"执行了")}this.$emit("change",i,this.currentItem)}}},r={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.currentItem&&"gameAccountQufu"===t.currentItem.ename&&3===t.currentItem.selectType?[i("div",{staticClass:"spaceStart playSearch_wrap",class:t.isExpand?"expand":""},[i("div",{staticClass:"playSearch_tit"},[t._v(t._s(t.groupName))]),t._v(" "),i("div",{staticStyle:{flex:"1"}},[i("div",{staticClass:"spaceStart item-box"},[i("div",{staticClass:"spaceStart flexWrap item-list",staticStyle:{flex:"1"}},[i("div",{staticClass:"playSearch_item",class:t.parentValue?"":"active",on:{click:function(e){return e.stopPropagation(),t.clearValue.apply(null,arguments)}}},[t._v("\n              不限\n            ")]),t._v(" "),t._l(t.currentItem.pOptionList,function(e,a){return i("div",{key:a,staticClass:"playSearch_item",class:t.parentValue===e?"active":"",on:{click:function(i){return i.stopPropagation(),t.handelParentClick(e)}}},[t._v("\n              "+t._s(e)+"\n            ")])})],2),t._v(" "),t.currentItem.pOptionList.length>8?i("div",{staticClass:"more_btn",on:{click:t.toggel}},[t._v("\n            "+t._s(t.isExpand?"收起":"更多")+"\n            "),i("i",{class:t.isExpand?"el-icon-arrow-up":"el-icon-arrow-down",staticStyle:{"font-size":"12px",cursor:"pointer"}})]):t._e()])])]),t._v(" "),i("div",{staticClass:"spaceStart playSearch_wrap",class:t.isSubExpand?"expand":""},[i("div",{staticClass:"playSearch_tit"},[t._v("服务器")]),t._v(" "),i("div",{staticStyle:{flex:"1"}},[i("div",{staticClass:"spaceStart item-box"},[i("div",{staticClass:"spaceStart flexWrap item-list",staticStyle:{flex:"1"}},[i("div",{staticClass:"playSearch_item",class:t.currentItem.selectValue&&t.currentItem.selectValue.find(function(t){return t.includes("|")})?"":"active",on:{click:function(e){return e.stopPropagation(),t.clearValue("服务器")}}},[t._v("\n              不限\n            ")]),t._v(" "),t._l(t.serverOptions,function(e,a){return i("div",{key:a,staticClass:"playSearch_item",class:t.currentItem.selectValue.includes(t.parentValue+"|"+e)?"active":"",on:{click:function(i){return i.stopPropagation(),t.choose(e)}}},[t._v("\n              "+t._s(e)+"\n            ")])})],2),t._v(" "),t.serverOptions.length>8?i("div",{staticClass:"more_btn",on:{click:t.toggelSub}},[t._v("\n            "+t._s(t.isSubExpand?"收起":"更多")+"\n            "),i("i",{class:t.isSubExpand?"el-icon-arrow-up":"el-icon-arrow-down",staticStyle:{"font-size":"12px",cursor:"pointer"}})]):t._e()])])])]:i("div",{staticClass:"spaceStart playSearch_wrap",class:t.isExpand?"expand":""},[i("div",{staticClass:"playSearch_tit"},[t._v(t._s(t.groupName)+"\n      "),t.isType(t.list)?i("div",{staticClass:"searchTypeBox",on:{click:function(e){return t.searchTypeClick(t.currentItem,t.currentItem.valueSearchType)}}},["must"==t.currentItem.valueSearchType?i("span",[t._v("\n              全部都要有\n            ")]):i("span",{staticStyle:{display:"flex","align-items":"center"}},[t._v("\n              有\n                "),i("span",{on:{click:function(t){t.stopPropagation()}}},[i("el-input-number",{staticClass:"sholdNumberInput",attrs:{min:0,max:t.currentItem.maxSholdValue},on:{change:function(e){return t.handleChange(t.currentItem,t.currentItem.valueSearchType,t.currentItem.valueSearchTypeValue)}},model:{value:t.currentItem.valueSearchTypeValue,callback:function(e){t.$set(t.currentItem,"valueSearchTypeValue",e)},expression:"currentItem.valueSearchTypeValue"}})],1),t._v("\n                个就行 \n                "),i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top"}},[i("div",{attrs:{slot:"content"},slot:"content"},[t._v("【有0个就行】该条件不做强制命中要求，如命中则会增加搜索得分"),i("br"),t._v("【有1+个就行】该条件会强制命中要求，命中越多得分越高")]),t._v(" "),i("i",{staticClass:"el-icon-question"})])],1),t._v(" "),i("div",{on:{click:function(t){t.stopPropagation()}}},[i("span",{staticStyle:{color:"red"},on:{click:function(e){return t.clearCurrentItem(t.currentItem)}}},[t._v("清空")]),t._v(" "),i("span",{on:{click:function(e){return t.allCurrentItem(t.currentItem)}}},[t._v("全选")])])]):t._e()]),t._v(" "),i("div",{staticStyle:{flex:"1"}},[t.list.length>1?i("div",{staticClass:"spaceStart flexWrap"},t._l(t.list,function(e,a){return i("div",{key:a,staticClass:"playSearch_item_parent",class:t.attrName===e.name?"active":"",on:{click:function(i){return i.stopPropagation(),t.handelAttrClick(e.name)}}},[i("el-badge",{staticClass:"item",attrs:{hidden:!t.getCurrentItemNum(e.name),value:t.getCurrentItemNum(e.name)}},[t._v("\n          "+t._s(e.name)+"\n        ")])],1)}),0):t._e(),t._v(" "),i("div",{staticClass:"spaceStart item-box"},[i("div",{staticClass:"spaceStart flexWrap item-list",staticStyle:{flex:"1"}},[t.currentItem.hasClear?i("div",{staticClass:"playSearch_item",class:t.currentItem.selectValue&&t.currentItem.selectValue.length?"":"active",on:{click:function(e){return e.stopPropagation(),t.clearValue.apply(null,arguments)}}},[t._v("\n            不限\n          ")]):t._e(),t._v(" "),t._l(t.currentItem.optionList,function(e,a){return i("div",{key:a,staticClass:"playSearch_item",class:t.currentItem.selectValue.includes(e)?"active":"",on:{click:function(i){return i.stopPropagation(),t.choose(e)}}},[t._v("\n            "+t._s(e)+"\n          ")])})],2),t._v(" "),t.currentItem.optionList&&t.currentItem.optionList.length>8?i("div",{staticClass:"more_btn",on:{click:t.toggel}},[t._v("\n          "+t._s(t.isExpand?"收起":"更多")+"\n          "),i("i",{class:t.isExpand?"el-icon-arrow-up":"el-icon-arrow-down",staticStyle:{"font-size":"12px",cursor:"pointer"}})]):t._e()])])])],2)},staticRenderFns:[]};var l=i("VU/8")(n,r,!1,function(t){i("r5is")},"data-v-49c9d560",null);e.a=l.exports},NYDq:function(t,e){},P5WN:function(t,e){},r5is:function(t,e){},zBuL:function(t,e,i){"use strict";var a=i("mvHQ"),s=i.n(a),n={props:{item:{type:Object,default:function(){}},index:{type:Number,default:0}},data:function(){return{}},methods:{change:function(t,e){var i=this.item.selectValue,a=JSON.parse(s()(i));a[e]=t,this.$emit("change",a)}}},r={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"spaceStart playSearch_wrap",staticStyle:{width:"33.3333333%","flex-shrink":"0","padding-right":"20px","margin-bottom":"24px"}},[i("div",{staticClass:"playSearch_tit"},[t._v(t._s(t.item.name))]),t._v(" "),i("div",{staticClass:"spaceStart sxboxItem"},[i("el-input",{staticStyle:{"margin-right":"15px"},attrs:{value:t.item.selectValue[0],placeholder:"最低"+t.item.name,size:"small",type:"tel",onkeypress:"return(/[\\d]/.test(String.fromCharCode(event.keyCode)))"},on:{input:function(e){return t.change(e,0)}}}),t._v(" "),i("div",{staticStyle:{"margin-right":"15px",color:"rgb(153, 153, 153)"}},[t._v("-")]),t._v(" "),i("el-input",{attrs:{value:t.item.selectValue[1],placeholder:"最高"+t.item.name,size:"small",type:"tel",onkeypress:"return(/[\\d]/.test(String.fromCharCode(event.keyCode)))"},on:{input:function(e){return t.change(e,1)}}})],1)])},staticRenderFns:[]};var l=i("VU/8")(n,r,!1,function(t){i("NYDq")},"data-v-25f451be",null);e.a=l.exports}});