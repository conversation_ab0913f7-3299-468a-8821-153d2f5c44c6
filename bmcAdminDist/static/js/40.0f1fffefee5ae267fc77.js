webpackJsonp([40],{RDAM:function(t,e){},ak23:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=a("Xxa5"),i=a.n(s),n=a("exGp"),l=a.n(n),r=a("woOf"),o=a.n(r),c=a("0xDb"),u=(a("gYgI"),a("CTAa")),d=a("5rT4"),p={startTime:void 0,endTime:void 0,username:void 0,imaccount:void 0,sessionType:void 0,pageNum:1,pageSize:20,date:c.b.timeFormat(new Date,"YYYY-MM-DD")},m={pageNum:1,pageSize:20,order:"DESC"},g={pageNum:1,pageSize:20,order:"ASC"},h={components:{},data:function(){return{checkNote:"",showMsgList:!1,options:[{label:"单聊",value:"PERSON"},{label:"群聊",value:"TEAM"}],msgList:[],util:c.b,listQuery:o()({},p),list:null,total:null,listLoading:!0,total2:null,total2Asc:null,listQuery2:o()({},m),listQuery2Asc:o()({},g)}},mounted:function(){this.listQuery.imaccount=this.$route.query.imaccount,this.initList()},methods:{inChat:function(t,e){var a=this;Object(u.f)(e.sessionNo).then(function(t){t.code&&a.$message({type:"success",message:t.message})})},handleSearchList:function(){this.listQuery.pageNum=1,this.initList()},onDetail:function(t,e){var a=this;return l()(i.a.mark(function t(){return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:a.listQuery2.pageNum=1,a.listQuery2.sessionNo=e.sessionNo,a.listQuery2.date=a.listQuery.date,Object(d.A)(a.listQuery2).then(function(t){if(200==t.code){a.rowDetail=e;var s=t.data.list.reverse();s=a.formatMsg(s),a.msgList=s,a.total2=t.data.totalPage,a.showMsgList=!0,a.getDetailAsc(e)}});case 4:case"end":return t.stop()}},t,a)}))()},getDetailAsc:function(t){var e=this;this.listQuery2Asc.pageNum=1,this.listQuery2Asc.sessionNo=t.sessionNo,this.listQuery2Asc.date=this.listQuery.date,Object(d.A)(this.listQuery2Asc).then(function(t){if(200==t.code){var a=t.data.list||[];a=e.formatMsg(a),e.msgList=e.msgList.concat(a),e.total2Asc=t.data.totalPage}})},doUpdateCheckFlag:function(){var t=this,e={id:this.rowDetail.id,checkFlag:1,checkNote:this.checkNote};Object(d._2)(e).then(function(e){200==e.code&&(t.showMsgList=!1,t.initList())})},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.initList()},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.initList()},initList:function(){var t=this;this.listQuery.sessionType||delete this.listQuery.sessionType,this.listQuery.date?Object(d.C)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total}):this.$message.error("请先选择日期")},handleCurrentChange2:function(){var t=this;this.listQuery2.pageNum++,Object(d.A)(this.listQuery2).then(function(e){if(200==e.code){var a=e.data.list.reverse();a=t.formatMsg(a),t.msgList=a.concat(t.msgList),t.total2=e.data.totalPage}})},handleCurrentChange2Asc:function(){var t=this;this.listQuery2Asc.pageNum++,Object(d.A)(this.listQuery2Asc).then(function(e){if(200==e.code){var a=e.data.list||[];a=t.formatMsg(a),t.msgList=t.msgList.concat(a),t.total2Asc=e.data.totalPage}})},formatMsg:function(t){return t.forEach(function(t){if("CUSTOM"==t.msgtype||"PICTURE"==t.msgtype)try{t.attach=JSON.parse(t.attach)}catch(e){t.attach=t.attach.replace(/\t/g,"");try{t.attach=JSON.parse(t.attach)}catch(e){t.attach={body:{}}}}}),t.filter(function(t){return"TEAM_INVITE"!=t.msgtype})}}},v={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"会话类型："}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.listQuery.sessionType,callback:function(e){t.$set(t.listQuery,"sessionType",e)},expression:"listQuery.sessionType"}},t._l(t.options,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"选择日期："}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期","value-format":"yyyy-MM-dd"},model:{value:t.listQuery.date,callback:function(e){t.$set(t.listQuery,"date",e)},expression:"listQuery.date"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"用户名："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"用户名"},model:{value:t.listQuery.username,callback:function(e){t.$set(t.listQuery,"username",e)},expression:"listQuery.username"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询结果\n          ")]),t._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[a("el-table-column",{attrs:{type:"index",width:"60",label:"序号"}}),t._v(" "),a("el-table-column",{attrs:{type:"index",width:"160",label:"会话类型"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v("\n            "+t._s("TEAM"==e.row.sessionType?"群聊":"单聊")+"\n          ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"会话",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v("\n            "+t._s("TEAM"==e.row.sessionType?e.row.name:e.row.sessionNo)+"\n          ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"更新时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(t.util.timeFormat(e.row.updateTime))+"\n        ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(t.util.timeFormat(e.row.createTime))+"\n        ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"质检备注",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.checkNote)+"\n        ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"300",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:e.row.checkFlag?"primary":"danger"},on:{click:function(a){return t.onDetail(e.$index,e.row)}}},[t._v("查看详情\n          ")]),t._v(" "),"TEAM"==e.row.sessionType?a("el-button",{attrs:{size:"mini"},on:{click:function(a){return t.inChat(e.$index,e.row)}}},[t._v("进入群聊\n          ")]):t._e()]}}])})],1)],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("el-dialog",{attrs:{visible:t.showMsgList},on:{"update:visible":function(e){t.showMsgList=e}}},[a("div",{staticClass:"msgList"},[a("div",{staticClass:"spaceCenter"},[t.total2>t.listQuery2.pageNum?a("el-link",{attrs:{type:"primary"},on:{click:t.handleCurrentChange2}},[t._v("\n          查看更多\n        ")]):t._e()],1),t._v(" "),t._l(t.msgList,function(e,s){return a("div",{key:s,staticClass:"msg"},[a("div",{staticClass:"chat-message-list-item-wrap"},[a("div",{staticClass:"common-complex-avatar-wrapper common-complex-avatar-wrapper-nocursor"},[a("div",[a("span",{staticClass:"ant-badge ant-badge-status"},[a("span",{staticClass:"ant-avatar ant-avatar-circle ant-avatar-image",staticStyle:{width:"36px",height:"36px","line-height":"36px","font-size":"18px","vertical-align":"middle"}})])])]),t._v(" "),a("div",{staticClass:"ant-dropdown-trigger chat-message-list-item-content-box"},[a("div",{staticClass:"name"},[a("span",[t._v(t._s(e.fromnick))])]),t._v(" "),a("div",{staticClass:"chat-message-list-item-content"},[a("div",{staticClass:"chat-message-list-item-body",style:e.fromnick&&e.fromnick.includes("客服")?"background:#b3f1bd":""},["CUSTOM"==e.msgtype?a("div",{staticStyle:{border:"solid 1px #a0d0f8"}},[a("div",{staticClass:"kk_custom_msg_wrapper kk_custom_msg_wrapper2"},[a("div",{staticClass:"kk_custom_msg_title",domProps:{innerHTML:t._s(e.attach.body.title)}}),t._v(" "),a("div",{staticClass:"kk_custom_msg_content",domProps:{innerHTML:t._s(e.attach.body.content)}})])]):"PICTURE"==e.msgtype?a("div",{staticStyle:{"max-width":"320px","max-height":"300px"}},[a("el-image",{attrs:{src:e.attach.url,"preview-src-list":[e.attach.url]}})],1):a("span",{staticClass:"common-parse-session-text-wrapper"},[t._v(t._s(e.body)+"\n                ")])])]),t._v(" "),a("div",{staticClass:"chat-message-list-item-date"},[t._v("\n              "+t._s(t.util.timeFormat(e.msgtimestamp))+"\n            ")])])])])}),t._v(" "),t.total2Asc>t.listQuery2Asc.pageNum?a("el-link",{attrs:{type:"primary"},on:{click:t.handleCurrentChange2Asc}},[t._v("\n        查看更多\n      ")]):t._e()],2),t._v(" "),a("div",[a("el-input",{attrs:{placeholder:"请输入质检备注"},model:{value:t.checkNote,callback:function(e){t.checkNote=e},expression:"checkNote"}})],1),t._v(" "),a("div",{staticClass:"spaceEnd",staticStyle:{"margin-top":"10px"}},[a("el-button",{on:{click:function(e){t.showMsgList=!1}}},[t._v("关闭")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.doUpdateCheckFlag}},[t._v("已检验")])],1)])],1)},staticRenderFns:[]};var y=a("VU/8")(h,v,!1,function(t){a("RDAM")},"data-v-882bfe82",null);e.default=y.exports},gYgI:function(t,e){}});