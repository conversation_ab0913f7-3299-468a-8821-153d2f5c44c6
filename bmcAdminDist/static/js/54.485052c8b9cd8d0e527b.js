webpackJsonp([54],{D5Pc:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("woOf"),n=a.n(i),l=(a("PJh5"),a("IcnI"),a("0xDb")),r=a("QPAx"),s={keyword:null,productSn:null,status:-1,pageNum:1,pageSize:20},o={components:{},data:function(){return{activeName:"buy",util:l.b,listQuery:n()({},s),list:null,total:null,listLoading:!0}},created:function(){this.initList()},methods:{handleSearchList:function(){this.listQuery.pageNum=1,this.initList()},handleResetSearch:function(){this.listQuery=n()({},s)},handleClick:function(){this.handleResetSearch(),this.initList()},getStatus:function(t){switch(t.status){case 0:return"待付款";case 1:case 2:return"已付款";case 3:return"已完成";case 4:return"已关闭";case 5:return"已取消"}},goProduct:function(t){var e=t.id;window.open("https://www.kkzhw.com/playDetail?productId="+e)},handleSelectionChange:function(){},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.initList()},initList:function(){var t=this;"buy"===this.activeName?Object(r.a)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list||[],t.list.forEach(function(t){t.productDetail=t.orderItemList.find(function(t){return 0===t.itemType}),t.productDetail.productName=t.productDetail.productName.replace(/\[核\]/g,"").replace(/\[绝\]/g,"").replace(/\[钱\]/g,"")}),t.total=e.data.total}):Object(r.b)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list||[],t.list.forEach(function(t){t.productDetail=t.orderItemList.find(function(t){return 0===t.itemType})}),t.total=e.data.total})}}},u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"买家订单",name:"buy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"卖家订单",name:"seller"}})],1)],1),t._v(" "),a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"输入搜索："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品名称"},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品编号"},model:{value:t.listQuery.productSn,callback:function(e){t.$set(t.listQuery,"productSn",e)},expression:"listQuery.productSn"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询结果\n          ")]),t._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"index",width:"60",label:"序号"}}),t._v(" "),a("el-table-column",{attrs:{label:"订单号",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.orderSn))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"售价",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.totalAmount))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品编号",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.productDetail.productSn))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品信息",width:"500",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"goodsHome_shopItem_box",on:{click:function(a){return t.goProduct(e.row)}}},[a("div",{staticClass:"goods_shopItem_top"},[a("div",{staticClass:"goods_shopItem_pic"},[a("img",{attrs:{src:e.row.productDetail.productPic}})]),t._v(" "),a("div",{staticClass:"goods_showItem_right"},[a("div",{staticClass:"goodsHome_Item_header"},[e.row.productDetail.productName?a("div",{staticClass:"subTitleMp",attrs:{"prediv-img":!1,"show-img-menu":!1,"lazy-load":!1},domProps:{innerHTML:t._s(e.row.productDetail.productName)}}):t._e()])])]),t._v(" "),a("div",{staticClass:"goods_shopItem_bottom spaceBetween"},[a("div",[e.row.productDetail.productCategoryName?a("span",{staticClass:"ext_item"},[t._v("\n                  "+t._s(e.row.productDetail.productCategoryName)+"\n                ")]):t._e(),t._v(" "),e.row.productDetail.gameAccountQufu?a("span",{staticClass:"ext_item"},[t._v("\n                  "+t._s(e.row.productDetail.gameAccountQufu)+"\n                ")]):t._e()])])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v("\n            "+t._s(t.util.timeFormat(e.row.createTime,"YYYY-MM-DD HH:mm:ss"))+"\n          ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(t.getStatus(e.row)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"}})],1)],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},staticRenderFns:[]};var c=a("VU/8")(o,u,!1,function(t){a("IxQp")},"data-v-ab38563a",null);e.default=c.exports},IxQp:function(t,e){}});