webpackJsonp([70],{"f/Bq":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("mRsl"),i=n("Lfj9"),s={name:"ProductCateList",filters:{levelFilter:function(t){return 0===t?"一级":1===t?"二级":void 0},disableNextLevel:function(t){return 0!==t}},data:function(){return{list:null,total:null,listLoading:!0,listQuery:{pageNum:1,pageSize:20},parentId:74}},watch:{$route:function(t){this.resetParentId(),this.getList()}},created:function(){this.resetParentId(),this.getList()},methods:{deleteIndex:function(){var t=this;this.$confirm("是否要删除所有索引","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(a.b)().then(function(e){200===e.code&&t.$message.success("删除索引成功")})})},refreshIndex:function(t,e){var n=this;Object(a.g)({id:e.id}).then(function(t){200==t.code&&n.$message.success("刷新索引成功")})},resetParentId:function(){this.listQuery.pageNum=1,this.parentId=74},handleAddProductCate:function(){this.$router.push("/pms/addProductCate")},getList:function(){var t=this;this.listLoading=!0,console.log(this.listQuery,111111),Object(i.L)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},handleNavStatusChange:function(t,e){var n=this,i=new URLSearchParams,s=[];s.push(e.id),i.append("ids",s),i.append("navStatus",e.navStatus),Object(a.h)(i).then(function(t){200==t.code&&n.$message({message:"修改成功",type:"success",duration:1e3})})},handleShowStatusChange:function(t,e){var n=this,i=new URLSearchParams,s=[];s.push(e.id),i.append("ids",s),i.append("showStatus",e.showStatus),Object(a.j)(i).then(function(t){200==t.code&&n.$message({message:"修改成功",type:"success",duration:1e3})})},handleShowNextLevel:function(t,e){this.$router.push({path:"/pms/productAttrTypeList",query:{cid:e.attriCateId}})},handleSearchConfigNext:function(t,e){this.$router.push({path:"/BMC-HSZX/searchConfig",query:{id:e.productCategoryId}})},handleNumberSelectionNext:function(t,e){this.$router.push({path:"/BMC-HSZX/numberSelection",query:{id:e.productCategoryId}})},handleTransferProduct:function(t,e){console.log("handleAddProductCate")},handleUpdate:function(t,e){this.$router.push({path:"/pms/updateProductCateList",query:{id:e.id}})},handleDelete:function(t,e){var n=this;this.$confirm("是否要删除该品牌","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(a.c)(e.id).then(function(t){200==t.code&&(n.$message({message:"删除成功",type:"success",duration:1e3}),n.getList())})})}}},r={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productCateTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[n("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"分类图",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[n("img",{staticStyle:{width:"60px",height:"60px"},attrs:{src:t.row.icon,alt:""}})]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"分类名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.name))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"快捷筛选",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.handleSearchConfigNext(e.$index,e.row)}}},[t._v("\n            找号筛选管理\n          ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"选号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.handleNumberSelectionNext(e.$index,e.row)}}},[t._v("\n            选号\n          ")])]}}])})],1)],1),t._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])},staticRenderFns:[]};var u=n("VU/8")(s,r,!1,function(t){n("sgEI")},"data-v-6267ee9d",null);e.default=u.exports},sgEI:function(t,e){}});