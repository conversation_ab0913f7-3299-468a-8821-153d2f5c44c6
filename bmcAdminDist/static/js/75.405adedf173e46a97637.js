webpackJsonp([75],{CfCW:function(t,e){},bnPf:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("woOf"),l=n.n(a),i=n("0xDb"),r=n("CTAa"),s={0:"买家咨询",1:"卖家咨询"},u={keyword:null,pageNum:1,pageSize:20,productSn:null,consultContent:null},o={components:{},data:function(){return{util:i.b,listQuery:l()({},u),list:null,total:null,listLoading:!0}},created:function(){this.initList()},methods:{onDel:function(t,e){var n=this;this.$confirm("是否要删除","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(r.a)(e.id).then(function(t){n.$message({type:"success",message:"删除成功!"}),n.initList()})})},getType:function(t){return s[t]},handleSearchList:function(){this.listQuery.pageNum=1,this.initList()},handleResetSearch:function(){this.listQuery=l()({},u)},handleSelectionChange:function(){},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.initList()},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},initList:function(){var t=this;Object(r.b)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})}}},c={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[n("div",{staticStyle:{"margin-top":"15px"}},[n("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[n("el-form-item",{attrs:{label:"商品编号："}},[n("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"商品编号"},model:{value:t.listQuery.productSn,callback:function(e){t.$set(t.listQuery,"productSn",e)},expression:"listQuery.productSn"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"小记内容："}},[n("el-input",{staticStyle:{width:"203px"},attrs:{placeholder:"小记内容"},model:{value:t.listQuery.consultContent,callback:function(e){t.$set(t.listQuery,"consultContent",e)},expression:"listQuery.consultContent"}})],1),t._v(" "),n("el-form-item",[n("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询结果\n          ")]),t._v(" "),n("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{type:"index",width:"60",label:"序号"}}),t._v(" "),n("el-table-column",{attrs:{label:"商品编号",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.productSn))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"咨询类型",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(t.getType(e.row.consultType)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"咨询客服",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.consultFrom))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"咨询用户",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.consultTo))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"小记内容",width:"300",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v(t._s(e.row.consultContent))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"创建时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v("\n            "+t._s(t.util.timeFormat(e.row.createTime,"YYYY-MM-DD HH:mm:ss"))+"\n          ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"最后更新时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("p",[t._v("\n            "+t._s(t.util.timeFormat(e.row.updateTime,"YYYY-MM-DD HH:mm:ss"))+"\n          ")])]}}])})],1)],1),t._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],"current-page":t.listQuery.pageNum,total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},staticRenderFns:[]};var p=n("VU/8")(o,c,!1,function(t){n("CfCW")},"data-v-53ea995b",null);e.default=p.exports}});