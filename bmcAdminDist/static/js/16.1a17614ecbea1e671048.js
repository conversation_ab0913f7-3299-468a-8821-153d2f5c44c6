webpackJsonp([16],{CmKi:function(t,e,a){"use strict";var l=a("woOf"),i=a.n(l),r=a("mRsl"),s=a("s/Rn"),u=a("UgCr"),n={name:"ProductInfoDetail",props:{value:Object,isEdit:{type:Boolean,default:!1}},data:function(){return{hasEditCreated:!1,selectProductCateValue:[],productCateOptions:[],brandOptions:[],rules:{name:[{required:!0,message:"请输入商品名称",trigger:"blur"},{min:2,max:140,message:"长度在 2 到 140 个字符",trigger:"blur"}],subTitle:[{required:!0,message:"请输入商品副标题",trigger:"blur"}],productCategoryId:[{required:!0,message:"请选择商品分类",trigger:"blur"}],brandId:[{required:!0,message:"请选择商品品牌",trigger:"blur"}],description:[{required:!0,message:"请输入商品介绍",trigger:"blur"}],requiredProp:[{required:!0,message:"该项为必填项",trigger:"blur"}]}}},created:function(){this.getProductCateList(),this.getBrandList()},computed:{productId:function(){return this.value.id}},watch:{productId:function(t){this.isEdit&&(this.hasEditCreated||void 0!==t&&null!=t&&0!==t&&this.handleEditCreated())},selectProductCateValue:function(t){null!=t&&2===t.length?(this.value.productCategoryId=t[1],this.value.productCategoryName=this.getCateNameById(this.value.productCategoryId)):(this.value.productCategoryId=null,this.value.productCategoryName=null)}},methods:{handleEditCreated:function(){null!=this.value.productCategoryId&&(this.selectProductCateValue=[],this.selectProductCateValue.push(this.value.cateParentId),this.selectProductCateValue.push(this.value.productCategoryId)),this.hasEditCreated=!0},getProductCateList:function(){var t=this;Object(r.e)().then(function(e){var a=e.data;t.productCateOptions=[];for(var l=0;l<a.length;l++){var i=[];if(null!=a[l].children&&a[l].children.length>0)for(var r=0;r<a[l].children.length;r++)i.push({label:a[l].children[r].name,value:a[l].children[r].id});t.productCateOptions.push({label:a[l].name,value:a[l].id,children:i})}})},getBrandList:function(){var t=this;Object(s.c)({pageNum:1,pageSize:200}).then(function(e){t.brandOptions=[];for(var a=e.data.list,l=0;l<a.length;l++)t.brandOptions.push({label:a[l].name,value:a[l].id})})},getCateNameById:function(t){for(var e=null,a=0;a<this.productCateOptions.length;a++)for(var l=0;l<this.productCateOptions[a].children.length;l++)if(this.productCateOptions[a].children[l].value===t)return e=this.productCateOptions[a].children[l].label;return e},handleNext:function(t){var e=this;this.$refs[t].validate(function(t){if(!t)return e.$message({message:"验证失败",type:"error",duration:1e3}),!1;e.$emit("nextStep")})},handleBrandChange:function(t){for(var e="",a=0;a<this.brandOptions.length;a++)if(this.brandOptions[a].value===t){e=this.brandOptions[a].label;break}this.value.brandName=e}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-top":"50px"}},[a("el-form",{ref:"productInfoForm",staticClass:"form-inner-container",attrs:{model:t.value,rules:t.rules,"label-width":"120px",size:"small"}},[a("el-form-item",{attrs:{label:"商品分类：",prop:"productCategoryId"}},[a("el-cascader",{attrs:{options:t.productCateOptions},model:{value:t.selectProductCateValue,callback:function(e){t.selectProductCateValue=e},expression:"selectProductCateValue"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品名称：",prop:"name"}},[a("el-input",{model:{value:t.value.name,callback:function(e){t.$set(t.value,"name",e)},expression:"value.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"副标题：",prop:"subTitle"}},[a("el-input",{model:{value:t.value.subTitle,callback:function(e){t.$set(t.value,"subTitle",e)},expression:"value.subTitle"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品品牌：",prop:"brandId"}},[a("el-select",{attrs:{placeholder:"请选择品牌"},on:{change:t.handleBrandChange},model:{value:t.value.brandId,callback:function(e){t.$set(t.value,"brandId",e)},expression:"value.brandId"}},t._l(t.brandOptions,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"商品介绍："}},[a("el-input",{attrs:{autoSize:!0,type:"textarea",placeholder:"请输入内容"},model:{value:t.value.description,callback:function(e){t.$set(t.value,"description",e)},expression:"value.description"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品编号："}},[a("el-input",{model:{value:t.value.productSn,callback:function(e){t.$set(t.value,"productSn",e)},expression:"value.productSn"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品售价："}},[a("el-input",{model:{value:t.value.price,callback:function(e){t.$set(t.value,"price",e)},expression:"value.price"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"市场价："}},[a("el-input",{model:{value:t.value.originalPrice,callback:function(e){t.$set(t.value,"originalPrice",e)},expression:"value.originalPrice"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品库存："}},[a("el-input",{model:{value:t.value.stock,callback:function(e){t.$set(t.value,"stock",e)},expression:"value.stock"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"计量单位："}},[a("el-input",{model:{value:t.value.unit,callback:function(e){t.$set(t.value,"unit",e)},expression:"value.unit"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品重量："}},[a("el-input",{staticStyle:{width:"300px"},model:{value:t.value.weight,callback:function(e){t.$set(t.value,"weight",e)},expression:"value.weight"}}),t._v(" "),a("span",{staticStyle:{"margin-left":"20px"}},[t._v("克")])],1),t._v(" "),a("el-form-item",{attrs:{label:"排序"}},[a("el-input",{model:{value:t.value.sort,callback:function(e){t.$set(t.value,"sort",e)},expression:"value.sort"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(e){return t.handleNext("productInfoForm")}}},[t._v("下一步，填写商品促销")])],1)],1)],1)},staticRenderFns:[]};var c=a("VU/8")(n,o,!1,function(t){a("UlPQ")},"data-v-2bbb7b50",null).exports,d=a("n97X"),v={name:"ProductSaleDetail",props:{value:Object,isEdit:{type:Boolean,default:!1}},data:function(){return{pickerOptions1:{disabledDate:function(t){return t.getTime()<Date.now()}}}},created:function(){var t=this;this.isEdit||Object(d.a)({defaultStatus:0}).then(function(e){for(var a=[],l=0;l<e.data.length;l++){var i=e.data[l];a.push({memberLevelId:i.id,memberLevelName:i.name})}t.value.memberPriceList=a})},computed:{selectServiceList:{get:function(){var t=[];if(void 0===this.value.serviceIds||null==this.value.serviceIds||""===this.value.serviceIds)return t;for(var e=this.value.serviceIds.split(","),a=0;a<e.length;a++)t.push(Number(e[a]));return t},set:function(t){var e="";if(null!=t&&t.length>0){for(var a=0;a<t.length;a++)e+=t[a]+",";e.endsWith(",")&&(e=e.substr(0,e.length-1)),this.value.serviceIds=e}else this.value.serviceIds=null}}},methods:{handleEditCreated:function(){var t=this.value.serviceIds.split(",");console.log("handleEditCreated",t);for(var e=0;e<t.length;e++)this.selectServiceList.push(Number(t[e]))},handleRemoveProductLadder:function(t,e){var a=this.value.productLadderList;1===a.length?(a.pop(),a.push({count:0,discount:0,price:0})):a.splice(t,1)},handleAddProductLadder:function(t,e){var a=this.value.productLadderList;a.length<3?a.push({count:0,discount:0,price:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handleRemoveFullReduction:function(t,e){var a=this.value.productFullReductionList;1===a.length?(a.pop(),a.push({fullPrice:0,reducePrice:0})):a.splice(t,1)},handleAddFullReduction:function(t,e){var a=this.value.productFullReductionList;a.length<3?a.push({fullPrice:0,reducePrice:0}):this.$message({message:"最多只能添加三条",type:"warning"})},handlePrev:function(){this.$emit("prevStep")},handleNext:function(){this.$emit("nextStep")}}},p={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-top":"50px"}},[a("el-form",{ref:"productSaleForm",staticClass:"form-inner-container",attrs:{model:t.value,"label-width":"120px",size:"small"}},[a("el-form-item",{attrs:{label:"赠送积分："}},[a("el-input",{model:{value:t.value.giftPoint,callback:function(e){t.$set(t.value,"giftPoint",e)},expression:"value.giftPoint"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"赠送成长值："}},[a("el-input",{model:{value:t.value.giftGrowth,callback:function(e){t.$set(t.value,"giftGrowth",e)},expression:"value.giftGrowth"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"积分购买限制："}},[a("el-input",{model:{value:t.value.usePointLimit,callback:function(e){t.$set(t.value,"usePointLimit",e)},expression:"value.usePointLimit"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"预告商品："}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.value.previewStatus,callback:function(e){t.$set(t.value,"previewStatus",e)},expression:"value.previewStatus"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品上架："}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.value.publishStatus,callback:function(e){t.$set(t.value,"publishStatus",e)},expression:"value.publishStatus"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品推荐："}},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v("新品")]),t._v(" "),a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.value.newStatus,callback:function(e){t.$set(t.value,"newStatus",e)},expression:"value.newStatus"}}),t._v(" "),a("span",{staticStyle:{"margin-left":"10px","margin-right":"10px"}},[t._v("推荐")]),t._v(" "),a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.value.recommandStatus,callback:function(e){t.$set(t.value,"recommandStatus",e)},expression:"value.recommandStatus"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"服务保证："}},[a("el-checkbox-group",{model:{value:t.selectServiceList,callback:function(e){t.selectServiceList=e},expression:"selectServiceList"}},[a("el-checkbox",{attrs:{label:1}},[t._v("无忧退货")]),t._v(" "),a("el-checkbox",{attrs:{label:2}},[t._v("快速退款")]),t._v(" "),a("el-checkbox",{attrs:{label:3}},[t._v("免费包邮")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"详细页标题："}},[a("el-input",{model:{value:t.value.detailTitle,callback:function(e){t.$set(t.value,"detailTitle",e)},expression:"value.detailTitle"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"详细页描述："}},[a("el-input",{model:{value:t.value.detailDesc,callback:function(e){t.$set(t.value,"detailDesc",e)},expression:"value.detailDesc"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品关键字："}},[a("el-input",{model:{value:t.value.keywords,callback:function(e){t.$set(t.value,"keywords",e)},expression:"value.keywords"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品备注："}},[a("el-input",{attrs:{type:"textarea",autoSize:!0},model:{value:t.value.note,callback:function(e){t.$set(t.value,"note",e)},expression:"value.note"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"选择优惠方式："}},[a("el-radio-group",{attrs:{size:"small"},model:{value:t.value.promotionType,callback:function(e){t.$set(t.value,"promotionType",e)},expression:"value.promotionType"}},[a("el-radio-button",{attrs:{label:0}},[t._v("无优惠")]),t._v(" "),a("el-radio-button",{attrs:{label:1}},[t._v("特惠促销")]),t._v(" "),a("el-radio-button",{attrs:{label:2}},[t._v("会员价格")]),t._v(" "),a("el-radio-button",{attrs:{label:3}},[t._v("阶梯价格")]),t._v(" "),a("el-radio-button",{attrs:{label:4}},[t._v("满减价格")])],1)],1),t._v(" "),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:1===t.value.promotionType,expression:"value.promotionType===1"}]},[a("div",[t._v("\n        开始时间：\n        "),a("el-date-picker",{attrs:{type:"datetime","picker-options":t.pickerOptions1,placeholder:"选择开始时间"},model:{value:t.value.promotionStartTime,callback:function(e){t.$set(t.value,"promotionStartTime",e)},expression:"value.promotionStartTime"}})],1),t._v(" "),a("div",{staticClass:"littleMargin"},[t._v("\n        结束时间：\n        "),a("el-date-picker",{attrs:{type:"datetime","picker-options":t.pickerOptions1,placeholder:"选择结束时间"},model:{value:t.value.promotionEndTime,callback:function(e){t.$set(t.value,"promotionEndTime",e)},expression:"value.promotionEndTime"}})],1),t._v(" "),a("div",{staticClass:"littleMargin"},[t._v("\n        促销价格：\n        "),a("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"输入促销价格"},model:{value:t.value.promotionPrice,callback:function(e){t.$set(t.value,"promotionPrice",e)},expression:"value.promotionPrice"}})],1)]),t._v(" "),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:2===t.value.promotionType,expression:"value.promotionType===2"}]},t._l(t.value.memberPriceList,function(e,l){return a("div",{class:{littleMargin:0!==l}},[t._v("\n        "+t._s(e.memberLevelName)+"：\n        "),a("el-input",{staticStyle:{width:"200px"},model:{value:e.memberPrice,callback:function(a){t.$set(e,"memberPrice",a)},expression:"item.memberPrice"}})],1)}),0),t._v(" "),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:3===t.value.promotionType,expression:"value.promotionType===3"}]},[a("el-table",{staticStyle:{width:"80%"},attrs:{data:t.value.productLadderList,border:""}},[a("el-table-column",{attrs:{label:"数量",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.count,callback:function(a){t.$set(e.row,"count",a)},expression:"scope.row.count"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"折扣",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.discount,callback:function(a){t.$set(e.row,"discount",a)},expression:"scope.row.discount"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.handleRemoveProductLadder(e.$index,e.row)}}},[t._v("删除")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.handleAddProductLadder(e.$index,e.row)}}},[t._v("添加")])]}}])})],1)],1),t._v(" "),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:4===t.value.promotionType,expression:"value.promotionType===4"}]},[a("el-table",{staticStyle:{width:"80%"},attrs:{data:t.value.productFullReductionList,border:""}},[a("el-table-column",{attrs:{label:"满",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.fullPrice,callback:function(a){t.$set(e.row,"fullPrice",a)},expression:"scope.row.fullPrice"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"立减",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.reducePrice,callback:function(a){t.$set(e.row,"reducePrice",a)},expression:"scope.row.reducePrice"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.handleRemoveFullReduction(e.$index,e.row)}}},[t._v("删除")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.handleAddFullReduction(e.$index,e.row)}}},[t._v("添加")])]}}])})],1)],1),t._v(" "),a("el-form-item",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{size:"medium"},on:{click:t.handlePrev}},[t._v("上一步，填写商品信息")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"medium"},on:{click:t.handleNext}},[t._v("下一步，填写商品属性")])],1)],1)],1)},staticRenderFns:[]};var h=a("VU/8")(v,p,!1,function(t){a("Kgmr")},"data-v-31c36332",null).exports,m=a("mvHQ"),f=a.n(m),b=a("c/Tr"),g=a.n(b),P=a("lHA8"),k=a.n(P),S=a("KhLR"),x=a("3idm"),_=a("TZVV"),y=a("sl7S"),w=a("5aCZ"),L={name:"ProductAttrDetail",components:{SingleUpload:_.a,MultiUpload:y.a,Tinymce:w.a},props:{value:Object,isEdit:{type:Boolean,default:!1}},data:function(){return{hasEditCreated:!1,productAttributeCategoryOptions:[],selectProductAttr:[],selectProductParam:[],selectProductAttrPics:[],addProductAttrValue:"",activeHtmlName:"pc",extList:[]}},computed:{hasAttrPic:function(){return!(this.selectProductAttrPics.length<1)},productId:function(){return this.value.id},selectProductPics:{get:function(){var t=[];if(void 0===this.value.pic||null==this.value.pic||""===this.value.pic)return t;if(t.push(this.value.pic),void 0===this.value.albumPics||null==this.value.albumPics||""===this.value.albumPics)return t;for(var e=this.value.albumPics.split(","),a=0;a<e.length;a++)t.push(e[a]);return t},set:function(t){if(null==t||0===t.length)this.value.pic=null,this.value.albumPics=null;else if(this.value.pic=t[0],this.value.albumPics="",t.length>1)for(var e=1;e<t.length;e++)this.value.albumPics+=t[e],e!==t.length-1&&(this.value.albumPics+=",")}}},created:function(){this.getProductAttrCateList()},watch:{productId:function(t){this.isEdit&&(this.hasEditCreated||void 0!==t&&null!=t&&0!==t&&this.handleEditCreated())}},methods:{handleEditCreated:function(){null!=this.value.productAttributeCategoryId&&this.handleProductAttrChange(this.value.productAttributeCategoryId),this.hasEditCreated=!0},getProductAttrCateList:function(){var t=this;Object(S.c)({pageNum:1,pageSize:200}).then(function(e){t.productAttributeCategoryOptions=[];for(var a=e.data.list,l=0;l<a.length;l++)t.productAttributeCategoryOptions.push({label:a[l].name,value:a[l].id})})},getProductAttrList:function(t,e){var a=this,l={pageNum:1,pageSize:200,type:t};Object(x.c)(e,l).then(function(e){var l=e.data.list;if(0===t){a.selectProductAttr=[];for(var i=0;i<l.length;i++){var r=[],s=[];a.isEdit&&(1===l[i].handAddStatus&&(r=a.getEditAttrOptions(l[i].id)),s=a.getEditAttrValues(i)),a.selectProductAttr.push({id:l[i].id,name:l[i].name,handAddStatus:l[i].handAddStatus,inputList:l[i].inputList,values:s,options:r})}a.isEdit&&a.refreshProductAttrPics()}else if(1===t){a.selectProductParam=[];for(var u=0;u<l.length;u++){var n=null;a.isEdit&&(n=a.getEditParamValue(l[u].id)),a.selectProductParam.push({id:l[u].id,name:l[u].name,value:n,inputType:l[u].inputType,inputList:l[u].inputList})}}else{for(var o={index:parseInt(t,10),label:"扩展属性"+t,list:[]},c=0;c<l.length;c++){var d=null;a.isEdit&&(d=a.getEditParamValue(l[c].id)),o.list.push({id:l[c].id,name:l[c].name,value:d,inputType:l[c].inputType,inputList:l[c].inputList})}a.extList.push(o),a.extList.sort(function(t,e){return t.index-e.index})}})},getEditAttrOptions:function(t){for(var e=[],a=0;a<this.value.productAttributeValueList.length;a++){var l=this.value.productAttributeValueList[a];if(l.productAttributeId===t){for(var i=l.value.split(","),r=0;r<i.length;r++)e.push(i[r]);break}}return e},getEditAttrValues:function(t){var e=new k.a;if(0===t)for(var a=0;a<this.value.skuStockList.length;a++){var l=this.value.skuStockList[a],i=JSON.parse(l.spData);null!=i&&i.length>=1&&e.add(i[0].value)}else if(1===t)for(var r=0;r<this.value.skuStockList.length;r++){var s=this.value.skuStockList[r],u=JSON.parse(s.spData);null!=u&&u.length>=2&&e.add(u[1].value)}else for(var n=0;n<this.value.skuStockList.length;n++){var o=this.value.skuStockList[n],c=JSON.parse(o.spData);null!=c&&c.length>=3&&e.add(c[2].value)}return g()(e)},getEditParamValue:function(t){for(var e=0;e<this.value.productAttributeValueList.length;e++)if(t===this.value.productAttributeValueList[e].productAttributeId)return this.value.productAttributeValueList[e].value},handleProductAttrChange:function(t){this.getProductAttrList(0,t),this.getProductAttrList(1,t),this.getExtAttrList(t)},getExtAttrList:function(t){this.extList=[],this.getProductAttrList(2,t),this.getProductAttrList(3,t)},getInputListArr:function(t){return t.split(",")},handleAddProductAttrValue:function(t){var e=this.selectProductAttr[t].options;null!=this.addProductAttrValue&&""!=this.addProductAttrValue?-1===e.indexOf(this.addProductAttrValue)?(this.selectProductAttr[t].options.push(this.addProductAttrValue),this.addProductAttrValue=null):this.$message({message:"属性值不能重复",type:"warning",duration:1e3}):this.$message({message:"属性值不能为空",type:"warning",duration:1e3})},handleRemoveProductAttrValue:function(t,e){this.selectProductAttr[t].options.splice(e,1)},getProductSkuSp:function(t,e){var a=JSON.parse(t.spData);return null!=a&&e<a.length?a[e].value:null},handleRefreshProductSkuList:function(){var t=this;this.$confirm("刷新列表将导致sku信息重新生成，是否要刷新","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.refreshProductAttrPics(),t.refreshProductSkuList()})},handleSyncProductSkuPrice:function(){var t=this;this.$confirm("将同步第一个sku的价格到所有sku,是否继续","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){if(null!==t.value.skuStockList&&t.value.skuStockList.length>0){var e=[];e=e.concat(e,t.value.skuStockList);for(var a=t.value.skuStockList[0].price,l=0;l<e.length;l++)e[l].price=a;t.value.skuStockList=[],t.value.skuStockList=t.value.skuStockList.concat(t.value.skuStockList,e)}})},handleSyncProductSkuStock:function(){var t=this;this.$confirm("将同步第一个sku的库存到所有sku,是否继续","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){if(null!==t.value.skuStockList&&t.value.skuStockList.length>0){var e=[];e=e.concat(e,t.value.skuStockList);for(var a=t.value.skuStockList[0].stock,l=t.value.skuStockList[0].lowStock,i=0;i<e.length;i++)e[i].stock=a,e[i].lowStock=l;t.value.skuStockList=[],t.value.skuStockList=t.value.skuStockList.concat(t.value.skuStockList,e)}})},refreshProductSkuList:function(){this.value.skuStockList=[];var t=this.value.skuStockList;if(1===this.selectProductAttr.length)for(var e=this.selectProductAttr[0],a=0;a<e.values.length;a++)t.push({spData:f()([{key:e.name,value:e.values[a]}])});else if(2===this.selectProductAttr.length)for(var l=this.selectProductAttr[0],i=this.selectProductAttr[1],r=0;r<l.values.length;r++)if(0!==i.values.length)for(var s=0;s<i.values.length;s++){var u=[];u.push({key:l.name,value:l.values[r]}),u.push({key:i.name,value:i.values[s]}),t.push({spData:f()(u)})}else t.push({spData:f()([{key:l.name,value:l.values[r]}])});else for(var n=this.selectProductAttr[0],o=this.selectProductAttr[1],c=this.selectProductAttr[2],d=0;d<n.values.length;d++)if(0!==o.values.length)for(var v=0;v<o.values.length;v++)if(0!==c.values.length)for(var p=0;p<c.values.length;p++){var h=[];h.push({key:n.name,value:n.values[d]}),h.push({key:o.name,value:o.values[v]}),h.push({key:c.name,value:c.values[p]}),t.push({spData:f()(h)})}else{var m=[];m.push({key:n.name,value:n.values[d]}),m.push({key:o.name,value:o.values[v]}),t.push({spData:f()(m)})}else t.push({spData:f()([{key:n.name,value:n.values[d]}])})},refreshProductAttrPics:function(){if(this.selectProductAttrPics=[],this.selectProductAttr.length>=1)for(var t=this.selectProductAttr[0].values,e=0;e<t.length;e++){var a=null;this.isEdit&&(a=this.getProductSkuPic(t[e])),this.selectProductAttrPics.push({name:t[e],pic:a})}},getProductSkuPic:function(t){for(var e=0;e<this.value.skuStockList.length;e++){if(t===JSON.parse(this.value.skuStockList[e].spData)[0].value)return this.value.skuStockList[e].pic}return null},mergeProductAttrValue:function(){this.value.productAttributeValueList=[];for(var t=0;t<this.selectProductAttr.length;t++){var e=this.selectProductAttr[t];1===e.handAddStatus&&null!=e.options&&e.options.length>0&&this.value.productAttributeValueList.push({productAttributeId:e.id,value:this.getOptionStr(e.options)})}for(var a=0;a<this.selectProductParam.length;a++){var l=this.selectProductParam[a];this.value.productAttributeValueList.push({productAttributeId:l.id,value:l.value})}for(var i=0;i<this.extList.length;i++)for(var r=this.extList[i].list,s=0;s<r.length;s++){var u=r[s];this.value.productAttributeValueList.push({productAttributeId:u.id,value:u.value})}},mergeProductAttrPics:function(){for(var t=0;t<this.selectProductAttrPics.length;t++)for(var e=0;e<this.value.skuStockList.length;e++){JSON.parse(this.value.skuStockList[e].spData)[0].value===this.selectProductAttrPics[t].name&&(this.value.skuStockList[e].pic=this.selectProductAttrPics[t].pic)}},getOptionStr:function(t){for(var e="",a=0;a<t.length;a++)e+=t[a],a!=t.length-1&&(e+=",");return e},handleRemoveProductSku:function(t,e){var a=this.value.skuStockList;1===a.length?a.pop():a.splice(t,1)},getParamInputList:function(t){return t.split(",")},handlePrev:function(){this.$emit("prevStep")},handleNext:function(){this.mergeProductAttrValue(),this.mergeProductAttrPics(),this.$emit("nextStep")}}},A={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-top":"50px"}},[a("el-form",{ref:"productAttrForm",staticClass:"form-inner-container",attrs:{model:t.value,"label-width":"120px",size:"small"}},[a("el-form-item",{attrs:{label:"属性类型："}},[a("el-select",{attrs:{placeholder:"请选择属性类型"},on:{change:t.handleProductAttrChange},model:{value:t.value.productAttributeCategoryId,callback:function(e){t.$set(t.value,"productAttributeCategoryId",e)},expression:"value.productAttributeCategoryId"}},t._l(t.productAttributeCategoryOptions,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"商品规格："}},[a("el-card",{staticClass:"cardBg",attrs:{shadow:"never"}},t._l(t.selectProductAttr,function(e,l){return a("div",[t._v("\n          "+t._s(e.name)+"：\n          "),0===e.handAddStatus?a("el-checkbox-group",{model:{value:t.selectProductAttr[l].values,callback:function(e){t.$set(t.selectProductAttr[l],"values",e)},expression:"selectProductAttr[idx].values"}},t._l(t.getInputListArr(e.inputList),function(t){return a("el-checkbox",{key:t,staticClass:"littleMarginLeft",attrs:{label:t}})}),1):a("div",[a("el-checkbox-group",{model:{value:t.selectProductAttr[l].values,callback:function(e){t.$set(t.selectProductAttr[l],"values",e)},expression:"selectProductAttr[idx].values"}},t._l(t.selectProductAttr[l].options,function(e,i){return a("div",{staticClass:"littleMarginLeft",staticStyle:{display:"inline-block"}},[a("el-checkbox",{key:e,attrs:{label:e}}),t._v(" "),a("el-button",{staticClass:"littleMarginLeft",attrs:{type:"text"},on:{click:function(e){return t.handleRemoveProductAttrValue(l,i)}}},[t._v("删除\n                ")])],1)}),0),t._v(" "),a("el-input",{staticStyle:{width:"160px","margin-left":"10px"},attrs:{clearable:""},model:{value:t.addProductAttrValue,callback:function(e){t.addProductAttrValue=e},expression:"addProductAttrValue"}}),t._v(" "),a("el-button",{staticClass:"littleMarginLeft",on:{click:function(e){return t.handleAddProductAttrValue(l)}}},[t._v("增加")])],1)],1)}),0),t._v(" "),a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.value.skuStockList,border:""}},[t._l(t.selectProductAttr,function(e,l){return a("el-table-column",{key:e.id,attrs:{label:e.name,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.getProductSkuSp(e.row,l))+"\n          ")]}}],null,!0)})}),t._v(" "),a("el-table-column",{attrs:{label:"销售价格",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.price,callback:function(a){t.$set(e.row,"price",a)},expression:"scope.row.price"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"促销价格",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.promotionPrice,callback:function(a){t.$set(e.row,"promotionPrice",a)},expression:"scope.row.promotionPrice"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品库存",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.stock,callback:function(a){t.$set(e.row,"stock",a)},expression:"scope.row.stock"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"库存预警值",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.lowStock,callback:function(a){t.$set(e.row,"lowStock",a)},expression:"scope.row.lowStock"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"SKU编号",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{model:{value:e.row.skuCode,callback:function(a){t.$set(e.row,"skuCode",a)},expression:"scope.row.skuCode"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.handleRemoveProductSku(e.$index,e.row)}}},[t._v("删除\n            ")])]}}])})],2),t._v(" "),a("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary"},on:{click:t.handleRefreshProductSkuList}},[t._v("刷新列表\n      ")]),t._v(" "),a("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary"},on:{click:t.handleSyncProductSkuPrice}},[t._v("同步价格\n      ")]),t._v(" "),a("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary"},on:{click:t.handleSyncProductSkuStock}},[t._v("同步库存\n      ")])],1),t._v(" "),t.hasAttrPic?a("el-form-item",{attrs:{label:"属性图片："}},[a("el-card",{staticClass:"cardBg",attrs:{shadow:"never"}},t._l(t.selectProductAttrPics,function(e,l){return a("div",[a("span",[t._v(t._s(e.name)+":")]),t._v(" "),a("single-upload",{staticStyle:{width:"300px",display:"inline-block","margin-left":"10px"},model:{value:e.pic,callback:function(a){t.$set(e,"pic",a)},expression:"item.pic"}})],1)}),0)],1):t._e(),t._v(" "),a("el-form-item",{attrs:{label:"商品参数："}},[a("el-card",{staticClass:"cardBg",attrs:{shadow:"never"}},t._l(t.selectProductParam,function(e,l){return a("div",{class:{littleMarginTop:0!==l}},[a("div",{staticClass:"paramInputLabel"},[t._v(t._s(e.name)+":")]),t._v(" "),1===e.inputType?a("el-select",{staticClass:"paramInput",model:{value:t.selectProductParam[l].value,callback:function(e){t.$set(t.selectProductParam[l],"value",e)},expression:"selectProductParam[index].value"}},t._l(t.getParamInputList(e.inputList),function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})}),1):a("el-input",{staticClass:"paramInput",model:{value:t.selectProductParam[l].value,callback:function(e){t.$set(t.selectProductParam[l],"value",e)},expression:"selectProductParam[index].value"}})],1)}),0)],1),t._v(" "),t._l(t.extList,function(e,l){return a("div",{key:l},[a("el-form-item",{attrs:{label:e.label}},[a("el-card",{staticClass:"cardBg",attrs:{shadow:"never"}},t._l(e.list,function(l,i){return a("div",[1===l.inputType?a("el-select",{staticClass:"paramInput",model:{value:e.list[i].value,callback:function(a){t.$set(e.list[i],"value",a)},expression:"extItem.list[index].value"}},t._l(t.getParamInputList(l.inputList),function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})}),1):a("el-input",{staticClass:"paramInput",model:{value:e.list[i].value,callback:function(a){t.$set(e.list[i],"value",a)},expression:"extItem.list[index].value"}})],1)}),0)],1)],1)}),t._v(" "),a("el-form-item",{attrs:{label:"商品相册："}},[a("multi-upload",{model:{value:t.selectProductPics,callback:function(e){t.selectProductPics=e},expression:"selectProductPics"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品详情："}},[a("el-tabs",{attrs:{type:"card"},model:{value:t.activeHtmlName,callback:function(e){t.activeHtmlName=e},expression:"activeHtmlName"}},[a("el-tab-pane",{attrs:{label:"电脑端详情",name:"pc"}},[a("tinymce",{attrs:{width:595,height:300},model:{value:t.value.detailHtml,callback:function(e){t.$set(t.value,"detailHtml",e)},expression:"value.detailHtml"}})],1),t._v(" "),a("el-tab-pane",{attrs:{label:"移动端详情",name:"mobile"}},[a("tinymce",{attrs:{width:595,height:300},model:{value:t.value.detailMobileHtml,callback:function(e){t.$set(t.value,"detailMobileHtml",e)},expression:"value.detailMobileHtml"}})],1)],1)],1),t._v(" "),a("el-form-item",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{size:"medium"},on:{click:t.handlePrev}},[t._v("上一步，填写商品促销")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"medium"},on:{click:t.handleNext}},[t._v("下一步，选择商品关联")])],1)],2)],1)},staticRenderFns:[]};var C=a("VU/8")(L,A,!1,function(t){a("G30i")},"data-v-2cafd707",null).exports,$=a("0QkR"),I=a("vLgD");var T={name:"ProductRelationDetail",props:{value:Object,isEdit:{type:Boolean,default:!1}},data:function(){return{subjectList:[],subjectTitles:["待选择","已选择"],prefrenceAreaList:[],prefrenceAreaTitles:["待选择","已选择"]}},computed:{selectSubject:{get:function(){var t=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return t;for(var e=0;e<this.value.subjectProductRelationList.length;e++)t.push(this.value.subjectProductRelationList[e].subjectId);return t},set:function(t){this.value.subjectProductRelationList=[];for(var e=0;e<t.length;e++)this.value.subjectProductRelationList.push({subjectId:t[e]})}},selectPrefrenceArea:{get:function(){var t=[];if(null==this.value.prefrenceAreaProductRelationList||this.value.prefrenceAreaProductRelationList.length<=0)return t;for(var e=0;e<this.value.prefrenceAreaProductRelationList.length;e++)t.push(this.value.prefrenceAreaProductRelationList[e].prefrenceAreaId);return t},set:function(t){this.value.prefrenceAreaProductRelationList=[];for(var e=0;e<t.length;e++)this.value.prefrenceAreaProductRelationList.push({prefrenceAreaId:t[e]})}}},created:function(){this.getSubjectList(),this.getPrefrenceAreaList()},methods:{filterMethod:function(t,e){return e.label.indexOf(t)>-1},getSubjectList:function(){var t=this;Object($.d)().then(function(e){for(var a=e.data,l=0;l<a.length;l++)t.subjectList.push({label:a[l].title,key:a[l].id})})},getPrefrenceAreaList:function(){var t=this;Object(I.a)({url:"/prefrenceArea/listAll",method:"get"}).then(function(e){for(var a=e.data,l=0;l<a.length;l++)t.prefrenceAreaList.push({label:a[l].name,key:a[l].id})})},handlePrev:function(){this.$emit("prevStep")},handleFinishCommit:function(){this.$emit("finishCommit",this.isEdit)}}},E={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-top":"50px"}},[a("el-form",{ref:"productRelationForm",staticClass:"form-inner-container",attrs:{model:t.value,"label-width":"120px",size:"small"}},[a("el-form-item",{attrs:{label:"关联专题："}},[a("el-transfer",{staticStyle:{display:"inline-block"},attrs:{"filter-method":t.filterMethod,titles:t.subjectTitles,data:t.subjectList,filterable:"","filter-placeholder":"请输入专题名称"},model:{value:t.selectSubject,callback:function(e){t.selectSubject=e},expression:"selectSubject"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"关联优选："}},[a("el-transfer",{staticStyle:{display:"inline-block"},attrs:{"filter-method":t.filterMethod,titles:t.prefrenceAreaTitles,data:t.prefrenceAreaList,filterable:"","filter-placeholder":"请输入优选名称"},model:{value:t.selectPrefrenceArea,callback:function(e){t.selectPrefrenceArea=e},expression:"selectPrefrenceArea"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{size:"medium"},on:{click:t.handlePrev}},[t._v("上一步，填写商品属性")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"medium"},on:{click:t.handleFinishCommit}},[t._v("完成，提交商品")])],1)],1)],1)},staticRenderFns:[]};var O={albumPics:"",brandId:null,brandName:"",deleteStatus:0,description:"",detailDesc:"",detailHtml:"",detailMobileHtml:"",detailTitle:"",feightTemplateId:0,flashPromotionCount:0,flashPromotionId:0,flashPromotionPrice:0,flashPromotionSort:0,giftPoint:0,giftGrowth:0,keywords:"",lowStock:0,name:"",newStatus:0,note:"",originalPrice:0,pic:"",memberPriceList:[],productFullReductionList:[{fullPrice:0,reducePrice:0}],productLadderList:[{count:0,discount:0,price:0}],previewStatus:0,price:0,productAttributeCategoryId:null,productAttributeValueList:[],skuStockList:[],subjectProductRelationList:[],prefrenceAreaProductRelationList:[],productCategoryId:null,productCategoryName:"",productSn:"",promotionEndTime:"",promotionPerLimit:0,promotionPrice:null,promotionStartTime:"",promotionType:0,publishStatus:0,recommandStatus:0,sale:0,serviceIds:"",sort:0,stock:0,subTitle:"",unit:"",usePointLimit:0,verifyStatus:0,weight:0},V={name:"ProductDetail",components:{ProductInfoDetail:c,ProductSaleDetail:h,ProductAttrDetail:C,ProductRelationDetail:a("VU/8")(T,E,!1,function(t){a("c/JP")},"data-v-0bb06652",null).exports},props:{isEdit:{type:Boolean,default:!1}},data:function(){return{active:0,productParam:i()({},O),showStatus:[!0,!1,!1,!1]}},created:function(){var t=this;this.isEdit&&Object(u.j)(this.$route.query.id).then(function(e){t.productParam=e.data})},methods:{hideAll:function(){for(var t=0;t<this.showStatus.length;t++)this.showStatus[t]=!1},prevStep:function(){this.active>0&&this.active<this.showStatus.length&&(this.active--,this.hideAll(),this.showStatus[this.active]=!0)},nextStep:function(){this.active<this.showStatus.length-1&&(this.active++,this.hideAll(),this.showStatus[this.active]=!0)},finishCommit:function(t){var e=this;this.$confirm("是否要提交该产品","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t?Object(u.C)(e.$route.query.id,e.productParam).then(function(t){e.$message({type:"success",message:"提交成功",duration:1e3}),e.$router.back()}):Object(u.d)(e.productParam).then(function(t){e.$message({type:"success",message:"提交成功",duration:1e3}),location.reload()})})}}},R={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-card",{staticClass:"form-container",attrs:{shadow:"never"}},[a("el-steps",{attrs:{active:t.active,"finish-status":"success","align-center":""}},[a("el-step",{attrs:{title:"填写商品信息"}}),t._v(" "),a("el-step",{attrs:{title:"填写商品促销"}}),t._v(" "),a("el-step",{attrs:{title:"填写商品属性"}}),t._v(" "),a("el-step",{attrs:{title:"选择商品关联"}})],1),t._v(" "),a("product-info-detail",{directives:[{name:"show",rawName:"v-show",value:t.showStatus[0],expression:"showStatus[0]"}],attrs:{"is-edit":t.isEdit},on:{nextStep:t.nextStep},model:{value:t.productParam,callback:function(e){t.productParam=e},expression:"productParam"}}),t._v(" "),a("product-sale-detail",{directives:[{name:"show",rawName:"v-show",value:t.showStatus[1],expression:"showStatus[1]"}],attrs:{"is-edit":t.isEdit},on:{nextStep:t.nextStep,prevStep:t.prevStep},model:{value:t.productParam,callback:function(e){t.productParam=e},expression:"productParam"}}),t._v(" "),a("product-attr-detail",{directives:[{name:"show",rawName:"v-show",value:t.showStatus[2],expression:"showStatus[2]"}],attrs:{"is-edit":t.isEdit},on:{nextStep:t.nextStep,prevStep:t.prevStep},model:{value:t.productParam,callback:function(e){t.productParam=e},expression:"productParam"}}),t._v(" "),a("product-relation-detail",{directives:[{name:"show",rawName:"v-show",value:t.showStatus[3],expression:"showStatus[3]"}],attrs:{"is-edit":t.isEdit},on:{prevStep:t.prevStep,finishCommit:t.finishCommit},model:{value:t.productParam,callback:function(e){t.productParam=e},expression:"productParam"}})],1)},staticRenderFns:[]};var N=a("VU/8")(V,R,!1,function(t){a("FN+s")},null,null);e.a=N.exports},"FN+s":function(t,e){},Fmmi:function(t,e){},G30i:function(t,e){},Kgmr:function(t,e){},N3DD:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l={name:"addProduct",components:{ProductDetail:a("CmKi").a}},i={render:function(){var t=this.$createElement;return(this._self._c||t)("product-detail",{attrs:{"is-edit":!1}})},staticRenderFns:[]};var r=a("VU/8")(l,i,!1,function(t){a("Fmmi")},null,null);e.default=r.exports},UlPQ:function(t,e){},"c/JP":function(t,e){}});