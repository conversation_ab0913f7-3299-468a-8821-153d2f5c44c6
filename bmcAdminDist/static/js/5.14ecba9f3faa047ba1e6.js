webpackJsonp([5],{"/A2/":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("fZjL"),n=a.n(i),s=a("//Fk"),r=a.n(s),o=a("c/Tr"),u=a.n(o),l=a("lHA8"),d=a.n(l),c=a("pFYg"),p=a.n(c),h=a("woOf"),v=a.n(h),f=a("KhLR"),g=a("tUEa"),m=a("mRsl"),b=a("UgCr"),L=a("0QkR"),y=a("xT6B"),_=a("TZVV"),x=a("3idm"),C=a("II7+"),S=a("0xDb"),w={pageNum:1,pageSize:20,brandId:63},A={0:"禁用",1:"启用",2:"挂起"},O=0,k={name:"Kefuguan<PERSON>",components:{SingleUpload:_.a,tedian:C.a},filters:{formatCreateTime:function(t){var e=new Date(t);return Object(y.a)(e,"YYYY-MM-DD HH:mm:ss")},verifyStatusFilter:function(t){return A[t]},verifyStatusFilter2:function(t){return t?"禁用":"启用"}},data:function(){return{metaId:"",activeName:"",extList:{ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]}},productAttributeCategoryOptions:[],detailOptions:[],opetionDate:[],subjectList:[],subjectTitles:["待选择","已选择"],prefrenceAreaList:[],prefrenceAreaTitles:["待选择","已选择"],productCategoryOptions:[],listLoading:!0,list:[],total:0,showAddModel:!1,listQuery:v()({},w),value:{name:"",gameCareinfoVx:"",gameCareinfoTime:"",pic:"",productCategoryId:"",publishStatus:"",subjectProductRelationList:[],productAttributeValueList:[],productAttributeCategoryId:""},rules:{},formLabelWidth:"140px",cateList:[]}},computed:{selectSubject:{get:function(){var t=[];if(null==this.value.subjectProductRelationList||this.value.subjectProductRelationList.length<=0)return t;for(var e=0;e<this.value.subjectProductRelationList.length;e++)t.push(this.value.subjectProductRelationList[e].subjectId);return t},set:function(t){this.value.subjectProductRelationList=[];for(var e=0;e<t.length;e++)this.value.subjectProductRelationList.push({subjectId:t[e]})}}},watch:{$route:function(t,e){var a=t.meta.id;this.metaId=a,this.fetchListCate(a)}},created:function(){var t=this.$route.meta.id;this.metaId=t,this.fetchListCate(t)},methods:{fetchListCate:function(t){var e=this;Object(m.d)(t,{pageNum:1,pageSize:999}).then(function(t){200==t.code&&(e.cateList=t.data.list||[],e.cateList.length&&(e.cateList.sort(function(t,e){return e.sort-t.sort}),e.activeName=e.cateList[0].name),e.getList(),e.getProductAttrCateList())})},changeTab:function(){this.getList()},handleDelete:function(t,e){var a=this;this.$confirm("是否要进行删除操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=[];t.push(e.id),a.productKFDelete(1,t)})},productKFDelete:function(t,e){var a=this,i=new URLSearchParams;i.append("ids",e),i.append("deleteStatus",t),Object(b.r)(i).then(function(t){a.$message({message:"删除成功",type:"success",duration:1e3})}),this.getList()},handleProductAttrChange:function(t){this.extList=this.getExtList(),this.getProductAttrList(1,t),this.getProductAttrList(2,t),this.getProductAttrList(3,t),this.getProductAttrList(4,t)},changequfu:function(t){this.value.gameAccountQufu=t},getEditAttrOptions2:function(t){return t.map(function(t){var e=1;if(1===t.inputType&&(1===t.selectType?e=2:2===t.selectType?e=3:3===t.selectType&&(e=4)),1===e)return v()({},t,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:e,iptVal:""});if(2===e)return v()({},t,{tdtype:e,value:"",is_required:0,field_type:2,inputList:t.inputList.split(",")});if(3===e){var a=[];return t.inputList.split(",").forEach(function(t){a.push({icon:"",name:t,checked:!1})}),v()({},t,{childList:a,is_required:0,field_type:2,default_word:"点击可下拉选择物品",tdtype:e,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[]})}var i=JSON.parse(t.inputList);return i.forEach(function(t){t.value=t.parent_name,t.label=t.parent_name;var e=t.childList.map(function(t){return{value:t,label:t}});t.children=e}),v()({},t,{tdtype:e,value:[],is_required:0,field_type:2,options:i})})},handleEditCreatedAttr:function(){null!=this.value.productAttributeCategoryId&&this.handleProductAttrChange(this.value.productAttributeCategoryId)},getProductAttrList:function(t,e){var a=this,i={pageNum:1,pageSize:200,type:t};Object(x.c)(e,i).then(function(e){var i=e.data.list;if(0!==t){var n="基础信息扩展";2===t?n="账号信息扩展":3===t&&(n="其他扩展");var s={index:parseInt(t,10),label:n,needShow:i&&i.length>0},r=a.getEditAttrOptions2(i),o=[];s.opetionDate=r;for(var u=0;u<i.length;u++){var l=null;a.isEdit&&(l=a.getEditParamValue2(i[u]))&&o.push(l)}s.detailOptions=o,S.b.async2opetionDate(s.detailOptions,s.opetionDate),a.$set(a.extList,"ext"+t,s)}else{a.isEdit||(a.selectProductAttr=[]);for(var d=0;d<i.length;d++){var c=[];a.isEdit&&(c=a.getEditAttrValues(d)),a.selectProductAttr.push({id:i[d].id,name:i[d].name,handAddStatus:i[d].handAddStatus,inputList:i[d].inputList,values:c,options:[]})}}})},getEditParamValue2:function(t){var e=1;1===t.inputType&&(1===t.selectType?e=2:2===t.selectType?e=3:3===t.selectType&&(e=4));for(var a=0;a<this.value.productAttributeValueList.length;a++)if(t.id===this.value.productAttributeValueList[a].productAttributeId){var i=this.value.productAttributeValueList[a];if(1===e)return v()({},t,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:e,iptVal:i.value});if(2===e)return v()({},t,{childList:[],is_required:0,field_type:2,default_word:"请输入",tdtype:e,value:i.value});if(3!==e)return v()({},t,{title:t.name,tdtype:e,value:(i.value||"").split("|"),options:JSON.parse(t.inputList)});var n=function(){var a=[];""!==i.value&&(a=i.value.split(","));var n=[];return a.forEach(function(t){n.push({icon:"",name:t,checked:!0})}),{v:v()({},t,{title:t.name,tdtype:e,value:n})}}();if("object"===(void 0===n?"undefined":p()(n)))return n.v}},getEditAttrValues:function(t){var e=new d.a;if(0===t)for(var a=0;a<this.value.skuStockList.length;a++){var i=this.value.skuStockList[a],n=JSON.parse(i.spData);null!=n&&n.length>=1&&e.add(n[0].value)}else if(1===t)for(var s=0;s<this.value.skuStockList.length;s++){var r=this.value.skuStockList[s],o=JSON.parse(r.spData);null!=o&&o.length>=2&&e.add(o[1].value)}else for(var l=0;l<this.value.skuStockList.length;l++){var c=this.value.skuStockList[l],p=JSON.parse(c.spData);null!=p&&p.length>=3&&e.add(p[2].value)}return u()(e)},getProductAttrCateList:function(){var t=this;Object(f.c)({pageNum:1,pageSize:999}).then(function(e){t.productAttributeCategoryOptions=[];for(var a=e.data.list,i=0;i<a.length;i++)t.productAttributeCategoryOptions.push({label:a[i].name,value:a[i].id})})},getSubjectList:function(){var t=this;Object(L.d)().then(function(e){t.subjectList=[];for(var a=e.data,i=0;i<a.length;i++)4===a[i].categoryId&&t.subjectList.push({label:a[i].title,key:a[i].id})})},filterMethod:function(t,e){return e.label.indexOf(t)>-1},getButtonType:function(t){return t?"danger":"success"},toggleState:function(t,e){var a=this,i=e.id,n=v()({},e);n.publishStatus=e.publishStatus?0:1,Object(b.s)(i,n).then(function(){a.getList()})},getGameCate:function(){var t=this;return new r.a(function(e,a){r.a.all([Object(m.d)(74,{pageNum:1,pageSize:999}),Object(m.d)(73,{pageNum:1,pageSize:999})]).then(function(a){var i=[];i.push({icon:"",name:"游戏",splitTitle:1,checked:!1});var n=a[0].data.list.map(function(t){return{icon:"",name:t.name,checked:!1}});i=i.concat(n);var s=a[1].data.list.map(function(t){return{icon:"",name:t.name,checked:!1}}),r={childList:i=i.concat(s),name:"关联游戏",is_required:0,field_type:2,default_word:"点击可下拉选择",tdtype:3,iptVal:"",showChild:!1,choosedList:[],zidingyiList:[],iptSearchVal:"",searchList:[],id:+new Date};t.opetionDate=[r],e()})})},getList:function(){var t=this;this.listLoading=!0,Object(m.e)().then(function(e){var a=e.data;t.productCategoryOptions=a.find(function(e){return e.id===t.metaId}).children;var i=t.cateList.find(function(e){return e.name===t.activeName});t.listQuery.productCategoryId=i.id,Object(g.d)(t.listQuery).then(function(e){t.listLoading=!1;var a=e.data.list;t.list=a,t.total=e.data.total})})},handleUpdateProduct:function(t,e){var a=this,i=e.id;this.clearValue(),this.getGameCate().then(function(t){a.getSubjectList(),Object(g.g)(i).then(function(t){if(a.value=v()({},t.data),a.id=i,a.isEdit=!0,a.showAddModel=!0,a.detailOptions=[],a.value.subTitle){var e=[];a.value.subTitle.split(",").forEach(function(t){e.push({icon:"",name:t,checked:!0})}),a.detailOptions=[{title:"关联游戏",tdtype:3,value:e,id:O++}]}else a.detailOptions=[];a.handleEditCreatedAttr()})})},createHelp:function(){var t=this;this.getGameCate().then(function(e){t.getSubjectList(),t.clearValue(),t.id="",t.isEdit=!1,t.showAddModel=!0})},getExtList:function(){return{ext1:{needShow:!1,opetionDate:[],detailOptions:[]},ext2:{needShow:!1,opetionDate:[],detailOptions:[]},ext3:{needShow:!1,opetionDate:[],detailOptions:[]},ext4:{needShow:!1,opetionDate:[],detailOptions:[]}}},clearValue:function(){this.detailOptions=[],this.extList=this.getExtList(),this.value={name:"",gameCareinfoVx:"",gameCareinfoTime:"",pic:"",productCategoryId:"",publishStatus:"",subjectProductRelationList:[],productAttributeValueList:[],productAttributeCategoryId:""}},handleSizeChange:function(t){this.listQuery.pageNum=1,this.listQuery.pageSize=t,this.getList()},handleCurrentChange:function(t){this.listQuery.pageNum=t,this.getList()},cancel:function(){this.showAddModel=!1},submitForm:function(){var t=this;this.value.brandId=63,this.value.productAttributeValueList=[];for(var e=n()(this.extList),a=0;a<e.length;a++)for(var i=e[a],s=this.extList[i].opetionDate,r=0;r<s.length;r++){var o=s[r],u=o.value||"";3===o.tdtype&&o.choosedList.length?u=o.choosedList.map(function(t){return t.name}).join(","):1===o.tdtype?u=o.iptVal:4===o.tdtype&&(u=o.value.join("|")),this.value.productAttributeValueList.push({productAttributeId:o.id,value:u,attriName:o.name,sort:o.sort,filterType:o.filterType,searchType:o.searchType,type:o.type,searchSort:o.searchSort})}this.showAddModel=!1;var l=this.opetionDate[0].choosedList.map(function(t){return t.name});this.value.subTitle=l.join(",");var d=this.productCategoryOptions.find(function(e){return t.value.productCategoryId===e.id});if(this.value.productCategoryName=d.name,this.isEdit)Object(b.s)(this.id,this.value).then(function(e){200==e.code&&(t.getList(),t.$message.success(e.message))});else{var c=v()({},this.value);Object(b.q)(c).then(function(e){200==e.code&&(t.getList(),t.$message.success(e.message))})}}}},j={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"table-container"},[a("el-button",{staticClass:"add-help",attrs:{type:"primary"},on:{click:t.createHelp}},[t._v("新建")]),t._v(" "),a("el-tabs",{staticClass:"tabs",attrs:{type:"card"},on:{"tab-click":t.changeTab},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.cateList,function(t,e){return a("el-tab-pane",{key:e,attrs:{label:t.name,name:t.name}})}),1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"productTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[a("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.id))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"名称",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.name))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"类型",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(e.row.productCategoryName))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[t._v(t._s(t._f("verifyStatusFilter")(e.row.publishStatus)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"更新时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatCreateTime")(e.row.updateTime)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("formatCreateTime")(e.row.createTime)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("p",[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return t.handleUpdateProduct(e.$index,e.row)}}},[t._v("详情/编辑\n            ")]),t._v(" "),a("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(a){return t.handleDelete(e.$index,e.row)}}},[t._v("\n              删除\n            ")])],1)]}}])})],1),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":t.listQuery.pageNum,"page-size":t.listQuery.pageSize,"page-sizes":[20,40,60],total:t.total,background:"",layout:"total, sizes,prev, pager, next,jumper"},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"pageNum",e)},"update:current-page":function(e){return t.$set(t.listQuery,"pageNum",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),t.showAddModel?a("el-dialog",{attrs:{visible:t.showAddModel,width:"80%",top:"1vh"},on:{"update:visible":function(e){t.showAddModel=e}}},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("编辑信息")])]),t._v(" "),a("el-form",{ref:"helpForm",staticClass:"form-box",attrs:{model:t.value,rules:t.rules,"label-width":t.formLabelWidth}},[a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{model:{value:t.value.productCategoryId,callback:function(e){t.$set(t.value,"productCategoryId",e)},expression:"value.productCategoryId"}},t._l(t.productCategoryOptions,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"名称"}},[a("el-input",{model:{value:t.value.name,callback:function(e){t.$set(t.value,"name",e)},expression:"value.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"启用状态"}},[a("el-radio-group",{model:{value:t.value.publishStatus,callback:function(e){t.$set(t.value,"publishStatus",e)},expression:"value.publishStatus"}},[a("el-radio",{attrs:{label:1}},[t._v("启用")]),t._v(" "),a("el-radio",{attrs:{label:0}},[t._v("禁用")]),t._v(" "),a("el-radio",{attrs:{label:2}},[t._v("挂起")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"头像"}},[a("single-upload",{model:{value:t.value.pic,callback:function(e){t.$set(t.value,"pic",e)},expression:"value.pic"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"SKU"}},[a("el-select",{on:{change:t.handleProductAttrChange},model:{value:t.value.productAttributeCategoryId,callback:function(e){t.$set(t.value,"productAttributeCategoryId",e)},expression:"value.productAttributeCategoryId"}},t._l(t.productAttributeCategoryOptions,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),t.extList.ext1.needShow?a("div",{staticClass:"ext1",attrs:{shadow:"never"}},[a("tedian",{staticClass:"tedian-box",attrs:{"detail-options":t.extList.ext1.detailOptions,"opetion-date":t.extList.ext1.opetionDate},on:{changequfu:t.changequfu}})],1):t._e(),t._v(" "),t.extList.ext2.needShow?a("div",{staticClass:"ext2",attrs:{shadow:"never"}},[a("tedian",{staticClass:"tedian-box",attrs:{"detail-options":t.extList.ext2.detailOptions,"opetion-date":t.extList.ext2.opetionDate},on:{changequfu:t.changequfu}})],1):t._e(),t._v(" "),t.extList.ext3.needShow?a("div",{staticClass:"ext3",attrs:{shadow:"never"}},[a("tedian",{staticClass:"tedian-box",attrs:{"detail-options":t.extList.ext3.detailOptions,"opetion-date":t.extList.ext3.opetionDate},on:{changequfu:t.changequfu}})],1):t._e(),t._v(" "),t.extList.ext4.needShow?a("div",{staticClass:"ext4",attrs:{shadow:"never"}},[a("tedian",{staticClass:"tedian-box",attrs:{"detail-options":t.extList.ext4.detailOptions,"opetion-date":t.extList.ext4.opetionDate},on:{changequfu:t.changequfu}})],1):t._e(),t._v(" "),a("el-form-item",{attrs:{label:""}},[a("tedian",{attrs:{needAll:!0,"detail-options":t.detailOptions,"opetion-date":t.opetionDate}})],1)],1),t._v(" "),a("div",{staticClass:"m-footer"},[a("el-button",{on:{click:t.cancel}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("helpForm")}}},[t._v("确 定")])],1)],1)],1):t._e()],1)])},staticRenderFns:[]};var T=a("VU/8")(k,j,!1,function(t){a("7F9m")},"data-v-3de01625",null);e.default=T.exports},"7F9m":function(t,e){}});