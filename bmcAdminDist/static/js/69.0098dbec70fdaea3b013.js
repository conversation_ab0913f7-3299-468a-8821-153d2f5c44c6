webpackJsonp([69],{"3HcJ":function(s,t){},KR8f:function(s,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={render:function(){var s=this,t=s.$createElement,a=s._self._c||t;return a("div",{staticClass:"home-container"},[a("div",{staticClass:"main-content"},[s._m(0),s._v(" "),a("div",{staticClass:"data-overview-section"},[s._m(1),s._v(" "),a("div",{staticClass:"overview-grid"},[a("div",{staticClass:"overview-card",on:{click:s.recyclingOrders}},[s._m(2),s._v(" "),a("span",{staticClass:"overview-label"},[s._v("回收订单")])]),s._v(" "),a("div",{staticClass:"overview-card",on:{click:s.salesOrders}},[s._m(3),s._v(" "),a("span",{staticClass:"overview-label"},[s._v("销售订单")])]),s._v(" "),s._m(4),s._v(" "),s._m(5),s._v(" "),a("div",{staticClass:"overview-card",on:{click:s.fbGoods}},[s._m(6),s._v(" "),a("span",{staticClass:"overview-label"},[s._v("商品发布")])])])]),s._v(" "),s._m(7),s._v(" "),s._m(8)]),s._v(" "),s._m(9)])},staticRenderFns:[function(){var s=this,t=s.$createElement,a=s._self._c||t;return a("div",{staticClass:"top-cards-row"},[a("div",{staticClass:"top-card purple-card"},[a("div",{staticClass:"card-header"},[a("span",{staticClass:"card-title"},[s._v("商品")]),s._v(" "),a("span",{staticClass:"card-dots"},[s._v("...")])]),s._v(" "),a("div",{staticClass:"card-body"},[a("div",{staticClass:"card-center-text"},[s._v("在售商品")])])]),s._v(" "),a("div",{staticClass:"top-card blue-card"},[a("div",{staticClass:"card-header"},[a("span",{staticClass:"card-title"},[s._v("销售台")]),s._v(" "),a("div",{staticClass:"card-dots-group"},[a("span",{staticClass:"card-dots"},[s._v("...")]),s._v(" "),a("span",{staticClass:"card-dots"},[s._v("...")]),s._v(" "),a("span",{staticClass:"card-dots"},[s._v("...")])])]),s._v(" "),a("div",{staticClass:"card-body"},[a("div",{staticClass:"card-stats-row"},[a("div",{staticClass:"stat-column"},[a("div",{staticClass:"stat-label"},[s._v("交易中")])]),s._v(" "),a("div",{staticClass:"stat-column"},[a("div",{staticClass:"stat-label"},[s._v("售后中")])]),s._v(" "),a("div",{staticClass:"stat-column"},[a("div",{staticClass:"stat-label"},[s._v("交易完成")])])])])]),s._v(" "),a("div",{staticClass:"top-card purple-card"},[a("div",{staticClass:"card-header"},[a("span",{staticClass:"card-title"},[s._v("回收台")]),s._v(" "),a("div",{staticClass:"card-dots-group"},[a("span",{staticClass:"card-dots"},[s._v("...")]),s._v(" "),a("span",{staticClass:"card-dots"},[s._v("...")]),s._v(" "),a("span",{staticClass:"card-dots"},[s._v("...")])])]),s._v(" "),a("div",{staticClass:"card-body"},[a("div",{staticClass:"card-stats-row"},[a("div",{staticClass:"stat-column"},[a("div",{staticClass:"stat-label"},[s._v("交易中")])]),s._v(" "),a("div",{staticClass:"stat-column"},[a("div",{staticClass:"stat-label"},[s._v("售后中")])]),s._v(" "),a("div",{staticClass:"stat-column"},[a("div",{staticClass:"stat-label"},[s._v("交易完成")])])])])])])},function(){var s=this.$createElement,t=this._self._c||s;return t("div",{staticClass:"section-header"},[t("div",{staticClass:"section-icon-bar"}),this._v(" "),t("span",{staticClass:"section-title"},[this._v("数据概览")])])},function(){var s=this.$createElement,t=this._self._c||s;return t("div",{staticClass:"overview-icon green-icon"},[t("span",{staticClass:"icon-text"},[this._v("♻")])])},function(){var s=this.$createElement,t=this._self._c||s;return t("div",{staticClass:"overview-icon blue-icon"},[t("span",{staticClass:"icon-text"},[this._v("📊")])])},function(){var s=this.$createElement,t=this._self._c||s;return t("div",{staticClass:"overview-card"},[t("div",{staticClass:"overview-icon red-icon"},[t("span",{staticClass:"icon-text"},[this._v("💰")])]),this._v(" "),t("span",{staticClass:"overview-label"},[this._v("日常支出")])])},function(){var s=this.$createElement,t=this._self._c||s;return t("div",{staticClass:"overview-card"},[t("div",{staticClass:"overview-icon yellow-icon"},[t("span",{staticClass:"icon-text"},[this._v("💵")])]),this._v(" "),t("span",{staticClass:"overview-label"},[this._v("日常收入")])])},function(){var s=this.$createElement,t=this._self._c||s;return t("div",{staticClass:"overview-icon purple-icon"},[t("span",{staticClass:"icon-text"},[this._v("📝")])])},function(){var s=this,t=s.$createElement,a=s._self._c||t;return a("div",{staticClass:"data-center-section"},[a("div",{staticClass:"section-header"},[a("div",{staticClass:"section-icon-bar"}),s._v(" "),a("span",{staticClass:"section-title"},[s._v("数据中心")]),s._v(" "),a("div",{staticClass:"section-actions"},[a("span",{staticClass:"refresh-link"},[s._v("刷新")])])]),s._v(" "),a("div",{staticClass:"data-center-grid"},[a("div",{staticClass:"data-item"},[a("div",{staticClass:"data-title"},[s._v("活跃用户")]),s._v(" "),a("div",{staticClass:"data-value"},[s._v("7日内活跃 ***")])]),s._v(" "),a("div",{staticClass:"data-item"},[a("div",{staticClass:"data-title"},[s._v("商品上新")]),s._v(" "),a("div",{staticClass:"data-value"},[s._v("今日 ***")]),s._v(" "),a("div",{staticClass:"data-value"},[s._v("昨日 ***")])]),s._v(" "),a("div",{staticClass:"data-item"},[a("div",{staticClass:"data-title"},[s._v("在售商品")]),s._v(" "),a("div",{staticClass:"data-value"},[s._v("*** 个")])])])])},function(){var s=this,t=s.$createElement,a=s._self._c||t;return a("div",{staticClass:"merchant-section"},[a("div",{staticClass:"section-header"},[a("div",{staticClass:"section-icon-bar"}),s._v(" "),a("span",{staticClass:"section-title"},[s._v("商家互联")])]),s._v(" "),a("div",{staticClass:"merchant-grid"},[a("div",{staticClass:"merchant-item"},[a("div",{staticClass:"merchant-icon pink-bg"},[a("span",{staticClass:"icon-text"},[s._v("🏪")])]),s._v(" "),a("div",{staticClass:"merchant-info"},[a("div",{staticClass:"merchant-title"},[s._v("今日订单总数")]),s._v(" "),a("div",{staticClass:"merchant-value"},[s._v("0")])])]),s._v(" "),a("div",{staticClass:"merchant-item"},[a("div",{staticClass:"merchant-icon blue-bg"},[a("span",{staticClass:"icon-text"},[s._v("🏙")])]),s._v(" "),a("div",{staticClass:"merchant-info"},[a("div",{staticClass:"merchant-title"},[s._v("售后处理")]),s._v(" "),a("div",{staticClass:"merchant-value"},[s._v("3")])])]),s._v(" "),a("div",{staticClass:"merchant-item"},[a("div",{staticClass:"merchant-icon blue-bg"},[a("span",{staticClass:"icon-text"},[s._v("💲")])]),s._v(" "),a("div",{staticClass:"merchant-info"},[a("div",{staticClass:"merchant-title"},[s._v("今日销售额")]),s._v(" "),a("div",{staticClass:"merchant-value"},[s._v("0")])])]),s._v(" "),a("div",{staticClass:"merchant-item"},[a("div",{staticClass:"merchant-icon orange-bg"},[a("span",{staticClass:"icon-text"},[s._v("📦")])]),s._v(" "),a("div",{staticClass:"merchant-info"},[a("div",{staticClass:"merchant-title"},[s._v("昨日销售额")]),s._v(" "),a("div",{staticClass:"merchant-value"},[s._v("17")])])])])])},function(){var s=this,t=s.$createElement,a=s._self._c||t;return a("div",{staticClass:"right-sidebar"},[a("div",{staticClass:"user-info-card"},[a("div",{staticClass:"user-avatar"},[a("div",{staticClass:"avatar-circle"},[a("span",{staticClass:"avatar-text"},[s._v("麦")])])]),s._v(" "),a("div",{staticClass:"user-details"},[a("div",{staticClass:"user-name"},[s._v("麦麦号")]),s._v(" "),a("div",{staticClass:"user-badges"},[a("span",{staticClass:"badge-item blue-badge"},[s._v("实名认证")]),s._v(" "),a("span",{staticClass:"badge-item green-badge"},[s._v("企业认证")])]),s._v(" "),a("div",{staticClass:"user-info-text"},[a("div",{staticClass:"info-line"},[a("span",{staticClass:"info-label"},[s._v("账户:")]),s._v(" "),a("span",{staticClass:"info-value"},[s._v("用户**********")])]),s._v(" "),a("div",{staticClass:"info-line"},[a("span",{staticClass:"info-label"},[s._v("手机号:")]),s._v(" "),a("span",{staticClass:"info-value"},[s._v("***********")])])])])]),s._v(" "),a("div",{staticClass:"account-section"},[a("div",{staticClass:"account-header"},[a("span",{staticClass:"account-title"},[s._v("账户信息")]),s._v(" "),a("span",{staticClass:"verified-badge"},[s._v("已实名")])]),s._v(" "),a("div",{staticClass:"account-list"},[a("div",{staticClass:"account-row"},[a("div",{staticClass:"account-icon pink-bg"},[a("span",{staticClass:"icon-text"},[s._v("👤")])]),s._v(" "),a("span",{staticClass:"account-label"},[s._v("账户资料")]),s._v(" "),a("span",{staticClass:"account-value"},[s._v("***")])]),s._v(" "),a("div",{staticClass:"account-row"},[a("div",{staticClass:"account-icon green-bg"},[a("span",{staticClass:"icon-text"},[s._v("💳")])]),s._v(" "),a("span",{staticClass:"account-label"},[s._v("账户资金")])]),s._v(" "),a("div",{staticClass:"account-row"},[a("div",{staticClass:"account-icon purple-bg"},[a("span",{staticClass:"icon-text"},[s._v("⭐")])]),s._v(" "),a("span",{staticClass:"account-label"},[s._v("实名等级")]),s._v(" "),a("span",{staticClass:"account-value"},[s._v("***")])]),s._v(" "),a("div",{staticClass:"account-row"},[a("div",{staticClass:"account-icon orange-bg"},[a("span",{staticClass:"icon-text"},[s._v("🏬")])]),s._v(" "),a("span",{staticClass:"account-label"},[s._v("店铺")]),s._v(" "),a("span",{staticClass:"account-value"},[s._v("000")]),s._v(" "),a("div",{staticClass:"account-actions"},[a("span",{staticClass:"action-button"},[s._v("联系优化")]),s._v(" "),a("span",{staticClass:"action-button"},[s._v("优化实施")])])])])]),s._v(" "),a("div",{staticClass:"special-section"},[a("div",{staticClass:"special-header"},[a("span",{staticClass:"special-title"},[s._v("特办事项")])]),s._v(" "),a("div",{staticClass:"special-grid"},[a("div",{staticClass:"special-item"},[a("div",{staticClass:"special-icon blue-bg"},[a("span",{staticClass:"icon-text"},[s._v("📦")])]),s._v(" "),a("span",{staticClass:"special-label"},[s._v("闲置商品")]),s._v(" "),a("span",{staticClass:"special-count"},[s._v("5")])]),s._v(" "),a("div",{staticClass:"special-item"},[a("div",{staticClass:"special-icon yellow-bg"},[a("span",{staticClass:"icon-text"},[s._v("⚙")])]),s._v(" "),a("span",{staticClass:"special-label"},[s._v("售后处理")]),s._v(" "),a("span",{staticClass:"special-count"},[s._v("10")])]),s._v(" "),a("div",{staticClass:"special-item"},[a("div",{staticClass:"special-icon orange-bg"},[a("span",{staticClass:"icon-text"},[s._v("📋")])]),s._v(" "),a("span",{staticClass:"special-label"},[s._v("外部订单")]),s._v(" "),a("span",{staticClass:"special-count"},[s._v("15")])]),s._v(" "),a("div",{staticClass:"special-item"},[a("div",{staticClass:"special-icon green-bg"},[a("span",{staticClass:"icon-text"},[s._v("💬")])]),s._v(" "),a("span",{staticClass:"special-label"},[s._v("咨询商品")]),s._v(" "),a("span",{staticClass:"special-count"},[s._v("20")])]),s._v(" "),a("div",{staticClass:"special-item"},[a("div",{staticClass:"special-icon blue-bg"},[a("span",{staticClass:"icon-text"},[s._v("✓")])]),s._v(" "),a("span",{staticClass:"special-label"},[s._v("保单核验")]),s._v(" "),a("span",{staticClass:"special-count"},[s._v("25")])])])]),s._v(" "),a("div",{staticClass:"announcement-section"},[a("div",{staticClass:"announcement-header"},[a("span",{staticClass:"announcement-title"},[s._v("公告通知")]),s._v(" "),a("div",{staticClass:"announcement-actions"},[a("span",{staticClass:"more-link"},[s._v("更多")])])]),s._v(" "),a("div",{staticClass:"announcement-body"},[a("div",{staticClass:"announcement-banner"},[a("div",{staticClass:"banner-content"},[a("h3",{staticClass:"banner-title"},[s._v("关于电子台同功能上线的公告")]),s._v(" "),a("button",{staticClass:"banner-button"},[s._v("立即查看")])]),s._v(" "),a("div",{staticClass:"banner-illustration"},[a("div",{staticClass:"illustration-graphic"})])]),s._v(" "),a("div",{staticClass:"announcement-news"},[a("div",{staticClass:"news-item"},[a("span",{staticClass:"news-bullet"},[s._v("•")]),s._v(" "),a("span",{staticClass:"news-text"},[s._v("推广服务与系统升级公告200字")])]),s._v(" "),a("div",{staticClass:"news-item"},[a("span",{staticClass:"news-bullet"},[s._v("•")]),s._v(" "),a("span",{staticClass:"news-text"},[s._v("C2C交易系统上线")])])])])])])}]};var c=a("VU/8")({name:"Home",data:function(){return{}},mounted:function(){},methods:{fbGoods:function(){this.$router.push("/BMC-DDZX/BMC-SPGL-FBSP")},recyclingOrders:function(){this.$router.push("/BMC-HSZX/BMC-HSZX-HSDD")},salesOrders:function(){this.$router.push("/BMC-DDZX/BMC-XSDD")}}},i,!1,function(s){a("3HcJ")},"data-v-650ca13c",null);t.default=c.exports}});