webpackJsonp([38],{"+H76":function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=e("6ccA"),i=e.n(n),s={name:"wrongPage",data:function(){return{img_404:i.a}},methods:{handleGoMain:function(){this.$router.push({path:"/"})}}},c={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("div",{staticClass:"app-container"},[e("el-col",{attrs:{span:12}},[e("img",{staticClass:"img-style",attrs:{src:t.img_404,alt:"404"}})]),t._v(" "),e("el-col",{attrs:{span:12}},[e("div",{staticStyle:{"margin-left":"100px","margin-top":"60px"}},[e("h1",{staticClass:"color-main"},[t._v("OOPS!")]),t._v(" "),e("h2",{staticStyle:{color:"#606266"}},[t._v("很抱歉，页面它不小心迷路了！")]),t._v(" "),e("div",{staticStyle:{color:"#909399","font-size":"14px"}},[t._v("请检查您输入的网址是否正确，请点击以下按钮返回主页或者发送错误报告")]),t._v(" "),e("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary",round:""},on:{click:t.handleGoMain}},[t._v("返回首页")])],1)])],1)])},staticRenderFns:[]};var r=e("VU/8")(s,c,!1,function(t){e("4pWZ")},"data-v-36ebfeda",null);a.default=r.exports},"4pWZ":function(t,a){},"6ccA":function(t,a,e){t.exports=e.p+"static/img/gif_404.6b8ae1d.gif"}});