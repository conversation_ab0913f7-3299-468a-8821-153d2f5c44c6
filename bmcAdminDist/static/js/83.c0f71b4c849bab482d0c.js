webpackJsonp([83],{khiG:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n("woOf"),s=n.n(a),i=n("xT6B"),l=n("vLgD");var r={pageNum:1,pageSize:20},o={name:null,sort:0,status:1,createTime:null},u={name:"returnReasonList",data:function(){return{list:null,total:null,multipleSelection:[],listLoading:!0,listQuery:s()({},r),operateType:null,operateOptions:[{label:"删除",value:1}],dialogVisible:!1,returnReason:s()({},o),operateReasonId:null}},created:function(){this.getList()},filters:{formatCreateTime:function(e){var t=new Date(e);return Object(i.a)(t,"YYYY-MM-DD HH:mm:ss")}},methods:{handleAdd:function(){this.dialogVisible=!0,this.operateReasonId=null,this.returnReason=s()({},o)},handleConfirm:function(){var e,t=this;null==this.operateReasonId?(e=this.returnReason,Object(l.a)({url:"/returnReason/create",method:"post",data:e})).then(function(e){t.dialogVisible=!1,t.operateReasonId=null,t.$message({message:"添加成功！",type:"success",duration:1e3}),t.getList()}):function(e,t){return Object(l.a)({url:"/returnReason/update/"+e,method:"post",data:t})}(this.operateReasonId,this.returnReason).then(function(e){t.dialogVisible=!1,t.operateReasonId=null,t.$message({message:"修改成功！",type:"success",duration:1e3}),t.getList()})},handleUpdate:function(e,t){var n,a=this;this.dialogVisible=!0,this.operateReasonId=t.id,(n=t.id,Object(l.a)({url:"/returnReason/"+n,method:"get"})).then(function(e){a.returnReason=e.data})},handleDelete:function(e,t){var n=[];n.push(t.id),this.deleteReason(n)},handleSelectionChange:function(e){this.multipleSelection=e},handleStatusChange:function(e,t){var n=this,a=[];a.push(t.id);var s,i=new URLSearchParams;i.append("status",t.status),i.append("ids",a),(s=i,Object(l.a)({url:"/returnReason/update/status",method:"post",params:s})).then(function(e){n.$message({message:"状态修改成功",type:"success"})})},handleBatchOperate:function(){if(null==this.multipleSelection||this.multipleSelection.length<1)this.$message({message:"请选择要操作的条目",type:"warning",duration:1e3});else if(1===this.operateType){for(var e=[],t=0;t<this.multipleSelection.length;t++)e.push(this.multipleSelection[t].id);this.deleteReason(e)}},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},getList:function(){var e,t=this;this.listLoading=!0,(e=this.listQuery,Object(l.a)({url:"/returnReason/list",method:"get",params:e})).then(function(e){t.listLoading=!1,t.list=e.data.list,t.total=e.data.total})},deleteReason:function(e){var t=this;this.$confirm("是否要进行该删除操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var n=new URLSearchParams;n.append("ids",e),function(e){return Object(l.a)({url:"/returnReason/delete",method:"post",params:e})}(n).then(function(e){t.$message({message:"删除成功！",type:"success",duration:1e3}),t.listQuery.pageNum=1,t.getList()})})}}},c={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[n("i",{staticClass:"el-icon-tickets"}),e._v(" "),n("span",[e._v("数据列表")]),e._v(" "),n("el-button",{staticClass:"btn-add",attrs:{size:"mini"},on:{click:e.handleAdd}},[e._v("添加\n    ")])],1),e._v(" "),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"returnReasonTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{label:"编号",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"原因类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"排序",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.sort))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"是否可用",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(n){return e.handleStatusChange(t.$index,t.row)}},model:{value:t.row.status,callback:function(n){e.$set(t.row,"status",n)},expression:"scope.row.status"}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"添加时间",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatCreateTime")(t.row.createTime)))]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{size:"mini"},on:{click:function(n){return e.handleUpdate(t.$index,t.row)}}},[e._v("编辑")]),e._v(" "),n("el-button",{attrs:{size:"mini"},on:{click:function(n){return e.handleDelete(t.$index,t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)}}})],1),e._v(" "),n("el-dialog",{attrs:{title:"添加退货原因",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("el-form",{ref:"reasonForm",attrs:{model:e.returnReason,"label-width":"150px"}},[n("el-form-item",{attrs:{label:"原因类型："}},[n("el-input",{staticClass:"input-width",model:{value:e.returnReason.name,callback:function(t){e.$set(e.returnReason,"name",t)},expression:"returnReason.name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"排序："}},[n("el-input",{staticClass:"input-width",model:{value:e.returnReason.sort,callback:function(t){e.$set(e.returnReason,"sort",t)},expression:"returnReason.sort"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"是否启用："}},[n("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.returnReason.status,callback:function(t){e.$set(e.returnReason,"status",t)},expression:"returnReason.status"}})],1)],1),e._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var d=n("VU/8")(u,c,!1,function(e){n("ktEA")},"data-v-49118f5c",null);t.default=d.exports},ktEA:function(e,t){}});