webpackJsonp([43],{"L1/C":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("woOf"),i=a.n(l),r=a("xJrl"),n=a("Og03"),s=a("xT6B"),o={pageNum:1,pageSize:20,nameKeyword:null,urlKeyword:null,categoryId:null},u={id:null,name:null,url:null,categoryId:null,description:""},c={name:"resourceList",data:function(){return{listQuery:i()({},o),list:null,total:null,listLoading:!1,dialogVisible:!1,resource:i()({},u),isEdit:!1,categoryOptions:[],defaultCategoryId:null}},created:function(){this.getList(),this.getCateList()},filters:{formatDateTime:function(e){if(null==e||""===e)return"N/A";var t=new Date(e);return Object(s.a)(t,"YYYY-MM-DD HH:mm:ss")}},methods:{handleResetSearch:function(){this.listQuery=i()({},o)},handleSearchList:function(){this.listQuery.pageNum=1,this.getList()},handleSizeChange:function(e){this.listQuery.pageNum=1,this.listQuery.pageSize=e,this.getList()},handleCurrentChange:function(e){this.listQuery.pageNum=e,this.getList()},handleAdd:function(){this.dialogVisible=!0,this.isEdit=!1,this.resource=i()({},u),this.resource.categoryId=this.defaultCategoryId},handleDelete:function(e,t){var a=this;this.$confirm("是否要删除该资源?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(r.b)(t.id).then(function(e){a.$message({type:"success",message:"删除成功!"}),a.getList()})})},handleUpdate:function(e,t){this.dialogVisible=!0,this.isEdit=!0,this.resource=i()({},t)},handleDialogConfirm:function(){var e=this;this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.isEdit?Object(r.e)(e.resource.id,e.resource).then(function(t){e.$message({message:"修改成功！",type:"success"}),e.dialogVisible=!1,e.getList()}):Object(r.a)(e.resource).then(function(t){e.$message({message:"添加成功！",type:"success"}),e.dialogVisible=!1,e.getList()})})},handleShowCategory:function(){this.$router.push({path:"/ums/resourceCategory"})},getList:function(){var e=this;this.listLoading=!0,Object(r.d)(this.listQuery).then(function(t){e.listLoading=!1,e.list=t.data.list,e.total=t.data.total})},getCateList:function(){var e=this;Object(n.c)().then(function(t){for(var a=t.data,l=0;l<a.length;l++){var i=a[l];e.categoryOptions.push({label:i.name,value:i.id})}e.defaultCategoryId=a[0].id})}}},d={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{attrs:{inline:!0,model:e.listQuery,size:"small","label-width":"140px"}},[a("el-form-item",{attrs:{label:"资源名称："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"资源名称",clearable:""},model:{value:e.listQuery.nameKeyword,callback:function(t){e.$set(e.listQuery,"nameKeyword",t)},expression:"listQuery.nameKeyword"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"资源路径："}},[a("el-input",{staticClass:"input-width",attrs:{placeholder:"资源路径",clearable:""},model:{value:e.listQuery.urlKeyword,callback:function(t){e.$set(e.listQuery,"urlKeyword",t)},expression:"listQuery.urlKeyword"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"资源分类："}},[a("el-select",{staticClass:"input-width",attrs:{placeholder:"全部",clearable:""},model:{value:e.listQuery.categoryId,callback:function(t){e.$set(e.listQuery,"categoryId",t)},expression:"listQuery.categoryId"}},e._l(e.categoryOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSearchList()}}},[e._v("\n            查询搜索\n          ")]),e._v(" "),a("el-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{size:"small"},on:{click:function(t){return e.handleResetSearch()}}},[e._v("\n            重置\n          ")])],1)],1)],1)]),e._v(" "),a("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "),a("span",[e._v("数据列表")]),e._v(" "),a("el-button",{staticClass:"btn-add",staticStyle:{"margin-left":"20px"},attrs:{size:"mini"},on:{click:function(t){return e.handleAdd()}}},[e._v("添加")]),e._v(" "),a("el-button",{staticClass:"btn-add",attrs:{size:"mini"},on:{click:function(t){return e.handleShowCategory()}}},[e._v("资源分类")])],1),e._v(" "),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"resourceTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:""}},[a("el-table-column",{attrs:{label:"编号",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"资源名称",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"资源路径",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.url))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"描述",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.description))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"添加时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatDateTime")(t.row.createTime)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.$index,t.row)}}},[e._v("\n            编辑\n          ")]),e._v(" "),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDelete(t.$index,t.row)}}},[e._v("删除\n          ")])]}}])})],1)],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{background:"",layout:"total, sizes,prev, pager, next,jumper","current-page":e.listQuery.pageNum,"page-size":e.listQuery.pageSize,"page-sizes":[20,40,60],total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"pageNum",t)},"update:current-page":function(t){return e.$set(e.listQuery,"pageNum",t)}}})],1),e._v(" "),a("el-dialog",{attrs:{title:e.isEdit?"编辑资源":"添加资源",visible:e.dialogVisible,width:"40%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"resourceForm",attrs:{model:e.resource,"label-width":"150px",size:"small"}},[a("el-form-item",{attrs:{label:"资源名称："}},[a("el-input",{staticStyle:{width:"250px"},model:{value:e.resource.name,callback:function(t){e.$set(e.resource,"name",t)},expression:"resource.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"资源路径："}},[a("el-input",{staticStyle:{width:"250px"},model:{value:e.resource.url,callback:function(t){e.$set(e.resource,"url",t)},expression:"resource.url"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"资源分类："}},[a("el-select",{staticStyle:{width:"250px"},attrs:{placeholder:"全部",clearable:""},model:{value:e.resource.categoryId,callback:function(t){e.$set(e.resource,"categoryId",t)},expression:"resource.categoryId"}},e._l(e.categoryOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"描述："}},[a("el-input",{staticStyle:{width:"250px"},attrs:{type:"textarea",rows:5},model:{value:e.resource.description,callback:function(t){e.$set(e.resource,"description",t)},expression:"resource.description"}})],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleDialogConfirm()}}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var f=a("VU/8")(c,d,!1,function(e){a("Nom4")},null,null);t.default=f.exports},Nom4:function(e,t){},xJrl:function(e,t,a){"use strict";t.d=function(e){return Object(l.a)({url:"/resource/list",method:"get",params:e})},t.a=function(e){return Object(l.a)({url:"/resource/create",method:"post",data:e})},t.e=function(e,t){return Object(l.a)({url:"/resource/update/"+e,method:"post",data:t})},t.b=function(e){return Object(l.a)({url:"/resource/delete/"+e,method:"post"})},t.c=function(){return Object(l.a)({url:"/resource/listAll",method:"get"})};var l=a("vLgD")}});