webpackJsonp([62],{"3P+S":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("fZjL"),l=n.n(a),i=n("woOf"),s=n.n(i),o=(n("M4fF"),n("TZVV")),r=n("NS8l"),c=n("STSY"),u=n("xT6B"),d=n("Cmve"),f=n("mRsl"),h=n("TIfe"),v=n("5rT4"),m=n("mtWM"),g=n.n(m),b={productCategoryId:"",createTime:""},p={name:"",phoneNumber:"",type:3},y={components:{SingleUpload:o.a},filters:{formatDateTime:function(t){if(null==t||""===t)return"N/A";var e=new Date(t);return Object(u.a)(e,"YYYY-MM-DD HH:mm:ss")}},data:function(){return{statusRow:{},areas:d.a,listQuery:s()({},b),list:null,total:null,listLoading:!1,dialogVisible:!1,value:s()({},p),isEdit:!1,allocDialogVisible:!1,allocRoleIds:[],allRoleList:[],allocAdminId:null,listCategory:[]}},created:function(){this.getCate(),this.getList()},methods:{objectToQueryString:function(t){return l()(t).filter(function(e){return null!=t[e]&&""!==t[e]}).map(function(e){return encodeURIComponent(e)+"="+encodeURIComponent(t[e])}).join("&")},handleExport:function(){var t=this.objectToQueryString(this.listQuery);g()({method:"get",url:"https://api2.kkzhw.com/mall-bmc/product/verify/statsExport?"+t,headers:{Authorization:Object(h.a)()},responseType:"blob"}).then(function(t){var e=window.URL.createObjectURL(new Blob([t.data])),n=document.createElement("a");n.href=e,n.setAttribute("download","filename.xlsx"),document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(e),document.body.removeChild(n)}).catch(function(t){console.error("There was an error downloading the file:",t)})},getCate:function(){var t=this;Object(f.d)(74,{pageNum:1,pageSize:999}).then(function(e){200==e.code&&(t.listCategory=e.data.list)})},getState:function(t){switch(t){case 0:return"审核中";case 1:return"拒绝";case 2:return"通过";default:return"待提交"}},handleAllocDialogConfirm:function(){var t=this,e={status:this.statusRow.status,type:3,desc:this.statusRow.desc||void 0};Object(r.b)(this.statusRow.id,e).then(function(e){t.$message({message:"提交成功！",type:"success"}),t.allocDialogVisible=!1,t.getList()})},handleSelectRole:function(t,e){var n=this;Object(r.d)(e.id).then(function(t){n.value=s()({},t.data),n.allocDialogVisible=!0})},handleResetSearch:function(){this.listQuery=s()({},b)},handleSearchList:function(){this.getList()},handleSizeChange:function(t){this.getList()},handleCurrentChange:function(t){this.getList()},handleAdd:function(){this.dialogVisible=!0,this.isEdit=!1,this.value=s()({},p)},handleUpdate:function(t,e){var n=this;Object(r.d)(e.id).then(function(t){n.dialogVisible=!0,n.isEdit=!0,n.value=s()({},t.data)})},handleDialogConfirm2:function(){var t=this;this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(r.c)(t.value.id,t.value).then(function(e){t.$message({message:"修改成功！",type:"success"}),t.allocDialogVisible=!1,t.getList()})})},handleDialogConfirm:function(){var t=this;this.$confirm("是否要确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.isEdit?Object(r.c)(t.value.id,t.value).then(function(e){t.$message({message:"修改成功！",type:"success"}),t.dialogVisible=!1,t.getList()}):Object(r.a)(t.value).then(function(e){t.$message({message:"添加成功！",type:"success"}),t.dialogVisible=!1,t.getList()})})},getList:function(){var t=this;this.listLoading=!0,Object(v._3)(this.listQuery).then(function(e){t.listLoading=!1,t.list=e.data})},getAllRoleList:function(){var t=this;Object(c.e)().then(function(e){t.allRoleList=e.data})}}},_={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[n("div",{staticStyle:{"margin-top":"15px"}},[n("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"140px"}},[n("el-form-item",{attrs:{label:"游戏分类："}},[n("el-select",{staticStyle:{width:"248px"},model:{value:t.listQuery.productCategoryId,callback:function(e){t.$set(t.listQuery,"productCategoryId",e)},expression:"listQuery.productCategoryId"}},t._l(t.listCategory,function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"选择日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:t.listQuery.createTime,callback:function(e){t.$set(t.listQuery,"createTime",e)},expression:"listQuery.createTime"}})],1),t._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSearchList()}}},[t._v("\n            查询搜索\n          ")]),t._v(" "),n("el-button",{attrs:{size:"small"},on:{click:function(e){return t.handleResetSearch()}}},[t._v("\n            重置\n          ")])],1)],1)],1)]),t._v(" "),n("el-card",{staticClass:"operate-container",attrs:{shadow:"never"}},[n("i",{staticClass:"el-icon-tickets"}),t._v(" "),n("span",[t._v("数据列表")]),t._v(" "),n("el-button",{staticClass:"btn-add",staticStyle:{"margin-left":"20px"},attrs:{size:"mini"},on:{click:t.handleExport}},[t._v("导出")])],1),t._v(" "),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"valueTable",staticStyle:{width:"100%"},attrs:{data:t.list,border:""}},[n("el-table-column",{attrs:{type:"index",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{label:"人员",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.verifyMan))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当日录号数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.recordTaskCount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当日录入账号数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.verifyNewCount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当日更新账号数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.verifyUpdateCount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当日拒绝账号数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.verifyRejectCount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当月录号数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.recordTaskMonthCount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当月录入账号数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.verifyNewMonthCount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当月更新账号数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.verifyUpdateMonthCount))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当月拒绝账号数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.verifyRejectMonthCount))]}}])})],1)],1)],1)},staticRenderFns:[]};var w=n("VU/8")(y,_,!1,function(t){n("rtT5")},null,null);e.default=w.exports},rtT5:function(t,e){}});